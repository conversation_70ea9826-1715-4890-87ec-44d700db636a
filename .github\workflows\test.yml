name: ModelBolt博客系统测试

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  # 后端测试任务
  backend-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: 设置JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'adopt'
          cache: maven
      
      - name: 安装Redis
        uses: supercharge/redis-github-action@1.5.0
        with:
          redis-version: 7
      
      - name: 运行Maven测试
        run: |
          cd blog-server
          mvn test
      
      - name: 生成测试覆盖率报告
        run: |
          cd blog-server
          mvn jacoco:report
      
      - name: 上传测试覆盖率报告
        uses: actions/upload-artifact@v3
        with:
          name: backend-coverage-report
          path: blog-server/target/site/jacoco/
  
  # 前台测试任务
  frontend-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: 设置Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: 安装pnpm
        run: npm install -g pnpm
      
      - name: 安装依赖
        run: |
          cd blog-frontend
          pnpm install
      
      - name: 运行测试
        run: |
          cd blog-frontend
          pnpm test
      
      - name: 生成测试覆盖率报告
        run: |
          cd blog-frontend
          pnpm test:coverage
      
      - name: 上传测试覆盖率报告
        uses: actions/upload-artifact@v3
        with:
          name: frontend-coverage-report
          path: blog-frontend/coverage/
  
  # 管理后台测试任务
  admin-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: 设置Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: 安装pnpm
        run: npm install -g pnpm
      
      - name: 安装依赖
        run: |
          cd blog-admin
          pnpm install
      
      - name: 运行测试
        run: |
          cd blog-admin
          pnpm test
      
      - name: 生成测试覆盖率报告
        run: |
          cd blog-admin
          pnpm test:coverage
      
      - name: 上传测试覆盖率报告
        uses: actions/upload-artifact@v3
        with:
          name: admin-coverage-report
          path: blog-admin/coverage/
  
  # 汇总测试结果
  test-summary:
    needs: [backend-test, frontend-test, admin-test]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: 下载所有测试报告
        uses: actions/download-artifact@v3
        with:
          path: coverage-reports
      
      - name: 生成汇总报告
        run: |
          echo "# ModelBolt博客系统测试报告" > test-summary.md
          echo "## 测试执行时间: $(date)" >> test-summary.md
          echo "## 测试结果" >> test-summary.md
          echo "- 后端测试: ✅ 通过" >> test-summary.md
          echo "- 前台测试: ✅ 通过" >> test-summary.md
          echo "- 管理后台测试: ✅ 通过" >> test-summary.md
          
          echo "## 覆盖率报告" >> test-summary.md
          echo "- [后端覆盖率报告](./coverage-reports/backend-coverage-report/index.html)" >> test-summary.md
          echo "- [前台覆盖率报告](./coverage-reports/frontend-coverage-report/index.html)" >> test-summary.md
          echo "- [管理后台覆盖率报告](./coverage-reports/admin-coverage-report/index.html)" >> test-summary.md
      
      - name: 上传汇总报告
        uses: actions/upload-artifact@v3
        with:
          name: test-summary
          path: test-summary.md 