package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.reward.RewardConfigDTO;
import com.modelbolt.blog.model.dto.reward.RewardRecordDTO;
import com.modelbolt.blog.model.entity.ArticleReward;
import com.modelbolt.blog.service.RewardService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@RestController
@RequestMapping("/rewards")
@RequiredArgsConstructor
public class RewardController {

    private final RewardService rewardService;

    // 获取用户的打赏配置
    @GetMapping("/config/{userId}")
    public ApiResponse<RewardConfigDTO> getRewardConfig(@PathVariable Long userId) {
        return ApiResponse.success(rewardService.getRewardConfig(userId));
    }

    // 更新用户的打赏配置
    @PutMapping("/config")
    public ApiResponse<Void> updateRewardConfig(@RequestBody RewardConfigDTO config) {
        rewardService.updateRewardConfig(config);
        return ApiResponse.success();
    }

    // 创建文章打赏记录
    @PostMapping("/articles/{articleId}")
    public ApiResponse<ArticleReward> createReward(
            @PathVariable Long articleId,
            @RequestParam Long userId,
            @RequestParam BigDecimal amount,
            @RequestParam String paymentType,
            @RequestParam(required = false) String message
    ) {
        return ApiResponse.success(rewardService.createReward(articleId, userId, amount, paymentType, message));
    }

    // 获取文章的打赏记录
    @GetMapping("/articles/{articleId}/records")
    public ApiResponse<PageResult<RewardRecordDTO>> getRewardRecords(
            @PathVariable Long articleId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size
    ) {
        return ApiResponse.success(rewardService.getRewardRecords(articleId, page, size));
    }

    // 获取文章的总打赏金额
    @GetMapping("/articles/{articleId}/total")
    public ApiResponse<BigDecimal> getTotalRewardAmount(@PathVariable Long articleId) {
        return ApiResponse.success(rewardService.getTotalRewardAmount(articleId));
    }

    // 更新打赏记录的支付状态
    @PutMapping("/{rewardId}/status")
    public ApiResponse<Void> updatePaymentStatus(
            @PathVariable Long rewardId,
            @RequestParam String status,
            @RequestParam String transactionId
    ) {
        rewardService.updatePaymentStatus(rewardId, status, transactionId);
        return ApiResponse.success();
    }
}
