package com.modelbolt.blog.exception.business;

public class PasswordException extends BusinessException {
    
    private PasswordException(int code, String message) {
        super(code, message);
    }
    
    public static class PasswordLocked extends PasswordException {
        public PasswordLocked(int lockMinutes) {
            super(401, String.format("账号已被锁定，请%d分钟后重试", lockMinutes));
        }
    }
    
    public static class WeakPassword extends PasswordException {
        public WeakPassword() {
            super(400, "密码强度不够，请包含大小写字母、数字和特殊字符");
        }
    }
    
    public static class PasswordNotMatch extends PasswordException {
        public PasswordNotMatch() {
            super(401, "密码错误");
        }
    }
    
    public static class PasswordExpired extends PasswordException {
        public PasswordExpired() {
            super(401, "密码已过期，请修改密码");
        }
    }
} 