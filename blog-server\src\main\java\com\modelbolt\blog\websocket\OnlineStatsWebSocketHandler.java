package com.modelbolt.blog.websocket;

import com.alibaba.fastjson2.JSON;
import com.modelbolt.blog.model.dto.dashboard.RealTimeStatsDTO;
import com.modelbolt.blog.service.DashboardService;
import com.modelbolt.blog.service.UserOnlineService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 在线统计WebSocket处理器
 * 负责WebSocket连接管理和实时数据推送
 * 用户在线状态由UserOnlineService统一管理
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OnlineStatsWebSocketHandler extends TextWebSocketHandler {

    private final DashboardService dashboardService;
    private final UserOnlineService userOnlineService;
    
    // 存储所有WebSocket会话
    private final ConcurrentHashMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    // 缓存最近的统计数据，避免频繁计算
    private RealTimeStatsDTO cachedStats = null;
    private long lastStatsUpdate = 0;
    private static final long STATS_CACHE_TTL = 3000; // 3秒缓存有效期

    /**
     * WebSocket连接建立时的处理
     * 保存会话信息并立即发送一次最新统计数据
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        String sessionId = session.getId();
        Long userId = (Long) session.getAttributes().get("userId");
        
        sessions.put(sessionId, session);
        log.info("WebSocket连接已建立 - 会话ID: {}, 用户ID: {}, 当前总连接数: {}", 
                sessionId, userId, sessions.size());
        
        // 立即推送一次最新统计数据给新连接的客户端
        try {
            sendStatsToSession(session);
        } catch (Exception e) {
            log.error("Failed to send initial stats to new session: {}", sessionId, e);
        }
    }

    /**
     * WebSocket连接关闭时的处理
     * 清理会话信息
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        String sessionId = session.getId();
        Long userId = (Long) session.getAttributes().get("userId");
        
        sessions.remove(sessionId);
        log.info("WebSocket连接已关闭 - 会话ID: {}, 用户ID: {}, 状态: {}, 剩余连接数: {}", 
                sessionId, userId, status, sessions.size());
    }

    /**
     * WebSocket连接错误处理
     */
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        log.error("WebSocket传输错误 - 会话ID: {}, 错误信息: {}", 
                session.getId(), exception.getMessage(), exception);
        
        // 出现传输错误时，关闭会话并清理资源
        try {
            session.close(CloseStatus.SERVER_ERROR);
            removeSession(session);
        } catch (IOException e) {
            log.error("关闭错误会话失败 - 会话ID: {}", session.getId(), e);
        }
    }

    /**
     * 定时推送实时统计数据
     * 每5秒执行一次，向所有连接的客户端推送最新统计数据
     */
    @Scheduled(fixedRate = 5000)
    public void sendRealTimeStats() {
        if (sessions.isEmpty()) {
            return;
        }

        try {
            // 构建实时统计数据，使用本地缓存减少计算
            RealTimeStatsDTO stats = getRealTimeStats();
            
            // 创建JSON消息
            String message = JSON.toJSONString(stats);
            TextMessage textMessage = new TextMessage(message);
            
            int activeSessionCount = 0;
            int failedCount = 0;
            
            // 向所有连接的客户端推送数据
            for (WebSocketSession session : sessions.values()) {
                try {
                    if (session.isOpen()) {
                        session.sendMessage(textMessage);
                        activeSessionCount++;
                    } else {
                        removeSession(session);
                        failedCount++;
                    }
                } catch (IOException e) {
                    log.error("发送消息失败 - 会话ID: {}, 错误: {}", session.getId(), e.getMessage());
                    removeSession(session);
                    failedCount++;
                }
            }
            
            if (log.isDebugEnabled()) {
                log.debug("已推送实时统计数据 - 在线用户: {}, 今日访问: {}, 评论: {}, 系统负载: {}, 成功发送: {}, 失败: {}",
                        stats.getOnlineUsers(), stats.getTodayVisits(), stats.getTodayComments(), 
                        stats.getSystemLoad(), activeSessionCount, failedCount);
            }
        } catch (Exception e) {
            log.error("推送实时统计数据时发生错误", e);
        }
    }
    
    /**
     * 向指定会话发送统计数据
     */
    private void sendStatsToSession(WebSocketSession session) throws IOException {
        if (!session.isOpen()) {
            return;
        }
        
        RealTimeStatsDTO stats = getRealTimeStats();
        String message = JSON.toJSONString(stats);
        TextMessage textMessage = new TextMessage(message);
        session.sendMessage(textMessage);
    }
    
    /**
     * 获取实时统计数据（使用缓存减少计算）
     */
    private RealTimeStatsDTO getRealTimeStats() {
        long currentTime = System.currentTimeMillis();
        
        // 如果缓存有效，直接返回缓存数据
        if (cachedStats != null && (currentTime - lastStatsUpdate) < STATS_CACHE_TTL) {
            return cachedStats;
        }
        
        // 构建新的统计数据
        RealTimeStatsDTO stats = RealTimeStatsDTO.builder()
                .onlineUsers(userOnlineService.getOnlineUsersCount())
                .todayVisits(dashboardService.getTodayVisits())
                .todayComments(dashboardService.getTodayComments())
                .systemLoad(dashboardService.getSystemLoad())
                .timestamp(currentTime)
                .build();
        
        // 更新缓存
        cachedStats = stats;
        lastStatsUpdate = currentTime;
        
        return stats;
    }

    /**
     * 移除无效的会话
     */
    private void removeSession(WebSocketSession session) {
        if (session == null) return;
        
        String sessionId = session.getId();
        if (sessionId != null) {
            sessions.remove(sessionId);
        }
    }
    
    /**
     * 获取活跃WebSocket连接数量
     */
    public int getActiveSessionCount() {
        return (int) sessions.values().stream()
                .filter(Objects::nonNull)
                .filter(WebSocketSession::isOpen)
                .count();
    }
}