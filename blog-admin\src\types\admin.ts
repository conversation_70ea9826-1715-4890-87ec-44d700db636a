/**
 * 管理员信息
 */
export interface AdminInfo {
  /** ID */
  id: number
  /** 用户名 */
  username: string
  /** 邮箱 */
  email: string
  /** 头像 */
  avatar?: string
  /** 角色 */
  role: string
  /** 状态 */
  status: number
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
}

/**
 * 登录参数
 */
export interface LoginParams {
  /** 用户名 */
  username: string
  /** 密码 */
  password: string
  /** 验证码 */
  verificationCode?: string
}

/**
 * 登录结果
 */
export interface LoginResult {
  /** Token */
  token?: string
  /** 刷新令牌 */
  refreshToken?: string
  /** 用户信息 */
  user?: AdminInfo
  /** 是否需要验证 */
  needVerification?: boolean
  /** 消息 */
  message?: string
}

/**
 * 管理员设置
 */
export interface AdminSettings {
  /** ID */
  id?: number
  /** 管理员ID */
  adminId?: number
  /** 是否启用邮箱验证 */
  emailVerificationEnabled: boolean
  /** 创建时间 */
  createdAt?: string
  /** 更新时间 */
  updatedAt?: string
} 