import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import { ElMessage } from 'element-plus'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      component: () => import('@/layouts/DefaultLayout.vue'),
      children: [
        {
          path: '',
          redirect: '/home'
        },
        {
          path: 'home',
          name: 'home',
          component: () => import('@/views/home/<USER>'),
          meta: {
            title: '首页',
            requiresAuth: false
          }
        },
        {
          path: 'articles',
          name: 'articles',
          component: () => import('@/views/article/index.vue'),
          meta: {
            title: '文章列表',
            requiresAuth: false
          }
        },
        {
          path: 'articles/:id',
          name: 'article-detail',
          component: () => import('@/views/article/detail.vue'),
          meta: {
            title: '文章详情',
            requiresAuth: false
          }
        },
        {
          path: 'user/:id',
          name: 'user-home',
          component: () => import('@/views/user/UserHome.vue'),
          meta: {
            title: '用户主页',
            requiresAuth: false
          }
        },
        {
          path: 'categories',
          name: 'categories',
          component: () => import('@/views/category/index.vue'),
          meta: {
            title: '分类',
            requiresAuth: false
          }
        },
        {
          path: 'categories/:id',
          name: 'category-detail',
          component: () => import('@/views/category/detail.vue'),
          meta: {
            title: '分类详情',
            requiresAuth: false
          }
        },
        {
          path: 'tags',
          name: 'tags',
          component: () => import('@/views/tag/index.vue'),
          meta: {
            title: '标签',
            requiresAuth: false
          }
        },
        {
          path: 'tags/:id',
          name: 'tag-detail',
          component: () => import('@/views/tag/detail.vue'),
          meta: {
            title: '标签详情',
            requiresAuth: false
          }
        },
        {
          path: 'user',
          component: () => import('@/layouts/UserLayout.vue'),
          meta: { requiresAuth: true },
          children: [
            {
              path: 'profile',
              name: 'user-profile',
              component: () => import('@/views/user/profile.vue'),
              meta: { 
                title: '个人资料',
                requiresAuth: true 
              }
            },
            {
              path: 'articles',
              name: 'user-articles',
              component: () => import('@/views/user/articles.vue'),
              meta: { 
                title: '我的文章',
                requiresAuth: true 
              }
            },
            {
              path: 'comments',
              name: 'user-comments',
              component: () => import('@/views/user/comments.vue'),
              meta: { 
                title: '我的评论',
                requiresAuth: true 
              }
            },
            {
              path: 'favorites',
              name: 'user-favorites',
              component: () => import('@/views/user/favorites.vue'),
              meta: { 
                title: '我的收藏',
                requiresAuth: true 
              }
            },
            {
              path: 'messages',
              name: 'user-messages',
              component: () => import('@/views/user/messages.vue'),
              meta: { 
                title: '消息通知',
                requiresAuth: true 
              }
            },
            {
              path: 'follows',
              name: 'user-follows',
              component: () => import('@/views/user/Follows.vue'),
              meta: { 
                title: '关注管理',
                requiresAuth: true 
              }
            }
          ]
        },
        {
          path: '/search',
          name: 'Search',
          component: () => import('@/views/search/index.vue'),
          meta: {
            title: '搜索结果',
            requiresAuth: false
          }
        },
        {
          path: '/archive',
          name: 'archive',
          component: () => import('@/views/archive/index.vue'),
          meta: {
            title: '文章归档',
            requiresAuth: false
          }
        },
        {
          path: 'editor',
          name: 'editor',
          component: () => import('@/views/editor/index.vue'),
          meta: {
            title: '写文章',
            requiresAuth: true
          }
        },
        {
          path: 'editor/:id',
          name: 'editor-edit',
          component: () => import('@/views/editor/index.vue'),
          meta: {
            title: '编辑文章',
            requiresAuth: true
          }
        }
      ]
    },
    {
      path: '/auth',
      component: () => import('@/layouts/AuthLayout.vue'),
      children: [
        {
          path: 'login',
          name: 'login',
          component: () => import('@/views/auth/Login.vue'),
          meta: {
            title: '登录',
            guest: true
          }
        },
        {
          path: 'register',
          name: 'register',
          component: () => import('@/views/auth/Register.vue'),
          meta: {
            title: '注册',
            guest: true
          }
        },
        {
          path: 'forgot-password',
          name: 'forgot-password',
          component: () => import('@/views/auth/ForgotPassword.vue'),
          meta: {
            title: '忘记密码',
            guest: true
          }
        },
        {
          path: 'reset-password',
          name: 'reset-password',
          component: () => import('@/views/auth/ResetPassword.vue'),
          meta: {
            title: '重置密码',
            guest: true
          }
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/error/404.vue'),
      meta: {
        title: '404'
      }
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  const { isAuthenticated } = storeToRefs(userStore)
  
  // 设置页面标题
  document.title = `${to.meta.title || '博客'} - ModelBolt`
  
  // 需要登录的路由
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  // 游客页面（登录、注册等）
  const isGuestPage = to.matched.some(record => record.meta.guest)
  
  // 如果路由需要认证
  if (requiresAuth) {
    // 如果没有认证，重定向到登录页
    if (!isAuthenticated.value) {
      ElMessage.warning('请先登录')
      next({
        path: '/auth/login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }
  
  // 如果是游客页面且已认证
  if (isGuestPage && isAuthenticated.value) {
    next('/home')
    return
  }
  
  // 其他情况正常通过
  next()
})

export default router 