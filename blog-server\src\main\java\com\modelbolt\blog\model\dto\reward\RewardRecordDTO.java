package com.modelbolt.blog.model.dto.reward;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class RewardRecordDTO {
    private Long id;
    private Long articleId;
    private Long userId;
    private String username;
    private String userAvatar;
    private BigDecimal amount;
    private String paymentType;
    private String paymentStatus;
    private String message;
    private LocalDateTime createdAt;
} 