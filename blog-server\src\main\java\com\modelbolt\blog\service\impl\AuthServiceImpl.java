package com.modelbolt.blog.service.impl;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.constant.RedisKeyConstant;
import com.modelbolt.blog.model.dto.auth.*;
import com.modelbolt.blog.model.entity.LoginLog;
import com.modelbolt.blog.model.entity.User;
import com.modelbolt.blog.model.entity.UserSettings;
import com.modelbolt.blog.mapper.LoginLogMapper;
import com.modelbolt.blog.mapper.UserMapper;
import com.modelbolt.blog.mapper.UserSettingsMapper;
import com.modelbolt.blog.service.AuthService;
import com.modelbolt.blog.service.EmailService;
import com.modelbolt.blog.service.SessionService;
import com.modelbolt.blog.utils.JwtUtils;
import com.modelbolt.blog.utils.RedisUtils;
import eu.bitwalker.useragentutils.UserAgent;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDateTime;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final UserMapper userMapper;
    private final LoginLogMapper loginLogMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtils jwtUtils;
    private final EmailService emailService;
    private final RedisUtils redisUtils;
    private final SessionService sessionService;
    private final UserSettingsMapper userSettingsMapper;

    // JWT过期时间配置
    @Value("${jwt.expiration}")
    private Long expiration;

    // JWT记住我模式下的过期时间配置
    @Value("${jwt.remember-me-expiration}")
    private Long rememberMeExpiration;


    @Override
    public ApiResponse<LoginResponse> login(LoginRequest request, HttpServletRequest servletRequest) {
        String username = request.getUsername();
        String password = request.getPassword();
        boolean rememberMe = request.getRememberMe() != null && request.getRememberMe();

        // 查询用户
        User user = userMapper.findByUsername(username);
        if (user == null) {
            saveLoginLog(username, servletRequest, false, "用户不存在");
            return ApiResponse.error("用户名或密码错误");
        }

        // 检查用户状态
        if (user.getStatus() == 0) {
            saveLoginLog(username, servletRequest, false, "用户已被禁用");
            return ApiResponse.error("账号已被禁用，请联系管理员");
        }

        // 验证密码
        if (!passwordEncoder.matches(password, user.getPassword())) {
            saveLoginLog(username, servletRequest, false, "密码错误");
            return ApiResponse.error("用户名或密码错误");
        }

        // 生成token
        long tokenExpiration = rememberMe ? rememberMeExpiration : expiration;
        String token = jwtUtils.generateToken(username, user.getId(), user.getRole(), tokenExpiration);
        
        // 生成刷新令牌
        String refreshToken = jwtUtils.generateRefreshToken(username, user.getId(), user.getRole());

        // 保存token到Redis
        redisUtils.set("token:" + username, token, tokenExpiration, TimeUnit.SECONDS);
        
        // 记录会话
        sessionService.createSession(user.getId(), username, servletRequest, token, rememberMe);

        // 记录登录日志
        saveLoginLog(username, servletRequest, true, "登录成功");

        // 构建响应
        LoginResponse loginResponse = LoginResponse.builder()
                .token(token)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .expiresIn(tokenExpiration)
                .userId(user.getId())
                .username(user.getUsername())
                .email(user.getEmail())
                .avatar(user.getAvatar())
                .role(user.getRole())
                .build();

        return ApiResponse.success(loginResponse);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<RegisterResponse> register(RegisterRequest request) {
        // 验证用户名是否已存在
        if (userMapper.findByUsername(request.getUsername()) != null) {
            return ApiResponse.error("用户名已存在");
        }

        // 验证邮箱是否已存在
        if (userMapper.findByEmail(request.getEmail()) != null) {
            return ApiResponse.error("邮箱已存在");
        }

        // 验证验证码
        String codeKey = RedisKeyConstant.EMAIL_CODE_PREFIX + request.getEmail();
        String savedCode = (String) redisUtils.get(codeKey);
        if (savedCode == null) {
            return ApiResponse.error("验证码已过期，请重新获取");
        }
        if (!savedCode.equals(request.getVerificationCode())) {
            return ApiResponse.error("验证码错误");
        }

        // 创建用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setEmail(request.getEmail());
        user.setRole("USER");
        user.setStatus(1); // 默认启用
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());

        userMapper.insert(user);
        // 创建默认用户设置
        UserSettings settings = new UserSettings();
        settings.setUserId(user.getId());
        settings.setTheme("light");
        settings.setNotificationEnabled(true);
        settings.setLanguage("zh_CN");
        userSettingsMapper.insert(settings);

        // 删除验证码
        redisUtils.delete(codeKey);

        // 生成token
        String token = jwtUtils.generateToken(user.getUsername(), user.getId(), user.getRole());
        
        // 保存token到Redis
        redisUtils.set("token:" + user.getUsername(), token, expiration, TimeUnit.SECONDS);
        
        // 创建会话，使用token ID作为会话ID
        HttpServletRequest servletRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        sessionService.createSession(user.getId(), user.getUsername(), servletRequest, token, false);

        // 记录注册成功的登录日志
        saveLoginLog(user.getUsername(), servletRequest, true, "注册成功并自动登录");

        return ApiResponse.success("注册成功", 
                RegisterResponse.builder()
                        .message("注册成功")
                        .user(user)
                        .token(token)
                        .build());
    }

    @Override
    public ApiResponse<Void> sendResetPasswordEmail(String email) {
        // 发送密码重置邮件
        User user = userMapper.findByEmail(email);
        if (user == null) {
            return ApiResponse.error("邮箱不存在");
        }

        String resetToken = UUID.randomUUID().toString();
        // 将重置token存储到Redis，设置30分钟过期
        String resetKey = RedisKeyConstant.PASSWORD_RESET_PREFIX + resetToken;
        redisUtils.set(resetKey, user.getId(), 30, TimeUnit.MINUTES);

        // 生成重置链接
        String resetLink = "http://localhost:3000/auth/reset-password?token=" + resetToken;
        emailService.sendResetPasswordEmail(email, resetLink);

        return ApiResponse.success();
    }

    @Override
    @Transactional
    public ApiResponse<Void> resetPassword(ResetPasswordRequest request) {
        // 验证重置密码token
        String resetKey = RedisKeyConstant.PASSWORD_RESET_PREFIX + request.getToken();
        Object userIdObj = redisUtils.get(resetKey);
        if (userIdObj == null) {
            return ApiResponse.error("重置链接已过期或无效");
        }
        
        // 安全地将 userId 转换为 Long 类型
        Long userId = Long.valueOf(userIdObj.toString());

        // 获取用户信息
        User user = userMapper.selectById(userId);
        if (user == null) {
            return ApiResponse.error("用户不存在");
        }

        // 更新密码
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setUpdatedAt(LocalDateTime.now());
        userMapper.updateById(user);

        // 删除Redis中的token
        redisUtils.delete(resetKey);

        return ApiResponse.success("密码重置成功");
    }

    @Override
    public ApiResponse<Void> sendVerificationCode(SendVerificationCodeRequest request) {
        // 发送邮箱验证码
        String email = request.getEmail();
        
        // 检查邮箱是否已被注册
        if (userMapper.findByEmail(email) != null) {
            return ApiResponse.error("该邮箱已被注册");
        }
        
        // 检查发送频率
        String rateLimitKey = RedisKeyConstant.EMAIL_RATE_LIMIT_PREFIX + email;
        if (Boolean.TRUE.equals(redisUtils.hasKey(rateLimitKey))) {
            return ApiResponse.error("发送太频繁，请稍后再试");
        }
        
        // 生成6位随机验证码
        String verificationCode = String.format("%06d", new Random().nextInt(1000000));
        
        // 将验证码保存到Redis，设置5分钟过期
        String codeKey = RedisKeyConstant.EMAIL_CODE_PREFIX + email;
        redisUtils.set(codeKey, verificationCode, 5, TimeUnit.MINUTES);
        
        // 设置发送频率限制，1分钟内不能重复发送
        redisUtils.set(rateLimitKey, true, 1, TimeUnit.MINUTES);
        
        // 发送验证码邮件
        emailService.sendVerificationCode(email, verificationCode);
        
        return ApiResponse.success("验证码已发送，请查收邮件");
    }

    @Override
    public ApiResponse<Void> logout(String token) {
        if (token == null) {
            return ApiResponse.error("未提供token");
        }

        try {
            String tokenId = jwtUtils.getTokenIdFromToken(token);
            String username = jwtUtils.getUsernameFromToken(token);
            
            // 移除会话
            if (tokenId != null) {
                sessionService.removeSession(tokenId);
            }
            
            // 从Redis中删除token
            redisUtils.delete("token:" + username);
            return ApiResponse.success("登出成功");
        } catch (Exception e) {
            log.error("Logout failed", e);
            return ApiResponse.error("登出失败");
        }
    }

    @Override
    public ApiResponse<RefreshTokenResponse> refreshToken(RefreshTokenRequest request) {
        log.debug("Processing refresh token request");
        
        String refreshToken = request.getRefreshToken();
        
        // 验证刷新令牌
        if (!jwtUtils.validateRefreshToken(refreshToken)) {
            log.error("Invalid refresh token");
            return ApiResponse.error("无效的刷新令牌");
        }
        
        // 从刷新令牌中生成新的访问令牌
        String newAccessToken = jwtUtils.generateAccessTokenFromRefreshToken(refreshToken);
        if (newAccessToken == null) {
            log.error("Failed to generate new access token");
            return ApiResponse.error("生成新的访问令牌失败");
        }
        
        try {
            // 从刷新令牌中提取用户信息
            String username = jwtUtils.getUsernameFromToken(refreshToken);
            Long userId = jwtUtils.getUserIdFromToken(refreshToken);
            
            // 保存新的访问令牌到Redis
            redisUtils.set("token:" + username, newAccessToken, expiration, TimeUnit.SECONDS);
            log.debug("New access token saved to Redis for user: {}", username);
            
            // 获取当前请求，用于创建会话
            HttpServletRequest servletRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            
            // 创建或更新会话
            sessionService.createSession(userId, username, servletRequest, newAccessToken, false);
            log.debug("Session created with new access token for user: {}", username);
            
            // 构建响应
            RefreshTokenResponse response = RefreshTokenResponse.builder()
                    .accessToken(newAccessToken)
                    .tokenType("Bearer")
                    .expiresIn(expiration)
                    .build();
            
            log.debug("Token refreshed successfully");
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("Error during token refresh process", e);
            return ApiResponse.error("刷新令牌过程中发生错误");
        }
    }

    private void saveLoginLog(String username, HttpServletRequest request, boolean success, String msg) {
        // 保存登录日志
        UserAgent userAgent = UserAgent.parseUserAgentString(request.getHeader("User-Agent"));
        
        LoginLog loginLog = new LoginLog();
        loginLog.setUsername(username);
        loginLog.setIpAddress(request.getRemoteAddr());
        loginLog.setBrowser(userAgent.getBrowser().getName());
        loginLog.setOs(userAgent.getOperatingSystem().getName());
        loginLog.setStatus(success ? 1 : 0);
        loginLog.setMsg(msg);
        loginLog.setLoginTime(LocalDateTime.now());
        
        loginLogMapper.insert(loginLog);
    }
}