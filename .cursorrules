# 现代化博客系统规范文档

## 项目概述
项目类型: 集成 AI 能力的现代化博客系统
描述: 基于 DeepSeek API 的智能化博客系统,提供内容创作、管理和交互功能

## 系统架构

### 博客前台（面向读者）
[前台架构]
技术框架:
  - Vue 3.0+ (Composition API)
  - Pinia 状态管理
  - Vue Router 4 路由管理
  - Element Plus 组件库
  - TailwindCSS + SCSS 样式方案
  - Vite 4.0+ 构建工具
  - pnpm 包管理器

代码规范:
  - ESLint + Prettier 代码格式化
  - Vue 官方风格指南
  - 组件命名规范
  - 文件组织标准

性能要求:
  - 首屏加载时间 < 1.5s
  - 页面交互响应 < 100ms
  - 图片懒加载
  - 路由懒加载
  - 合理的缓存策略

### 管理后台（面向博主）
[后台架构]
技术框架:
  - Vue 3.0+ (Composition API)
  - Pinia 状态管理
  - Vue Router 4 路由管理
  - Ant Design Vue 组件库
  - TailwindCSS + SCSS 样式方案
  - Vite 4.0+ 构建工具
  - pnpm 包管理器

功能组件:
  - TinyMCE/WangEditor 富文本编辑器
  - MD Editor V3 Markdown编辑器
  - ECharts 5 图表库
  - Vue.Draggable 拖拽排序

性能要求:
  - 后台首屏加载 < 2s
  - 表格渲染优化
  - 大数据列表虚拟滚动
  - 按需加载组件

### 后端服务
[服务架构]
技术框架:
  - Spring Boot 3.0+
  - MyBatis Plus
  - Redis 缓存
  - MySQL 8.0+
  - JWT + Redis 认证
  - DeepSeek API 集成

性能要求:
  - API 响应时间 < 200ms
  - 数据库查询 < 100ms
  - 缓存命中率 > 80%
  - 支持并发用户 1000+

## 项目结构
```
├── blog-frontend/           # 博客前台
│   ├── src/
│   │   ├── api/           # API 接口
│   │   ├── assets/       # 静态资源
│   │   ├── components/   # 组件
│   │   ├── composables/  # 组合式函数
│   │   ├── layouts/      # 布局组件
│   │   ├── router/       # 路由配置
│   │   ├── stores/       # 状态管理
│   │   ├── styles/       # 样式文件
│   │   ├── utils/        # 工具函数
│   │   └── views/        # 页面组件
│   ├── public/
│   └── tests/
│
├── blog-admin/             # 管理后台
│   ├── src/
│   │   ├── api/           # API 接口
│   │   ├── assets/       # 静态资源
│   │   ├── components/   # 组件
│   │   ├── composables/  # 组合式函数
│   │   ├── layouts/      # 布局组件
│   │   ├── router/       # 路由配置
│   │   ├── stores/       # 状态管理
│   │   ├── styles/       # 样式文件
│   │   ├── utils/        # 工具函数
│   │   └── views/        # 页面组件
│   ├── public/
│   └── tests/
│
└── blog-server/           # 后端服务
    ├── src/
    │   ├── main/
    │   │   ├── java/
    │   │   └── resources/
    │   └── test/
    └── docs/
```

## 编码规范

### 通用规范
- 统一使用 UTF-8 编码
- 使用清晰的命名方式
- 保持代码简洁性
- 编写必要的注释
- 遵循 DRY 原则
- 合理的错误处理

### 前端规范
- 使用 Composition API
- 组件命名采用大驼峰
- 文件命名采用短横线
- props 必须声明类型
- 避免过度嵌套组件
- 编写单元测试

### 后端规范
- 遵循 RESTful API 设计
- 统一异常处理
- 规范的数据库设计
- 完善的接口文档
- 合理的缓存策略
- 必要的日志记录

## 安全规范
[安全要求]
认证授权:
  - JWT 身份验证
  - 密码加密存储
  - 会话管理
  - 权限控制

数据安全:
  - 输入验证
  - XSS 防护
  - CSRF 防护
  - SQL 注入防护
  - 敏感数据加密

接口安全:
  - 访问频率限制
  - 请求参数验证
  - 跨域配置
  - 安全响应头

## 测试规范
[测试要求]
前端测试:
  - 单元测试 (Vitest)
  - 组件测试
  - 覆盖率 > 80%

后端测试:
  - 单元测试 (JUnit 5)
  - 接口测试
  - 覆盖率 > 80%

监控要求:
  - 错误监控
  - 性能监控
  - 用户行为分析
  - 服务器监控

数据库设计:

-- ============================================= 用户管理相关表 =============================================
-- 用户表
CREATE TABLE `user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `bio` varchar(500) DEFAULT NULL COMMENT '个人简介',
  `role` varchar(20) NOT NULL DEFAULT 'USER' COMMENT '角色:USER,ADMIN',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 用户配置表
CREATE TABLE `user_settings` (
  `user_id` bigint NOT NULL,
  `theme` varchar(20) DEFAULT 'light' COMMENT '主题设置',
  `notification_enabled` tinyint(1) DEFAULT '1' COMMENT '通知开关',
  `language` varchar(10) DEFAULT 'zh_CN' COMMENT '语言设置',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户配置表';

-- 用户关注表
CREATE TABLE `user_follow` (
  `follower_id` bigint NOT NULL COMMENT '关注者ID',
  `following_id` bigint NOT NULL COMMENT '被关注者ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`follower_id`,`following_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户关注表';

-- 登录日志表
CREATE TABLE IF NOT EXISTS `login_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `browser` varchar(50) DEFAULT NULL COMMENT '浏览器类型',
  `os` varchar(50) DEFAULT NULL COMMENT '操作系统',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0失败 1成功',
  `msg` varchar(255) DEFAULT NULL COMMENT '提示信息',
  `login_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录日志表';


-- ============================================= 内容管理相关表 =============================================
-- 文章表
CREATE TABLE `article` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL COMMENT '标题',
  `content` longtext NOT NULL COMMENT '内容',
  `summary` varchar(500) DEFAULT NULL COMMENT '文章摘要',
  `cover` varchar(255) DEFAULT NULL COMMENT '封面图片URL',
  `author_id` bigint NOT NULL COMMENT '作者ID',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态:0草稿,1发布,2下架',
  `view_count` int DEFAULT '0' COMMENT '浏览量',
  `like_count` int DEFAULT '0' COMMENT '点赞数',
  `favorite_count` int DEFAULT '0' COMMENT '收藏数',
  `comment_count` int DEFAULT '0' COMMENT '评论数',
  `is_top` tinyint(1) DEFAULT '0' COMMENT '是否置顶',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_author` (`author_id`),
  FULLTEXT KEY `idx_title_content` (`title`,`content`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章表';

-- 分类表
CREATE TABLE `category` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `parent_id` bigint DEFAULT NULL COMMENT '父分类ID',
  `order_num` int DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类表';

-- 标签表
CREATE TABLE `tag` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';

-- 文章分类关联表
CREATE TABLE `article_category` (
  `article_id` bigint NOT NULL,
  `category_id` bigint NOT NULL,
  PRIMARY KEY (`article_id`,`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章分类关联表';

-- 文章标签关联表
CREATE TABLE `article_tag` (
  `article_id` bigint NOT NULL,
  `tag_id` bigint NOT NULL,
  PRIMARY KEY (`article_id`,`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章标签关联表';

-- 访问历史记录表
CREATE TABLE `visit_history` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `visit_date` date NOT NULL COMMENT '访问日期',
  `visit_count` int NOT NULL DEFAULT '0' COMMENT '访问次数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_visit_date` (`visit_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='访问历史记录表';


-- ============================================= 互动管理相关表 =============================================
-- 评论表
CREATE TABLE `comment` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `content` text NOT NULL COMMENT '评论内容',
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `user_id` bigint NOT NULL COMMENT '评论用户ID',
  `parent_id` bigint DEFAULT NULL COMMENT '父评论ID',
  `root_id` bigint DEFAULT NULL COMMENT '根评论ID',
  `status` tinyint DEFAULT '0' COMMENT '状态:0待审核,1通过,2拒绝',
  `like_count` int DEFAULT '0' COMMENT '点赞数',
  `reply_count` int DEFAULT '0' COMMENT '回复数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_article` (`article_id`),
  KEY `idx_user` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

-- 评论点赞表
CREATE TABLE `comment_like` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `comment_id` bigint NOT NULL COMMENT '评论ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_comment_user` (`comment_id`, `user_id`),
  KEY `idx_user` (`user_id`),
  CONSTRAINT `fk_comment_like_comment` FOREIGN KEY (`comment_id`) REFERENCES `comment` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_comment_like_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论点赞表';

-- 用户点赞表
CREATE TABLE `user_like` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '点赞时间',
  PRIMARY KEY (`user_id`,`article_id`),
  KEY `idx_article_user` (`article_id`,`user_id`),
  CONSTRAINT `fk_user_like_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_like_article` FOREIGN KEY (`article_id`) REFERENCES `article` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户点赞表';

-- 用户收藏表
CREATE TABLE `user_favorite` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  PRIMARY KEY (`user_id`,`article_id`),
  KEY `idx_article_user` (`article_id`,`user_id`),
  CONSTRAINT `fk_user_favorite_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_favorite_article` FOREIGN KEY (`article_id`) REFERENCES `article` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏表';

-- 分享记录表
CREATE TABLE `share_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `user_id` bigint DEFAULT NULL COMMENT '分享用户ID(未登录为空)',
  `platform` varchar(20) NOT NULL COMMENT '分享平台:WECHAT,WEIBO,LINK,POSTER',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(200) DEFAULT NULL COMMENT '浏览器信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_article` (`article_id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_share_article` FOREIGN KEY (`article_id`) REFERENCES `article` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_share_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分享记录表';

-- 通知表
CREATE TABLE `notification` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '接收通知的用户ID',
  `sender_id` bigint DEFAULT NULL COMMENT '发送者ID',
  `type` varchar(20) NOT NULL COMMENT '通知类型:COMMENT,REPLY,LIKE,SYSTEM',
  `target_id` bigint NOT NULL COMMENT '目标ID(文章ID或评论ID)',
  `target_type` varchar(20) NOT NULL COMMENT '目标类型:ARTICLE,COMMENT',
  `content` text NOT NULL COMMENT '通知内容',
  `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_read` (`user_id`, `is_read`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_notification_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知表';


-- ============================================= 打赏相关表 =============================================
-- 打赏配置表
CREATE TABLE `reward_config` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用打赏',
  `amounts` json DEFAULT NULL COMMENT '打赏金额选项',
  `qrcode_wechat` varchar(255) DEFAULT NULL COMMENT '微信收款码',
  `qrcode_alipay` varchar(255) DEFAULT NULL COMMENT '支付宝收款码',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  CONSTRAINT `fk_reward_config_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='打赏配置表';

-- 打赏记录表
CREATE TABLE `article_reward` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `user_id` bigint NOT NULL COMMENT '打赏用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '打赏金额',
  `payment_type` varchar(20) NOT NULL COMMENT '支付方式:WECHAT,ALIPAY',
  `payment_status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '支付状态:PENDING,SUCCESS,FAILED',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '支付平台交易号',
  `message` varchar(200) DEFAULT NULL COMMENT '打赏留言',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_article_user` (`article_id`,`user_id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_reward_article` FOREIGN KEY (`article_id`) REFERENCES `article` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_reward_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='打赏记录表';


-- ============================================= 系统管理相关表 =============================================
-- 系统配置表
CREATE TABLE `system_config` (
  `key` varchar(50) NOT NULL COMMENT '配置键',
  `value` text COMMENT '配置值',
  `description` varchar(200) DEFAULT NULL COMMENT '配置描述',
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 操作日志表
CREATE TABLE `operation_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL COMMENT '操作用户ID',
  `operation` varchar(50) NOT NULL COMMENT '操作类型',
  `method` varchar(200) NOT NULL COMMENT '请求方法',
  `params` text COMMENT '请求参数',
  `ip` varchar(64) DEFAULT NULL COMMENT '操作IP',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';


-- ============================================= AI服务相关表 =============================================
-- AI 对话历史表
CREATE TABLE `ai_chat_history` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `session_id` varchar(50) NOT NULL COMMENT '会话ID',
  `role` varchar(20) NOT NULL COMMENT '角色:user,assistant',
  `content` text NOT NULL COMMENT '对话内容',
  `tokens` int DEFAULT '0' COMMENT 'token数量',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_session` (`user_id`,`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI对话历史表';

-- AI 使用统计表
CREATE TABLE `ai_usage_stats` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `date` date NOT NULL COMMENT '统计日期',
  `total_tokens` int DEFAULT '0' COMMENT '总token数量',
  `total_requests` int DEFAULT '0' COMMENT '总请求次数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_date` (`user_id`,`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI使用统计表';

-- 管理员设置表
CREATE TABLE IF NOT EXISTS `admin_settings` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `admin_id` bigint NOT NULL COMMENT '管理员ID',
  `email_verification_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用邮箱验证',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_admin_id` (`admin_id`),
  CONSTRAINT `fk_admin_settings_user` FOREIGN KEY (`admin_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员设置表';
