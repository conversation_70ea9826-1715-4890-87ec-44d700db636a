import { http } from '@/utils/request'
import type { User, UserQuery, UserForm, StatusUpdateDTO } from '@/types/user'
import type { R, PageResult } from '@/types/common'

export const userApi = {
  // 获取用户列表
  getUsers(params: UserQuery) {
    return http.get<R<PageResult<User>>>('/admin/users', { params })
  },
  
  // 更新用户信息
  updateUser(id: number, data: UserForm) {
    return http.put<R<void>>(`/admin/users/${id}`, data)
  },
  
  // 更新用户状态
  updateUserStatus(id: number, status: number) {
    return http.put<R<void>>(`/admin/users/${id}/status`, { status } as StatusUpdateDTO)
  },
  
  // 导出用户数据
  exportUsers(params: Partial<UserQuery>) {
    return http.get('/admin/users/export', { 
      params,
      responseType: 'blob',
      headers: {
        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      },
      _isExport: true
    })
  }
} 