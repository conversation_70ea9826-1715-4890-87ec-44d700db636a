// 搜索建议类型
export interface SearchSuggestion {
  id: number
  title?: string
  name?: string
  type: 'article' | 'tag' | 'category'
  count?: number
}

// 搜索历史记录
export interface SearchHistory {
  keyword: string
  timestamp: number
}

// 热门搜索
export interface HotSearch {
  keyword: string
  count: number
}

export interface SearchResult {
  id: number
  title: string
  summary: string
  cover?: string
  authorName: string
  viewCount: number
  commentCount: number
  createdAt: string
  tags?: Array<{
    id: number
    name: string
  }>
} 