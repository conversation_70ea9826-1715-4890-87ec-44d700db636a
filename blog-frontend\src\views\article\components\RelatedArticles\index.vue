<template>
  <div class="related-articles bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
    <h3 class="text-lg font-bold mb-4 text-gray-900 dark:text-gray-100">相关文章</h3>
    
    <div class="space-y-4">
      <div
        v-for="article in articles"
        :key="article.id"
        class="block hover:bg-gray-50 dark:hover:bg-gray-700 p-2 rounded transition-colors duration-200 cursor-pointer"
        @click="handleArticleClick(article.id)"
      >
        <div class="flex items-center gap-4">
          <el-image
            v-if="article.cover"
            :src="getCoverUrl(article.cover)"
            class="w-16 h-12 object-cover rounded"
            fit="cover"
          />
          <div>
            <div class="text-gray-900 dark:text-gray-100 font-medium line-clamp-1">{{ article.title }}</div>
            <div class="text-sm text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">{{ article.summary }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import type { ArticleVO } from '@/types/article'
import { getCoverUrl } from '@/utils/image'

defineProps<{
  articles: ArticleVO[]
}>()

const router = useRouter()

const handleArticleClick = async (articleId: number) => {
  try {
    // 使用 push 进行导航，确保触发完整的路由更新
    await router.push({
      path: `/articles/${articleId}`,
      replace: true // 使用 replace 模式，避免在历史记录中堆积
    })
  } catch (error) {
    console.error('Navigation failed:', error)
  }
}
</script>

<style lang="scss" scoped>
.related-articles {
  .article-item {
    &:hover {
      .el-image {
        @apply shadow-sm;
      }
    }
  }

  .image-placeholder {
    @apply rounded;
  }
}
</style> 