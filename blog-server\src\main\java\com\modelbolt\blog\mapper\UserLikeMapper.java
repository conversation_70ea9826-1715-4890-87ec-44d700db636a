package com.modelbolt.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.modelbolt.blog.model.entity.UserLike;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface UserLikeMapper extends BaseMapper<UserLike> {
    
    @Select("SELECT COUNT(*) > 0 FROM user_like WHERE user_id = #{userId} AND article_id = #{articleId}")
    boolean existsByUserIdAndArticleId(@Param("userId") Long userId, @Param("articleId") Long articleId);
    
    @Delete("DELETE FROM user_like WHERE user_id = #{userId} AND article_id = #{articleId}")
    int deleteByUserIdAndArticleId(@Param("userId") Long userId, @Param("articleId") Long articleId);
} 