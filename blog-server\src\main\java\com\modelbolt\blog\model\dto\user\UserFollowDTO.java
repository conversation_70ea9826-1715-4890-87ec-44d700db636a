package com.modelbolt.blog.model.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户关注信息")
public class UserFollowDTO {
    
    @Schema(description = "用户ID")
    private Long userId;
    
    @Schema(description = "用户名")
    private String username;
    
    @Schema(description = "头像")
    private String avatar;
    
    @Schema(description = "个人简介")
    private String bio;
    
    @Schema(description = "关注时间")
    private LocalDateTime followTime;
    
    @Schema(description = "是否互相关注")
    private Boolean isFollowEachOther;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "关注统计")
    public static class FollowStats {
        
        @Schema(description = "关注数量")
        private Integer followingCount;
        
        @Schema(description = "粉丝数量")
        private Integer followerCount;
    }
} 