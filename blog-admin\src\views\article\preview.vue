<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import type { Article } from '@/types/article'
import { getArticleDetail } from '@/api/article'
import MdEditor from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'
import { getAvatarUrl, getRefreshedImageUrl, getCoverUrl, useImageFallback } from '@/utils/image'

const route = useRoute()
const router = useRouter()
const id = Number(route.params.id)
const { imgError } = useImageFallback()

// 文章数据
const article = ref<Article>()
// 加载状态
const loading = ref(false)

// 获取文章详情
const getDetail = async () => {
  loading.value = true
  try {
    const { data } = await getArticleDetail(id)
    article.value = data
  } catch (error) {
    message.error('获取文章详情失败')
  } finally {
    loading.value = false
  }
}

// 返回编辑
const handleBack = () => {
  router.push(`/article/edit/${id}`)
}

onMounted(() => {
  getDetail()
})
</script>

<template>
  <div class="article-preview">
    <div class="header">
      <div class="title">文章预览</div>
      <div class="actions">
        <a-button type="primary" @click="handleBack">返回编辑</a-button>
      </div>
    </div>

    <a-spin :spinning="loading">
      <div v-if="article" class="content">
        <!-- 文章标题 -->
        <h1 class="article-title">{{ article.title }}</h1>

        <!-- 文章信息 -->
        <div class="article-info">
          <span>创建时间：{{ article.createdAt }}</span>
          <span>更新时间：{{ article.updatedAt }}</span>
          <span>浏览量：{{ article.viewCount }}</span>
          <span>评论数：{{ article.commentCount }}</span>
        </div>

        <!-- 文章封面 -->
        <div v-if="article.cover" class="article-cover">
          <img 
            :src="getRefreshedImageUrl(getCoverUrl(article.cover))" 
            :alt="article.title"
            @error="imgError"
            @load="() => console.log('Cover image loaded:', article.cover)"
          />
        </div>

        <!-- 文章内容 -->
        <div class="article-content">
          <MdEditor 
            :modelValue="article.content" 
            :previewOnly="true"
            :previewTheme="'default'"
            :codeTheme="'atom'"
            :showCodeRowNumber="true"
            :toolbarsExclude="['github']"
            class="md-preview"
          />
        </div>
      </div>
    </a-spin>
  </div>
</template>

<style lang="scss" scoped>
.article-preview {
  padding: 24px;
  background: #fff;
  min-height: 100%;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .title {
      font-size: 20px;
      font-weight: 500;
    }
  }

  .content {
    max-width: 1000px;
    margin: 0 auto;

    .article-title {
      font-size: 32px;
      font-weight: 500;
      text-align: center;
      margin-bottom: 24px;
    }

    .article-info {
      text-align: center;
      color: #666;
      margin-bottom: 24px;

      span {
        margin: 0 12px;
      }
    }

    .article-cover {
      text-align: center;
      margin-bottom: 24px;

      img {
        max-width: 100%;
        max-height: 400px;
        object-fit: cover;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    .article-content {
      line-height: 1.8;

      :deep(.md-preview) {
        border: none;
        background: transparent;
      }

      :deep(.md-editor-preview-wrapper) {
        padding: 0;
      }

      :deep(img) {
        max-width: 100%;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }
}
</style> 