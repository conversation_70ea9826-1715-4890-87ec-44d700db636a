package com.modelbolt.blog.model.dto.user;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class AuthorInfoDTO {
    private Long id;
    private String username;
    private String avatar;
    private String bio;
    private AuthorStatsDTO stats;

    @Data
    @Builder
    public static class AuthorStatsDTO {
        private Integer articles;    // 文章数
        private Integer followers;   // 粉丝数
        private Integer likes;       // 获赞数
    }
} 