package com.modelbolt.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.modelbolt.blog.model.entity.ArticleReward;
import com.modelbolt.blog.model.dto.reward.RewardRecordDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface ArticleRewardMapper extends BaseMapper<ArticleReward> {
    
    @Select("SELECT COALESCE(SUM(amount), 0) FROM article_reward " +
            "WHERE article_id = #{articleId} AND payment_status = 'SUCCESS'")
    BigDecimal selectTotalRewardAmount(@Param("articleId") Long articleId);

    @Select("SELECT ar.id, ar.article_id, ar.user_id, u.username, u.avatar as user_avatar, " +
            "ar.amount, ar.payment_type, ar.payment_status, ar.message, ar.created_at " +
            "FROM article_reward ar " +
            "LEFT JOIN user u ON ar.user_id = u.id " +
            "WHERE ar.article_id = #{articleId} " +
            "AND ar.payment_status = 'SUCCESS' " +
            "ORDER BY ar.created_at DESC")
    List<RewardRecordDTO> selectRewardRecords(@Param("articleId") Long articleId, IPage<ArticleReward> page);
} 