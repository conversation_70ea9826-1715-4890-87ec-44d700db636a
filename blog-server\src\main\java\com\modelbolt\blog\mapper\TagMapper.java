package com.modelbolt.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.modelbolt.blog.model.dto.tag.TagVO;
import com.modelbolt.blog.model.entity.Tag;
import com.modelbolt.blog.model.entity.Article;
import com.modelbolt.blog.model.vo.SearchVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TagMapper extends BaseMapper<Tag> {
    
    @Select("SELECT t.id, t.name, COUNT(at.article_id) as articleCount " +
            "FROM tag t " +
            "LEFT JOIN article_tag at ON t.id = at.tag_id " +
            "LEFT JOIN article a ON at.article_id = a.id AND a.status = 1 " +
            "GROUP BY t.id, t.name " +
            "ORDER BY articleCount DESC " +
            "LIMIT #{limit}")
    List<TagVO> selectHotTags(Integer limit);

    /**
     * 查询文章的标签列表
     */
    @Select("SELECT t.id, t.name, " +
            "(SELECT COUNT(*) FROM article_tag at2 " +
            " LEFT JOIN article a2 ON at2.article_id = a2.id AND a2.status = 1 " +
            " WHERE at2.tag_id = t.id) as articleCount " +
            "FROM tag t " +
            "INNER JOIN article_tag at ON t.id = at.tag_id " +
            "WHERE at.article_id = #{articleId}")
    List<TagVO> selectTagsByArticleId(Long articleId);

    @Select("SELECT t.id, t.name, 'tag' as type, " +
            "COUNT(at.article_id) as articleCount " +
            "FROM tag t " +
            "LEFT JOIN article_tag at ON t.id = at.tag_id " +
            "WHERE t.name LIKE CONCAT('%', #{keyword}, '%') " +
            "GROUP BY t.id")
    List<SearchVO> searchTags(@Param("keyword") String keyword);

    @Select("SELECT t.id, t.name, COUNT(at.article_id) as articleCount " +
            "FROM tag t " +
            "LEFT JOIN article_tag at ON t.id = at.tag_id " +
            "LEFT JOIN article a ON at.article_id = a.id AND a.status = 1 " +
            "GROUP BY t.id, t.name " +
            "ORDER BY articleCount DESC")
    List<TagVO> selectAllTags();

    @Select("SELECT t.id, t.name, COUNT(at.article_id) as articleCount " +
            "FROM tag t " +
            "LEFT JOIN article_tag at ON t.id = at.tag_id " +
            "LEFT JOIN article a ON at.article_id = a.id AND a.status = 1 " +
            "WHERE t.id = #{id} " +
            "GROUP BY t.id, t.name")
    TagVO selectTagById(@Param("id") Long id);

    @Select("SELECT a.* FROM article a " +
            "INNER JOIN article_tag at ON a.id = at.article_id " +
            "WHERE at.tag_id = #{tagId} AND a.status = 1 " +
            "ORDER BY a.created_at DESC")
    IPage<Article> getArticlesByTagId(Page<Article> page, @Param("tagId") Long tagId);

    /**
     * 统计标签下的文章数量
     */
    @Select("SELECT COUNT(DISTINCT a.id) FROM article_tag at " +
            "LEFT JOIN article a ON at.article_id = a.id AND a.status = 1 " +
            "WHERE at.tag_id = #{tagId}")
    Integer countArticles(@Param("tagId") Long tagId);
} 