package com.modelbolt.blog.service;

import org.springframework.web.multipart.MultipartFile;

/**
 * 管理员服务接口
 */
public interface AdminService {
    /**
     * 更新管理员头像
     *
     * @param adminId 管理员ID
     * @param avatar  头像文件
     * @return 头像URL
     */
    String updateAdminAvatar(Long adminId, MultipartFile avatar);

    /**
     * 更新管理员密码
     *
     * @param adminId     管理员ID
     * @param oldPassword 原密码
     * @param newPassword 新密码
     */
    void updateAdminPassword(Long adminId, String oldPassword, String newPassword);
} 