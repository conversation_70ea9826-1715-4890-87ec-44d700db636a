package com.modelbolt.blog.service.impl;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.constant.RedisKeyConstant;
import com.modelbolt.blog.model.dto.auth.AdminLoginRequest;
import com.modelbolt.blog.model.dto.auth.AdminLoginResponse;
import com.modelbolt.blog.model.dto.auth.RefreshTokenRequest;
import com.modelbolt.blog.model.dto.auth.RefreshTokenResponse;
import com.modelbolt.blog.model.entity.LoginLog;
import com.modelbolt.blog.model.entity.User;
import com.modelbolt.blog.model.entity.AdminSettings;
import com.modelbolt.blog.mapper.LoginLogMapper;
import com.modelbolt.blog.mapper.UserMapper;
import com.modelbolt.blog.mapper.AdminSettingsMapper;
import com.modelbolt.blog.service.AdminAuthService;
import com.modelbolt.blog.service.EmailService;
import com.modelbolt.blog.service.SessionService;
import com.modelbolt.blog.utils.JwtUtils;
import com.modelbolt.blog.utils.RedisUtils;
import eu.bitwalker.useragentutils.UserAgent;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDateTime;
import java.util.Random;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdminAuthServiceImpl implements AdminAuthService {

    private final UserMapper userMapper;
    private final LoginLogMapper loginLogMapper;
    private final AdminSettingsMapper adminSettingsMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtils jwtUtils;
    private final EmailService emailService;
    private final RedisUtils redisUtils;
    private final SessionService sessionService;

    // JWT过期时间配置
    @Value("${jwt.expiration}")
    private Long expiration;

    @Override
    public ApiResponse<AdminLoginResponse> login(AdminLoginRequest request, HttpServletRequest servletRequest) {
        User user = userMapper.findByUsername(request.getUsername());
        if (user == null) {
            saveLoginLog(request.getUsername(), servletRequest, false, "用户不存在");
            return ApiResponse.error(401, "用户名或密码错误");
        }

        if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            saveLoginLog(request.getUsername(), servletRequest, false, "密码错误");
            return ApiResponse.error(401, "用户名或密码错误");
        }

        if (user.getStatus() == 0) {
            saveLoginLog(request.getUsername(), servletRequest, false, "账号已禁用");
            return ApiResponse.error(403, "账号已被禁用");
        }

        if (!"ADMIN".equals(user.getRole())) {
            saveLoginLog(request.getUsername(), servletRequest, false, "非管理员账号");
            return ApiResponse.error(403, "非管理员账号");
        }

        // 获取管理员设置
        AdminSettings settings = adminSettingsMapper.selectOne(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<AdminSettings>()
                .eq(AdminSettings::getAdminId, user.getId())
        );

        // 如果没有设置记录，创建默认设置（默认启用邮箱验证）
        if (settings == null) {
            settings = new AdminSettings()
                .setAdminId(user.getId())
                .setEmailVerificationEnabled(true);
            adminSettingsMapper.insert(settings);
        }

        // 如果启用了邮箱验证且没有提供验证码
        if (settings.getEmailVerificationEnabled() && request.getVerificationCode() == null) {
            // 生成并发送验证码
            String verificationCode = String.format("%06d", new Random().nextInt(1000000));
            String codeKey = RedisKeyConstant.ADMIN_EMAIL_CODE_PREFIX + user.getEmail();
            redisUtils.set(codeKey, verificationCode, 5, TimeUnit.MINUTES);
            emailService.sendVerificationCode(user.getEmail(), verificationCode);
            
            // 创建一个简单的用户对象，只包含必要信息
            User simpleUser = new User();
            simpleUser.setEmail(user.getEmail());
            simpleUser.setUsername(user.getUsername());
            
            return ApiResponse.success(AdminLoginResponse.builder()
                    .needVerification(true)
                    .user(simpleUser)
                    .build());
        }

        // 如果启用了邮箱验证，验证验证码
        if (settings.getEmailVerificationEnabled() && request.getVerificationCode() != null) {
            String codeKey = RedisKeyConstant.ADMIN_EMAIL_CODE_PREFIX + user.getEmail();
            String savedCode = (String) redisUtils.get(codeKey);
            if (savedCode == null) {
                return ApiResponse.error("验证码已过期，请重新获取");
            }
            if (!savedCode.equals(request.getVerificationCode())) {
                return ApiResponse.error("验证码错误");
            }
            // 删除验证码
            redisUtils.delete(codeKey);
        }

        // 生成新的token，包含角色信息
        String token = jwtUtils.generateToken(user.getUsername(), user.getId(), user.getRole());
        
        // 生成刷新令牌
        String refreshToken = jwtUtils.generateRefreshToken(user.getUsername(), user.getId(), user.getRole());
        
        // 保存token到Redis
        redisUtils.set("token:" + user.getUsername(), token, expiration, TimeUnit.SECONDS);
        
        // 创建会话，使用token ID作为会话ID
        sessionService.createSession(user.getId(), user.getUsername(), servletRequest, token, false);

        saveLoginLog(request.getUsername(), servletRequest, true, "登录成功");
        
        return ApiResponse.success("登录成功", AdminLoginResponse.builder()
                .token(token)
                .refreshToken(refreshToken)
                .user(user)
                .needVerification(false)
                .build());
    }

    @Override
    public ApiResponse<Void> sendVerificationCode(String email) {
        // 检查邮箱是否存在且是管理员
        User user = userMapper.findByEmail(email);
        if (user == null || !"ADMIN".equals(user.getRole())) {
            return ApiResponse.error("邮箱不存在或非管理员账号");
        }
        
        // 检查发送频率
        String rateLimitKey = RedisKeyConstant.ADMIN_EMAIL_RATE_LIMIT_PREFIX + email;
        if (Boolean.TRUE.equals(redisUtils.hasKey(rateLimitKey))) {
            return ApiResponse.error("发送太频繁，请稍后再试");
        }
        
        // 生成6位随机验证码
        String verificationCode = String.format("%06d", new Random().nextInt(1000000));
        
        // 将验证码保存到Redis，设置5分钟过期
        String codeKey = RedisKeyConstant.ADMIN_EMAIL_CODE_PREFIX + email;
        redisUtils.set(codeKey, verificationCode, 5, TimeUnit.MINUTES);
        
        // 设置发送频率限制，1分钟内不能重复发送
        redisUtils.set(rateLimitKey, true, 1, TimeUnit.MINUTES);
        
        // 发送验证码邮件
        emailService.sendVerificationCode(email, verificationCode);
        
        return ApiResponse.success("验证码已发送，请查收邮件");
    }

    @Override
    public ApiResponse<Void> logout(String token) {
        if (token == null) {
            return ApiResponse.error("未提供token");
        }

        try {
            String tokenId = jwtUtils.getTokenIdFromToken(token);
            String username = jwtUtils.getUsernameFromToken(token);
            
            // 移除会话
            if (tokenId != null) {
                sessionService.removeSession(tokenId);
            }
            
            // 从Redis中删除token
            redisUtils.delete("token:" + username);
            return ApiResponse.success("登出成功");
        } catch (Exception e) {
            log.error("Logout failed", e);
            return ApiResponse.error("登出失败");
        }
    }

    @Override
    public ApiResponse<AdminLoginResponse> getAdminInfo(String username) {
        User user = userMapper.findByUsername(username);
        if (user == null) {
            return ApiResponse.error(401, "用户不存在");
        }

        if (!"ADMIN".equals(user.getRole())) {
            return ApiResponse.error(403, "非管理员账号");
        }

        if (user.getStatus() == 0) {
            return ApiResponse.error(403, "账号已被禁用");
        }

        // 获取当前token
        String token = (String) redisUtils.get("token:" + username);
        if (token == null) {
            return ApiResponse.error(401, "登录已过期，请重新登录");
        }

        return ApiResponse.success(AdminLoginResponse.builder()
                .token(token)
                .user(user)
                .needVerification(false)
                .build());
    }

    @Override
    public ApiResponse<RefreshTokenResponse> refreshToken(RefreshTokenRequest request) {
        log.debug("Processing admin refresh token request");
        
        String refreshToken = request.getRefreshToken();
        
        // 验证刷新令牌
        if (!jwtUtils.validateRefreshToken(refreshToken)) {
            log.error("Invalid admin refresh token");
            return ApiResponse.error("无效的刷新令牌");
        }
        
        // 从刷新令牌中生成新的访问令牌
        String newAccessToken = jwtUtils.generateAccessTokenFromRefreshToken(refreshToken);
        if (newAccessToken == null) {
            log.error("Failed to generate new access token for admin");
            return ApiResponse.error("生成新的访问令牌失败");
        }
        
        try {
            // 从刷新令牌中提取用户信息
            String username = jwtUtils.getUsernameFromToken(refreshToken);
            Long userId = jwtUtils.getUserIdFromToken(refreshToken);
            
            // 保存新的访问令牌到Redis
            redisUtils.set("token:" + username, newAccessToken, expiration, TimeUnit.SECONDS);
            log.debug("New access token saved to Redis for admin: {}", username);
            
            // 获取当前请求，用于创建会话
            HttpServletRequest servletRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            
            // 创建或更新会话
            sessionService.createSession(userId, username, servletRequest, newAccessToken, false);
            log.debug("Session created with new access token for admin: {}", username);
            
            // 构建响应
            RefreshTokenResponse response = RefreshTokenResponse.builder()
                    .accessToken(newAccessToken)
                    .tokenType("Bearer")
                    .expiresIn(expiration)
                    .build();
            
            log.debug("Admin token refreshed successfully");
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("Error during admin token refresh process", e);
            return ApiResponse.error("刷新令牌过程中发生错误");
        }
    }

    private void saveLoginLog(String username, HttpServletRequest request, boolean success, String msg) {
        UserAgent userAgent = UserAgent.parseUserAgentString(request.getHeader("User-Agent"));
        
        LoginLog loginLog = new LoginLog();
        loginLog.setUsername(username);
        loginLog.setUserId(userMapper.findByUsername(username).getId());
        loginLog.setIpAddress(request.getRemoteAddr());
        loginLog.setBrowser(userAgent.getBrowser().getName());
        loginLog.setOs(userAgent.getOperatingSystem().getName());
        loginLog.setStatus(success ? 1 : 0);
        loginLog.setMsg(msg);
        loginLog.setLoginTime(LocalDateTime.now());
        
        loginLogMapper.insert(loginLog);
    }
} 