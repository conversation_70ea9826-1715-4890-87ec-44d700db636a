package com.modelbolt.blog.websocket;

import com.alibaba.fastjson2.JSON;
import com.modelbolt.blog.event.NotificationEvent;
import com.modelbolt.blog.utils.JwtUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
@RequiredArgsConstructor
public class NotificationWebSocketHandler extends TextWebSocketHandler {

    private final JwtUtils jwtUtils;
    
    // 用户ID -> WebSocket会话的映射
    private final Map<Long, WebSocketSession> userSessions = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        // 从token中获取用户ID
        String token = extractToken(session);
        if (token != null) {
            try {
                Long userId = jwtUtils.getUserIdFromToken(token);
                userSessions.put(userId, session);
                log.info("User {} connected to notification websocket", userId);
            } catch (Exception e) {
                log.error("Error establishing notification websocket connection", e);
            }
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        String token = extractToken(session);
        if (token != null) {
            try {
                Long userId = jwtUtils.getUserIdFromToken(token);
                userSessions.remove(userId);
                log.info("User {} disconnected from notification websocket", userId);
            } catch (Exception e) {
                log.error("Error closing notification websocket connection", e);
            }
        }
    }

    /**
     * 监听通知事件
     */
    @EventListener
    public void handleNotificationEvent(NotificationEvent event) {
        Long userId = event.getUserId();
        WebSocketSession session = userSessions.get(userId);
        
        if (session != null && session.isOpen()) {
            try {
                // 发送通知消息
                Map<String, Object> wsNotification = Map.of(
                    "type", "notification",
                    "notification", event.getNotification()
                );
                session.sendMessage(new TextMessage(JSON.toJSONString(wsNotification)));
                
                // 发送未读消息数量更新
                Map<String, Object> countUpdate = Map.of(
                    "type", "unread_count",
                    "count", event.getUnreadCount()
                );
                session.sendMessage(new TextMessage(JSON.toJSONString(countUpdate)));
                
                log.debug("Sent notification to user {}", userId);
            } catch (IOException e) {
                log.error("Error sending notification to user " + userId, e);
                userSessions.remove(userId);
            }
        }
    }

    /**
     * 从WebSocket会话中提取token
     */
    private String extractToken(WebSocketSession session) {
        String token = session.getUri().getQuery();
        if (token != null && token.startsWith("token=")) {
            return token.substring(6);
        }
        return null;
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        log.error("WebSocket transport error", exception);
        try {
            session.close(CloseStatus.SERVER_ERROR);
        } catch (IOException e) {
            log.error("Error closing websocket session after transport error", e);
        }
    }
} 