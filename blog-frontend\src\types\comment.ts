/**
 * 评论视图对象
 */
export interface CommentVO {
  /** 评论ID */
  id: number
  /** 评论内容 */
  content: string
  /** 文章ID */
  articleId: number
  /** 评论用户ID */
  userId: number
  /** 用户名 */
  userName: string
  /** 用户头像 */
  userAvatar?: string
  /** 父评论ID */
  parentId?: number
  /** 根评论ID */
  rootId?: number
  /** 回复数量 */
  replyCount: number
  /** 点赞数量 */
  likeCount: number
  /** 是否已点赞 */
  liked: boolean
  /** 创建时间 */
  createdAt: string
  /** 子评论列表 */
  children?: CommentVO[]
}

/**
 * 评论查询参数
 */
export interface CommentQuery {
  /** 页码 */
  page: number
  /** 每页大小 */
  size: number
}

/**
 * 评论表单数据
 */
export interface CommentForm {
  /** 文章ID */
  articleId: number
  /** 评论内容 */
  content: string
  /** 父评论ID */
  parentId?: number
  /** 根评论ID */
  rootId?: number
} 