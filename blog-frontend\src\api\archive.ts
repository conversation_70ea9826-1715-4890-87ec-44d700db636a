import {http} from '@/utils/request'
import type { ArchiveQuery, ArchiveYear, ArchiveStats } from '@/types/archive'
import type { R, PageResult } from '@/types/common'

// 获取归档列表
export function getArchives(params: ArchiveQuery) {
  return http<R<PageResult<ArchiveYear>>>({
    url: '/archives',
    method: 'get',
    params
  })
}

// 获取归档统计信息
export function getArchiveStats() {
  return http<R<ArchiveStats>>({
    url: '/archives/stats',
    method: 'get'
  })
} 