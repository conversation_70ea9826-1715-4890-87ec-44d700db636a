import { defineStore } from 'pinia'
import { ref, watch } from 'vue'

// 主题类型
type Theme = 'light' | 'dark'
// 字体大小
type FontSize = 'small' | 'medium' | 'large'

export const useThemeStore = defineStore('theme', () => {
  // 主题模式
  const theme = ref<Theme>(
    localStorage.getItem('theme') as Theme || 
    (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light')
  )
  
  // 字体大小
  const fontSize = ref<FontSize>(localStorage.getItem('fontSize') as FontSize || 'medium')
  
  // 监听系统主题变化
  const systemThemeQuery = window.matchMedia('(prefers-color-scheme: dark)')
  systemThemeQuery.addEventListener('change', (e) => {
    if (!localStorage.getItem('theme')) {
      theme.value = e.matches ? 'dark' : 'light'
    }
  })
  
  // 监听主题变化
  watch(theme, (newTheme) => {
    // 更新 HTML 的 class
    document.documentElement.classList.remove('light', 'dark')
    document.documentElement.classList.add(newTheme)
    // 保存到本地存储
    localStorage.setItem('theme', newTheme)
  }, { immediate: true })
  
  // 监听字体大小变化
  watch(fontSize, (newSize) => {
    // 更新根元素的 class
    document.documentElement.classList.remove('text-small', 'text-medium', 'text-large')
    document.documentElement.classList.add(`text-${newSize}`)
    // 保存到本地存储
    localStorage.setItem('fontSize', newSize)
  }, { immediate: true })
  
  return {
    theme,
    fontSize
  }
}) 