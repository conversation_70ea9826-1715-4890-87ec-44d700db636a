package com.modelbolt.blog.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.HandshakeInterceptor;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;
import com.modelbolt.blog.websocket.OnlineStatsWebSocketHandler;
import com.modelbolt.blog.websocket.NotificationWebSocketHandler;
import com.modelbolt.blog.utils.JwtUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

@Configuration
@EnableWebSocket
@Profile("!test") // 在测试环境中不加载此配置
// WebSocket配置类，实现WebSocketConfigurer接口以配置WebSocket处理程序
public class WebSocketConfig implements WebSocketConfigurer {

    private final OnlineStatsWebSocketHandler onlineStatsWebSocketHandler;
    private final NotificationWebSocketHandler notificationWebSocketHandler;
    private final JwtUtils jwtUtils;
    private static final Logger log = LoggerFactory.getLogger(WebSocketConfig.class);

    // 构造函数，接收OnlineStatsWebSocketHandler和NotificationWebSocketHandler实例
    public WebSocketConfig(OnlineStatsWebSocketHandler onlineStatsWebSocketHandler,
                         NotificationWebSocketHandler notificationWebSocketHandler,
                         JwtUtils jwtUtils) {
        this.onlineStatsWebSocketHandler = onlineStatsWebSocketHandler;
        this.notificationWebSocketHandler = notificationWebSocketHandler;
        this.jwtUtils = jwtUtils;
    }

    // 重写registerWebSocketHandlers方法，注册WebSocket处理程序
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 添加握手拦截器进行认证
        HandshakeInterceptor authInterceptor = createAuthInterceptor();

        // 在线状态WebSocket（全站）
        registry.addHandler(onlineStatsWebSocketHandler, "/ws/online")
                .addInterceptors(authInterceptor)
                .setAllowedOrigins("*");
        
        // 通知WebSocket（博客前台）
        registry.addHandler(notificationWebSocketHandler, "/ws/notifications")
                .addInterceptors(authInterceptor)
                .setAllowedOrigins("*");
    }

    // 创建ServerEndpointExporter实例，用于扫描并注册使用@ServerEndpoint注解的类
    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }

    private HandshakeInterceptor createAuthInterceptor() {
        return new HandshakeInterceptor() {
            @Override
            public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                         WebSocketHandler wsHandler, Map<String, Object> attributes) {
                try {
                    // 从URL参数中获取token
                    String query = request.getURI().getQuery();
                    if (query == null || !query.contains("token=")) {
                        log.error("WebSocket handshake failed: Missing token parameter");
                        return false;
                    }
                    
                    // 解析token参数
                    String token = null;
                    String[] params = query.split("&");
                    for (String param : params) {
                        if (param.startsWith("token=")) {
                            token = param.substring(6);
                            break;
                        }
                    }
                    
                    if (token == null) {
                        log.error("WebSocket handshake failed: Token not found in parameters");
                        return false;
                    }

                    // 验证token
                    if (jwtUtils.validateToken(token)) {
                        // 将用户信息存储在attributes中，供WebSocketHandler使用
                        Long userId = jwtUtils.extractUserId(token);
                        String username = jwtUtils.extractUsername(token);
                        
                        if (userId == null || username == null) {
                            log.error("WebSocket handshake failed: Invalid user information in token");
                            return false;
                        }
                        
                        attributes.put("userId", userId);
                        attributes.put("username", username);
                        log.info("WebSocket handshake successful for user: {}", username);
                        return true;
                    } else {
                        log.error("WebSocket handshake failed: Invalid token");
                        return false;
                    }
                } catch (Exception e) {
                    log.error("WebSocket handshake failed with error", e);
                    return false;
                }
            }

            @Override
            public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                     WebSocketHandler wsHandler, Exception exception) {
                // 握手后的处理，这里不需要做什么
            }
        };
    }
}
