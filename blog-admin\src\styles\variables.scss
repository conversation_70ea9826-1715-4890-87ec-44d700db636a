// 颜色变量
$primary-color: var(--primary-color);
$success-color: var(--success-color);
$warning-color: var(--warning-color);
$error-color: var(--error-color);
$heading-color: var(--heading-color);
$text-color: var(--text-color);
$text-color-secondary: var(--text-color-secondary);
$disabled-color: var(--disabled-color);
$border-color: var(--border-color);
$box-shadow: var(--box-shadow);

// 布局变量
$header-height: 64px;
$sidebar-width: 256px;
$sidebar-collapsed-width: 80px;

// 响应式断点
$screen-xs: 480px;
$screen-sm: 576px;
$screen-md: 768px;
$screen-lg: 992px;
$screen-xl: 1200px;
$screen-xxl: 1600px;

// 间距
$spacing-unit: 4px;
$spacing-xs: $spacing-unit * 1;  // 4px
$spacing-sm: $spacing-unit * 2;  // 8px
$spacing-md: $spacing-unit * 4;  // 16px
$spacing-lg: $spacing-unit * 6;  // 24px
$spacing-xl: $spacing-unit * 8;  // 32px

// 字体大小
$font-size-base: 14px;
$font-size-sm: 12px;
$font-size-lg: 16px;
$font-size-xl: 18px;
$font-size-xxl: 20px;

// 圆角
$border-radius-base: 2px;
$border-radius-sm: 1px;
$border-radius-lg: 4px;

// 动画
$animation-duration-base: 0.2s;
$animation-duration-slow: 0.3s;
$animation-duration-fast: 0.1s;

// z-index
$zindex-dropdown: 1050;
$zindex-sticky: 1020;
$zindex-fixed: 1030;
$zindex-modal-mask: 1000;
$zindex-modal: 1000;
$zindex-popover: 1030;
$zindex-tooltip: 1070;
$zindex-message: 1090; 