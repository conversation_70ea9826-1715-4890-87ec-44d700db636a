package com.modelbolt.blog.model.dto.comment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "用户评论视图对象")
public class UserCommentVO {
    @Schema(description = "评论ID")
    private Long id;

    @Schema(description = "评论内容")
    private String content;

    @Schema(description = "文章ID")
    private Long articleId;

    @Schema(description = "文章标题")
    private String articleTitle;

    @Schema(description = "评论状态：0-待审核，1-已通过，2-已拒绝")
    private Integer status;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
} 