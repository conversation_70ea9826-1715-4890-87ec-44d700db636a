import { http } from '@/utils/request'
import type { R, PageResult } from '@/types/common'
import type { CommentVO, CommentQuery, CommentAuditDTO } from '@/types/comment'

export const commentApi = {
  /**
   * 获取评论列表
   */
  getList(params: CommentQuery) {
    return http.get<R<PageResult<CommentVO>>>('/admin/comment/list', { params })
  },

  /**
   * 审核评论
   */
  audit(id: number, data: CommentAuditDTO) {
    return http.put<R<void>>(`/admin/comment/${id}/audit`, data)
  },

  /**
   * 删除评论
   */
  delete(id: number) {
    return http.delete<R<void>>(`/admin/comment/${id}`)
  },

  /**
   * 批量删除评论
   */
  batchDelete(ids: number[]) {
    return http.delete<R<void>>('/admin/comment/batch', { data: ids })
  }
} 