{"name": "blog-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@kangc/v-md-editor": "^2.3.18", "@types/highlight.js": "^10.1.0", "@types/js-cookie": "^3.0.6", "@types/prismjs": "^1.26.5", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "^12.5.0", "axios": "^1.7.9", "element-plus": "^2.6.0", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "markdown-it": "^14.1.0", "md-editor-v3": "^5.2.1", "pinia": "^2.1.7", "prismjs": "^1.29.0", "qrcode.vue": "^3.6.0", "vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node20": "^20.1.2", "@types/jsdom": "^21.1.6", "@types/markdown-it": "^14.1.2", "@types/node": "^20.11.25", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.4", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.17", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "jsdom": "^24.0.0", "npm-run-all2": "^6.1.1", "postcss": "^8.4.35", "prettier": "^3.0.3", "sass": "^1.71.1", "tailwindcss": "^3.4.1", "typescript": "~5.3.0", "vite": "^5.1.5", "vitest": "^1.3.1", "vue-tsc": "^1.8.27"}}