import type { PageParams } from './common'

export interface CommentVO {
  id: number
  content: string
  articleId: number
  articleTitle: string
  userId: number
  userName: string
  userAvatar: string
  status: number
  replyCount: number
  likeCount: number
  createdAt: string
  updatedAt: string
}

export interface CommentQuery extends PageParams {
  status?: number
  articleId?: number
  userId?: number
}

export interface CommentAuditDTO {
  status: number
  reason?: string
}

export const CommentStatus = {
  PENDING: 0,
  APPROVED: 1,
  REJECTED: 2
} as const 