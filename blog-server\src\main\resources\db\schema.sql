-- 删除数据库如果存在
DROP DATABASE IF EXISTS modelbolt_blog;

-- 创建数据库
CREATE DATABASE modelbolt_blog DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE modelbolt_blog;
-- ============================================= 用户管理相关表 =============================================
-- 用户表
CREATE TABLE `user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `bio` varchar(500) DEFAULT NULL COMMENT '个人简介',
  `role` varchar(20) NOT NULL DEFAULT 'USER' COMMENT '角色:USER,ADMIN',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 用户配置表
CREATE TABLE `user_settings` (
  `user_id` bigint NOT NULL,
  `theme` varchar(20) DEFAULT 'light' COMMENT '主题设置',
  `notification_enabled` tinyint(1) DEFAULT '1' COMMENT '通知开关',
  `language` varchar(10) DEFAULT 'zh_CN' COMMENT '语言设置',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户配置表';

-- 用户关注表
CREATE TABLE `user_follow` (
  `follower_id` bigint NOT NULL COMMENT '关注者ID',
  `following_id` bigint NOT NULL COMMENT '被关注者ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`follower_id`,`following_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户关注表';

-- 登录日志表
CREATE TABLE IF NOT EXISTS `login_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `browser` varchar(50) DEFAULT NULL COMMENT '浏览器类型',
  `os` varchar(50) DEFAULT NULL COMMENT '操作系统',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0失败 1成功',
  `msg` varchar(255) DEFAULT NULL COMMENT '提示信息',
  `login_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录日志表';


-- ============================================= 内容管理相关表 =============================================
-- 文章表
CREATE TABLE `article` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL COMMENT '标题',
  `content` longtext NOT NULL COMMENT '内容',
  `summary` varchar(500) DEFAULT NULL COMMENT '文章摘要',
  `cover` varchar(255) DEFAULT NULL COMMENT '封面图片URL',
  `author_id` bigint NOT NULL COMMENT '作者ID',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态:0草稿,1发布,2下架',
  `view_count` int DEFAULT '0' COMMENT '浏览量',
  `like_count` int DEFAULT '0' COMMENT '点赞数',
  `favorite_count` int DEFAULT '0' COMMENT '收藏数',
  `comment_count` int DEFAULT '0' COMMENT '评论数',
  `is_top` tinyint(1) DEFAULT '0' COMMENT '是否置顶',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_author` (`author_id`),
  FULLTEXT KEY `idx_title_content` (`title`,`content`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章表';

-- 分类表
CREATE TABLE `category` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `parent_id` bigint DEFAULT NULL COMMENT '父分类ID',
  `order_num` int DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类表';

-- 标签表
CREATE TABLE `tag` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';

-- 文章分类关联表
CREATE TABLE `article_category` (
  `article_id` bigint NOT NULL,
  `category_id` bigint NOT NULL,
  PRIMARY KEY (`article_id`,`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章分类关联表';

-- 文章标签关联表
CREATE TABLE `article_tag` (
  `article_id` bigint NOT NULL,
  `tag_id` bigint NOT NULL,
  PRIMARY KEY (`article_id`,`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章标签关联表';

-- 访问历史记录表
CREATE TABLE `visit_history` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `visit_date` date NOT NULL COMMENT '访问日期',
  `visit_count` int NOT NULL DEFAULT '0' COMMENT '访问次数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_visit_date` (`visit_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='访问历史记录表';


-- ============================================= 互动管理相关表 =============================================
-- 评论表
CREATE TABLE `comment` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `content` text NOT NULL COMMENT '评论内容',
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `user_id` bigint NOT NULL COMMENT '评论用户ID',
  `parent_id` bigint DEFAULT NULL COMMENT '父评论ID',
  `root_id` bigint DEFAULT NULL COMMENT '根评论ID',
  `status` tinyint DEFAULT '0' COMMENT '状态:0待审核,1通过,2拒绝',
  `like_count` int DEFAULT '0' COMMENT '点赞数',
  `reply_count` int DEFAULT '0' COMMENT '回复数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_article` (`article_id`),
  KEY `idx_user` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

-- 评论点赞表
CREATE TABLE `comment_like` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `comment_id` bigint NOT NULL COMMENT '评论ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_comment_user` (`comment_id`, `user_id`),
  KEY `idx_user` (`user_id`),
  CONSTRAINT `fk_comment_like_comment` FOREIGN KEY (`comment_id`) REFERENCES `comment` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_comment_like_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论点赞表';

-- 用户点赞表
CREATE TABLE `user_like` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '点赞时间',
  PRIMARY KEY (`user_id`,`article_id`),
  KEY `idx_article_user` (`article_id`,`user_id`),
  CONSTRAINT `fk_user_like_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_like_article` FOREIGN KEY (`article_id`) REFERENCES `article` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户点赞表';

-- 用户收藏表
CREATE TABLE `user_favorite` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  PRIMARY KEY (`user_id`,`article_id`),
  KEY `idx_article_user` (`article_id`,`user_id`),
  CONSTRAINT `fk_user_favorite_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_favorite_article` FOREIGN KEY (`article_id`) REFERENCES `article` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏表';

-- 分享记录表
CREATE TABLE `share_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `user_id` bigint DEFAULT NULL COMMENT '分享用户ID(未登录为空)',
  `platform` varchar(20) NOT NULL COMMENT '分享平台:WECHAT,WEIBO,LINK,POSTER',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(200) DEFAULT NULL COMMENT '浏览器信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_article` (`article_id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_share_article` FOREIGN KEY (`article_id`) REFERENCES `article` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_share_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分享记录表';

-- 通知表
CREATE TABLE `notification` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '接收通知的用户ID',
  `sender_id` bigint DEFAULT NULL COMMENT '发送者ID',
  `type` varchar(20) NOT NULL COMMENT '通知类型:COMMENT,REPLY,LIKE,SYSTEM',
  `target_id` bigint NOT NULL COMMENT '目标ID(文章ID或评论ID)',
  `target_type` varchar(20) NOT NULL COMMENT '目标类型:ARTICLE,COMMENT',
  `content` text NOT NULL COMMENT '通知内容',
  `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_read` (`user_id`, `is_read`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_notification_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知表';


-- ============================================= 打赏相关表 =============================================
-- 打赏配置表
CREATE TABLE `reward_config` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用打赏',
  `amounts` json DEFAULT NULL COMMENT '打赏金额选项',
  `qrcode_wechat` varchar(255) DEFAULT NULL COMMENT '微信收款码',
  `qrcode_alipay` varchar(255) DEFAULT NULL COMMENT '支付宝收款码',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  CONSTRAINT `fk_reward_config_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='打赏配置表';

-- 打赏记录表
CREATE TABLE `article_reward` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `user_id` bigint NOT NULL COMMENT '打赏用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '打赏金额',
  `payment_type` varchar(20) NOT NULL COMMENT '支付方式:WECHAT,ALIPAY',
  `payment_status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '支付状态:PENDING,SUCCESS,FAILED',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '支付平台交易号',
  `message` varchar(200) DEFAULT NULL COMMENT '打赏留言',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_article_user` (`article_id`,`user_id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_reward_article` FOREIGN KEY (`article_id`) REFERENCES `article` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_reward_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='打赏记录表';


-- ============================================= 系统管理相关表 =============================================
-- 系统配置表
CREATE TABLE `system_config` (
  `key` varchar(50) NOT NULL COMMENT '配置键',
  `value` text COMMENT '配置值',
  `description` varchar(200) DEFAULT NULL COMMENT '配置描述',
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 操作日志表
CREATE TABLE `operation_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL COMMENT '操作用户ID',
  `operation` varchar(50) NOT NULL COMMENT '操作类型',
  `method` varchar(200) NOT NULL COMMENT '请求方法',
  `params` text COMMENT '请求参数',
  `ip` varchar(64) DEFAULT NULL COMMENT '操作IP',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 管理员设置表
CREATE TABLE IF NOT EXISTS `admin_settings` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `admin_id` bigint NOT NULL COMMENT '管理员ID',
    `email_verification_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用邮箱验证',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_admin_id` (`admin_id`),
    CONSTRAINT `fk_admin_settings_user` FOREIGN KEY (`admin_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员设置表';

-- ============================================= AI服务相关表 =============================================
-- AI 对话历史表
CREATE TABLE `ai_chat_history` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `session_id` varchar(50) NOT NULL COMMENT '会话ID',
  `role` varchar(20) NOT NULL COMMENT '角色:user,assistant',
  `content` text NOT NULL COMMENT '对话内容',
  `tokens` int DEFAULT '0' COMMENT 'token数量',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_session` (`user_id`,`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI对话历史表';

-- AI 使用统计表
CREATE TABLE `ai_usage_stats` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `date` date NOT NULL COMMENT '统计日期',
  `total_tokens` int DEFAULT '0' COMMENT '总token数量',
  `total_requests` int DEFAULT '0' COMMENT '总请求次数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_date` (`user_id`,`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI使用统计表';

-- --------------------------------------------- 初始化数据 ---------------------------------------------

-- 初始化分类数据
INSERT INTO `category` (id, name, parent_id, order_num) VALUES
(1, '技术', NULL, 1),
(2, '前端', 1, 1),
(3, '后端', 1, 2),
(4, '运维', 1, 3),
(5, 'AI', 1, 4),
(6, '生活', NULL, 2),
(7, '美食', 6, 1),
(8, '旅行', 6, 2),
(9, '阅读', 6, 3),
(10, '随笔', NULL, 3);

-- 初始化标签数据
INSERT INTO `tag` (id, name) VALUES
(1, 'Vue3'),
(2, 'React'),
(3, 'Spring Boot'),
(4, 'Java'),
(5, 'Python'),
(6, 'Docker'),
(7, 'Kubernetes'),
(8, 'AI'),
(9, 'ChatGPT'),
(10, '架构设计'),
(11, '性能优化'),
(12, '微服务'),
(13, '美食探店'),
(14, '读书笔记'),
(15, '旅行日记');

-- 初始化文章标签关联
INSERT INTO `article_tag` (article_id, tag_id) VALUES
(1, 8), (1, 9), (1, 10),
(2, 3), (2, 4), (2, 12),
(3, 1), (3, 11),
(4, 2), (4, 11),
(5, 10), (5, 11), (5, 12),
(6, 3), (6, 4), (6, 11),
(7, 15),
(8, 13),
(9, 14),
(10, 3), (10, 12),
(11, 14),
(12, 13);

-- 初始化文章分类关联
INSERT INTO `article_category` (article_id, category_id) VALUES
(1, 1), (1, 5),
(2, 1), (2, 3),
(3, 1), (3, 2),
(4, 1), (4, 2),
(5, 1),
(6, 1), (6, 3),
(7, 6), (7, 8),
(8, 6), (8, 7),
(9, 6), (9, 9),
(10, 1), (10, 3),
(11, 6), (11, 9),
(12, 6), (12, 7);

-- 初始化评论数据
INSERT INTO `comment` (content, article_id, user_id, status, created_at) VALUES
('写得非常好，对AI技术发展的预测很有见地', 1, 2, 1, DATE_SUB(NOW(), INTERVAL 1 HOUR)),
('Spring Boot 3.0的性能提升确实很明显', 2, 1, 1, DATE_SUB(NOW(), INTERVAL 2 HOUR)),
('Vue3的优化技巧分享得很实用', 3, 1, 1, DATE_SUB(NOW(), INTERVAL 3 HOUR)),
('React 19的新特性解析得很透彻', 4, 2, 1, DATE_SUB(NOW(), INTERVAL 4 HOUR)),
('总结得很全面，期待更多分享', 5, 1, 1, DATE_SUB(NOW(), INTERVAL 5 HOUR));

-- 初始化用户关注关系
INSERT INTO `user_follow` (follower_id, following_id) VALUES
(2, 1), (1, 2);

-- 初始化AI对话历史
INSERT INTO `ai_chat_history` (user_id, session_id, role, content, tokens) VALUES
(1, 'session1', 'user', '如何优化Vue3应用性能？', 10),
(1, 'session1', 'assistant', '以下是一些Vue3应用性能优化的建议：\n1. 使用computed代替复杂的模板表达式\n2. 合理使用v-show和v-if\n3. 使用keep-alive缓存组件\n4. 异步组件和路由懒加载', 50),
(2, 'session2', 'user', '介绍一下Spring Boot 3新特性', 12),
(2, 'session2', 'assistant', 'Spring Boot 3的主要新特性包括：\n1. 支持Java 17\n2. 原生镜像支持\n3. 性能优化\n4. 依赖升级', 45);

-- 初始化AI使用统计
INSERT INTO `ai_usage_stats` (user_id, date, total_tokens, total_requests) VALUES
(1, CURDATE(), 1000, 50),
(2, CURDATE(), 500, 25);

-- 初始化文章数据
INSERT INTO article (id, title, content, summary, cover, author_id, status, view_count, like_count, favorite_count, comment_count, is_top, created_at) VALUES
(1, '深度学习框架对比分析', '# 深度学习框架性能对比

## 主流框架介绍

### 1. PyTorch
- 动态计算图
- Python优先
- 研究友好

### 2. TensorFlow
- 静态计算图
- 生产部署优势
- 完整生态

### 3. JAX
```python
# JAX示例代码
import jax.numpy as jnp
from jax import grad, jit

def predict(params, x):
    return jnp.dot(x, params)

# 自动求导
grad_fn = grad(predict)
```

## 性能测试
1. 训练速度
2. 推理延迟
3. 显存占用

## 框架选择建议
* 研究原型：PyTorch
* 生产环境：TensorFlow
* 高性能计算：JAX

> 注意：框架选择要根据具体场景', 
'深度学习主流框架对比分析', 'https://picsum.photos/800/400?random=1', 1, 1, 300, 30, 12, 5, 1, DATE_SUB(NOW(), INTERVAL 1 DAY)),

(2, '容器化部署实践', '# Docker与Kubernetes最佳实践

## Docker基础
### 镜像优化
```dockerfile
# 多阶段构建示例
FROM node:alpine as builder
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
```

### 容器安全
1. 最小权限原则
2. 镜像漏洞扫描
3. 运行时保护

## Kubernetes部署
- 资源限制
- 健康检查
- 滚动更新

## 监控方案
* Prometheus
* Grafana
* ELK Stack

## 最佳实践
1. 合理规划资源
2. 灰度发布策略
3. 备份与恢复', 
'容器化部署最佳实践指南', 'https://picsum.photos/800/400?random=2', 1, 1, 250, 25, 10, 3, 0, DATE_SUB(NOW(), INTERVAL 2 DAY)),

(3, '微服务架构设计', '# 微服务架构设计指南

## 1. 服务拆分
### 领域驱动设计
- 界限上下文
- 聚合根
- 领域事件

### 服务粒度
```mermaid
graph TD
    A[单体应用] --> B[粗粒度服务]
    B --> C[细粒度服务]
    C --> D[微服务]
```

## 2. 通信方式
1. 同步通信
   - REST
   - gRPC
2. 异步通信
   - 消息队列
   - 事件总线

## 3. 数据管理
* 数据一致性
* CAP理论
* CQRS模式

## 4. 服务治理
- 服务注册
- 负载均衡
- 熔断降级

> 微服务不是银弹，需要根据业务场景选择', 
'微服务架构设计与实践指南', 'https://picsum.photos/800/400?random=3', 1, 1, 280, 28, 11, 4, 0, DATE_SUB(NOW(), INTERVAL 3 DAY)),

(4, '前端性能优化实战', '# 前端性能优化实战指南

## 1. 加载优化
### 资源压缩
```javascript
// webpack配置示例
module.exports = {
  optimization: {
    minimize: true,
    splitChunks: {
      chunks: "all",
      minSize: 20000
    }
    }
}
```

### 懒加载策略
- 路由懒加载
- 组件懒加载
- 图片懒加载

## 2. 渲染优化
1. 虚拟列表
2. 防抖节流
3. Web Workers

## 3. 缓存策略
* 浏览器缓存
* Service Worker
* CDN缓存

## 4. 监控分析
- Performance API
- Lighthouse
- 性能指标', 
'前端性能优化技巧总结', 'https://picsum.photos/800/400?random=4', 1, 1, 320, 32, 13, 6, 1, DATE_SUB(NOW(), INTERVAL 4 DAY)),

(5, '数据库优化指南', '# MySQL性能优化指南

## 1. 索引优化
### 索引设计原则
- 最左前缀原则
- 索引选择性
- 覆盖索引

### 常见问题
```sql
-- 优化前
SELECT * FROM users 
WHERE age > 20 
  AND status = 1;

-- 优化后
CREATE INDEX idx_age_status 
ON users(age, status);
```

## 2. 查询优化
1. 避免全表扫描
2. 合理使用JOIN
3. 子查询优化

## 3. 配置优化
* 缓冲池设置
* 并发参数
* 日志配置

## 4. 监控分析
- 慢查询日志
- 执行计划
- 性能指标

> 优化建议仅供参考，具体需要根据实际情况调整', 
'MySQL数据库性能优化实践', 'https://picsum.photos/800/400?random=5', 1, 1, 290, 29, 12, 4, 0, DATE_SUB(NOW(), INTERVAL 5 DAY)),

(6, '云原生架构实践', '# 云原生应用架构指南

## 1. 云原生概念
### 核心理念
- 不可变基础设施
- 声明式API
- 自动化运维

### 技术栈
```yaml
# Kubernetes配置示例
apiVersion: apps/v1
kind: Deployment
metadata:
  name: myapp
spec:
  replicas: 3
  selector:
    matchLabels:
      app: myapp
  template:
    metadata:
      labels:
        app: myapp
    spec:
      containers:
      - name: myapp
        image: myapp:1.0
```

## 2. 服务网格
1. Istio架构
2. 流量管理
3. 安全策略

## 3. 可观测性
* 分布式追踪
* 日志聚合
* 监控告警

## 4. DevOps实践
- CI/CD流水线
- GitOps工作流
- 自动化测试

> 云原生转型是一个渐进的过程，需要团队共同努力', 
'云原生应用开发与运维指南', 'https://picsum.photos/800/400?random=6', 1, 1, 270, 27, 11, 3, 0, DATE_SUB(NOW(), INTERVAL 6 DAY)),

(7, '函数式编程指南', '# 函数式编程实战指南

## 1. 基础概念
### 纯函数
```javascript
// 纯函数示例
const add = (a, b) => a + b;

// 非纯函数
let count = 0;
const increment = () => ++count;
```

### 不可变性
- 避免状态修改
- 使用新对象
- 数据转换

## 2. 高阶函数
1. map/reduce/filter
2. 函数组合
3. 柯里化

## 3. 函子与单子
* Maybe函子
* Either函子
* IO单子

## 4. 实践技巧
- 类型推导
- 模式匹配
- 递归优化

> 函数式编程能带来更可靠的代码', 
'函数式编程核心概念与实践', 'https://picsum.photos/800/400?random=7', 1, 1, 240, 24, 10, 2, 0, DATE_SUB(NOW(), INTERVAL 7 DAY)),

(8, '系统设计模式', '# 软件设计模式实战

## 1. 创建型模式
### 工厂方法
```java
// 工厂方法示例
public interface Product {}

public class ConcreteProduct implements Product {}

public interface Factory {
    Product createProduct();
}

public class ConcreteFactory implements Factory {
    @Override
    public Product createProduct() {
        return new ConcreteProduct();
    }
}
```

## 2. 结构型模式
1. 适配器模式
2. 装饰器模式
3. 代理模式

## 3. 行为型模式
* 观察者模式
* 策略模式
* 命令模式

## 4. 实践建议
- 设计原则
- 模式选择
- 反模式避免

> 设计模式是经验的总结，不是教条', 
'常用设计模式详解与实践', 'https://picsum.photos/800/400?random=8', 1, 1, 260, 26, 10, 3, 0, DATE_SUB(NOW(), INTERVAL 8 DAY)),

(9, 'GraphQL实战指南', '# GraphQL API开发指南

## 1. 基础概念
### Schema定义
```graphql
type User {
  id: ID!
  name: String!
  posts: [Post!]!
}

type Post {
  id: ID!
  title: String!
  content: String!
  author: User!
}

type Query {
  user(id: ID!): User
  posts: [Post!]!
}
```

## 2. 解析器实现
1. 字段解析
2. 数据加载
3. 错误处理

## 3. 性能优化
* 数据加载优化
* 缓存策略
* N+1问题解决

## 4. 最佳实践
- 类型设计
- 安全考虑
- 版本管理

> GraphQL提供了更灵活的API设计方案', 
'GraphQL API设计与开发实践', 'https://picsum.photos/800/400?random=9', 1, 1, 230, 23, 9, 2, 0, DATE_SUB(NOW(), INTERVAL 9 DAY)),

(10, '响应式编程', '# 响应式编程实战指南

## 1. 响应式流
### 基本概念
- Publisher
- Subscriber
- Subscription
- Processor

### 示例代码
```java
Flux.range(1, 5)
    .map(i -> i * 2)
    .filter(i -> i > 5)
    .subscribe(
        System.out::println,
        error -> error.printStackTrace(),
        () -> System.out.println("Completed")
    );
```

## 2. 操作符
1. 转换操作
2. 过滤操作
3. 组合操作

## 3. 错误处理
* retry机制
* fallback策略
* 错误恢复

## 4. 实践技巧
- 背压处理
- 并发控制
- 测试方法

> 响应式编程适合处理异步数据流', 
'响应式编程原理与实践', 'https://picsum.photos/800/400?random=10', 1, 1, 210, 21, 8, 2, 0, DATE_SUB(NOW(), INTERVAL 10 DAY)),

(11, '咖啡爱好者指南', '# 咖啡入门到精通

## 1. 认识咖啡豆
### 产地特点
- 埃塞俄比亚：花香果酸
- 哥伦比亚：平衡醇厚
- 肯尼亚：浆果香气

### 烘焙程度
```markdown
| 烘焙度 | 特点 | 适合品类 |
|--------|------|----------|
| 浅烘焙 | 酸味明显 | 手冲 |
| 中烘焙 | 平衡均匀 | 意式 |
| 深烘焙 | 苦香浓郁 | 摩卡 |
```

## 2. 冲煮方法
1. 手冲咖啡
   - V60
   - 凯膜
   - 爱乐压
2. 意式咖啡
   - 浓缩
   - 拿铁
   - 卡布奇诺

## 3. 器具选择
* 磨豆机
* 温控壶
* 滤纸

## 4. 进阶技巧
- 水温控制
- 研磨细度
- 萃取时间

> 好咖啡需要耐心与专注', 
'咖啡知识与冲煮技巧指南', 'https://picsum.photos/800/400?random=11', 1, 1, 180, 18, 7, 2, 0, DATE_SUB(NOW(), INTERVAL 11 DAY)),

(12, '城市摄影指南', '# 城市摄影技巧分享

## 1. 前期准备
### 器材选择
- 相机：全画幅/APS-C
- 镜头：广角/标准/长焦
- 脚架与滤镜

### 拍摄时机
```markdown
#### 黄金时间
* 清晨：柔和晨光
* 黄昏：温暖逆光
* 蓝调时分：城市点灯
```

## 2. 构图技巧
1. 引导线条
2. 对称平衡
3. 框架构图

## 3. 后期处理
* 曝光调整
* 色彩管理
* 局部修饰

## 4. 创作主题
- 建筑剪影
- 街头即景
- 人文故事

> 城市摄影记录生活之美', 
'城市摄影入门指南', 'https://picsum.photos/800/400?random=12', 1, 1, 160, 16, 6, 2, 0, DATE_SUB(NOW(), INTERVAL 12 DAY)),

(13, '居家烘焙教程', '# 居家烘焙入门指南

## 1. 基础知识
### 常用工具
- 烤箱
- 厨师机
- 量具

### 食材选择
```markdown
#### 面粉类型
| 类型 | 蛋白质含量 | 适用范围 |
|------|------------|----------|
| 高筋 | 12-14%     | 面包     |
| 中筋 | 9-11%      | 蛋糕     |
| 低筋 | 7-8%       | 饼干     |
```

## 2. 入门配方
1. 戚风蛋糕
2. 曲奇饼干
3. 牛角面包

## 3. 烘焙技巧
* 温度控制
* 时间把握
* 搅拌手法

## 4. 常见问题
- 蛋糕回缩
- 饼干变形
- 面包不膨胀

> 烘焙是一门需要耐心的艺术', 
'居家烘焙基础教程', 'https://picsum.photos/800/400?random=13', 1, 1, 150, 15, 6, 2, 0, DATE_SUB(NOW(), INTERVAL 13 DAY)),

(14, '插花艺术入门', '# 花艺设计基础教程

## 1. 基础知识
### 花材认识
- 主花：玫瑰、百合
- 配花：满天星、尤加利
- 绿叶：散尾葵、绿萝

### 工具准备
```markdown
#### 必备工具
1. 花剪
2. 花泥
3. 花瓶
4. 保鲜剂
```

## 2. 插花技法
1. 基本形式
   - 直立式
   - 倾斜式
   - 自然式
2. 空间处理
   - 高度层次
   - 疏密对比
   - 色彩搭配

## 3. 养护技巧
* 修剪方法
* 换水频率
* 环境控制

## 4. 主题创作
- 餐桌摆件
- 节日装饰
- 办公摆设

> 插花艺术让生活更优雅', 
'花艺设计与插花技巧', 'https://picsum.photos/800/400?random=14', 1, 1, 140, 14, 5, 2, 0, DATE_SUB(NOW(), INTERVAL 14 DAY)),

(15, '家庭园艺指南', '# 阳台种植入门指南

## 1. 基础准备
### 环境评估
- 光照条件
- 温度湿度
- 通风情况

### 工具材料
```markdown
#### 基本配置
* 花盆/种植箱
* 营养土
* 园艺工具
* 肥料水壶
```

## 2. 植物选择
1. 观叶植物
   - 绿萝
   - 常春藤
   - 龟背竹
2. 观花植物
   - 长春花
   - 茉莉花
   - 三色堇

## 3. 养护要点
* 浇水技巧
* 施肥方法
* 修剪整形

## 4. 常见问题
- 黄叶脱落
- 病虫防治
- 徒长处理

> 园艺让生活充满绿意', 
'家庭园艺种植指南', 'https://picsum.photos/800/400?random=15', 1, 1, 130, 13, 5, 2, 0, DATE_SUB(NOW(), INTERVAL 15 DAY));

-- 初始化系统配置
INSERT INTO `system_config` (`key`, `value`, `description`)
VALUES 
('siteName', 'ModelBolt Blog', '站点名称'),
('siteDescription', 'A modern blog system powered by AI', '站点描述'),
('siteKeywords', 'blog,ai,writing', '站点关键词'),
('siteUrl', 'http://localhost:8080', '站点地址'),
('smtpHost', '', 'SMTP服务器地址'),
('smtpPort', '587', 'SMTP服务器端口'),
('smtpUsername', '', 'SMTP用户名'),
('smtpPassword', '', 'SMTP密码'),
('smtpFrom', '', '发件人地址'),
('aiModel', '', 'AI模型配置'),
('aiApiKey', '', 'AI API密钥'),
('aiApiEndpoint', '', 'AI API地址'),
('aiMaxTokens', '2000', '最大Token数'),
('aiTemperature', '0.7', 'AI温度参数'),
('aiRequestLimit', '1000', '每日请求限制'),
('aiTokenLimit', '100000', '每日Token限制'),
('securityLoginAttempts', '5', '登录失败次数限制'),
('securityLockDuration', '30', '账号锁定时间(分钟)');

-- 初始化访问历史数据（最近7天）
INSERT INTO `visit_history` (visit_date, visit_count, created_at) VALUES
(DATE_SUB(CURDATE(), INTERVAL 1 DAY), 156, DATE_SUB(NOW(), INTERVAL 1 DAY)),
(DATE_SUB(CURDATE(), INTERVAL 2 DAY), 142, DATE_SUB(NOW(), INTERVAL 2 DAY)),
(DATE_SUB(CURDATE(), INTERVAL 3 DAY), 187, DATE_SUB(NOW(), INTERVAL 3 DAY)),
(DATE_SUB(CURDATE(), INTERVAL 4 DAY), 198, DATE_SUB(NOW(), INTERVAL 4 DAY)),
(DATE_SUB(CURDATE(), INTERVAL 5 DAY), 165, DATE_SUB(NOW(), INTERVAL 5 DAY)),
(DATE_SUB(CURDATE(), INTERVAL 6 DAY), 134, DATE_SUB(NOW(), INTERVAL 6 DAY)),
(DATE_SUB(CURDATE(), INTERVAL 7 DAY), 178, DATE_SUB(NOW(), INTERVAL 7 DAY));