package com.modelbolt.blog.aspect;

import com.modelbolt.blog.annotation.OperationLog;
import com.modelbolt.blog.mapper.OperationLogMapper;
import com.modelbolt.blog.utils.SecurityUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import java.lang.reflect.Method;

@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class OperationLogAspect {

    private final OperationLogMapper operationLogMapper;

    @Around("@annotation(com.modelbolt.blog.annotation.OperationLog)")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        long beginTime = System.currentTimeMillis();
        Object result = point.proceed();
        long time = System.currentTimeMillis() - beginTime;
        recordLog(point, time);
        return result;
    }

    private void recordLog(ProceedingJoinPoint joinPoint, long time) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            OperationLog logAnnotation = method.getAnnotation(OperationLog.class);
            
            // 获取request
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            
            // 构建操作日志
            com.modelbolt.blog.model.entity.OperationLog operationLog = new com.modelbolt.blog.model.entity.OperationLog();
            operationLog.setUserId(SecurityUtils.getCurrentUserId());
            operationLog.setOperation(logAnnotation.value());
            operationLog.setMethod(request.getMethod() + " " + request.getRequestURI());
            operationLog.setParams(joinPoint.getArgs().toString());
            operationLog.setIp(request.getRemoteAddr());
            
            // 保存日志
            operationLogMapper.insert(operationLog);
            
            log.debug("Operation log recorded - Operation: {}, Method: {}, Time: {}ms", 
                    logAnnotation.value(), method.getName(), time);
        } catch (Exception e) {
            log.error("Failed to record operation log", e);
        }
    }
} 