package com.modelbolt.blog.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@TableName("article_tag")
@Schema(description = "文章标签关联实体")
public class ArticleTag {
    @TableId(type = IdType.AUTO)
    @Schema(description = "ID")
    private Long id;

    @Schema(description = "文章ID")
    private Long articleId;

    @Schema(description = "标签ID")
    private Long tagId;
} 