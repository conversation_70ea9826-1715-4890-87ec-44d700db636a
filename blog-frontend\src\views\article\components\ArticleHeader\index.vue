<template>
  <div class="article-header mb-8">
    <!-- 文章标题 -->
    <h1 class="text-3xl font-bold mb-4 text-gray-900 dark:text-gray-100">
      {{ title }}
    </h1>

    <!-- 文章元信息 -->
    <div class="flex flex-wrap items-center gap-6 text-sm text-gray-500 dark:text-gray-400 mb-6">
      <!-- 作者信息 -->
      <div class="flex items-center gap-2">
        <el-avatar 
          :size="32" 
          :src="getAvatarUrl(authorAvatar)"
          @error="imgError"
        >
          {{ authorName?.charAt(0) }}
        </el-avatar>
        <span>{{ authorName }}</span>
      </div>

      <!-- 发布时间 -->
      <span class="flex items-center">
        <el-icon class="mr-1"><Timer /></el-icon>
        {{ formatDate(createdAt) }}
      </span>

      <!-- 阅读量 -->
      <span class="flex items-center">
        <el-icon class="mr-1"><View /></el-icon>
        {{ viewCount }}
      </span>

      <!-- 评论数 -->
      <span class="flex items-center">
        <el-icon class="mr-1"><ChatDotRound /></el-icon>
        {{ commentCount }}
      </span>

      <!-- 点赞数 -->
      <span class="flex items-center">
        <el-icon class="mr-1"><Star /></el-icon>
        {{ likeCount }}
      </span>

      <!-- 收藏数 -->
      <span class="flex items-center">
        <el-icon class="mr-1"><Collection /></el-icon>
        {{ favoriteCount }}
      </span>
    </div>

    <!-- 文章封面 -->
    <div v-if="cover" class="article-cover rounded-lg overflow-hidden">
      <el-image
        :src="getCoverUrl(cover)"
        :alt="title"
        class="w-full h-auto"
        fit="cover"
        lazy
      >
        <template #placeholder>
          <div class="image-placeholder animate-pulse bg-gray-200 dark:bg-gray-700 w-full h-64"></div>
        </template>
        <template #error>
          <div class="image-error flex items-center justify-center bg-gray-100 dark:bg-gray-800 w-full h-64">
            <el-icon :size="32" class="text-gray-400"><Picture /></el-icon>
          </div>
        </template>
      </el-image>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Timer, View, ChatDotRound, Star, Collection, Picture } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/date'
import { getAvatarUrl, useImageFallback, getCoverUrl } from '@/utils/image'

const { imgError } = useImageFallback()

defineProps<{
  title: string
  authorName: string
  authorAvatar?: string
  createdAt: string
  viewCount: number
  commentCount: number
  likeCount: number
  favoriteCount: number
  cover?: string
}>()
</script>

<style lang="scss" scoped>
.article-header {
  .article-cover {
    @apply shadow-sm transition-shadow duration-300;
    
    &:hover {
      @apply shadow-md;
    }
  }

  .image-placeholder,
  .image-error {
    @apply rounded-lg;
  }
}
</style> 