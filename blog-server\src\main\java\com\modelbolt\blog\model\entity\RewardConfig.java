package com.modelbolt.blog.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@TableName(value = "reward_config", autoResultMap = true)
public class RewardConfig {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long userId;
    private Boolean enabled;
    
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Integer> amounts;
    
    private String qrcodeWechat;
    private String qrcodeAlipay;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}