<template>
  <router-view />
</template>

<script setup lang="ts">
import { onMounted, watch, onUnmounted } from 'vue'
import { useAdminStore } from '@/stores/admin'
import { useWebSocketStore } from '@/stores/websocket'
import { message } from 'ant-design-vue'
import { startTokenCheck, stopTokenCheck } from '@/utils/request'

const adminStore = useAdminStore()
const webSocketStore = useWebSocketStore()

// 监听WebSocket连接状态
watch(() => webSocketStore.isConnected, (newValue) => {
  if (!newValue) {
    message.warning('仪表盘实时数据连接已断开，正在尝试重连...')
  }
})

// 监听用户认证状态
watch(() => adminStore.token, (newValue) => {
  if (newValue) {
    // 用户已登录，启动token检查
    startTokenCheck()
  } else {
    // 用户已登出，停止token检查
    stopTokenCheck()
  }
})

onMounted(async () => {
  // 如果有token，获取管理员信息并启动token检查
  if (adminStore.token) {
    try {
      const adminInfo = await adminStore.getAdminInfo()
      if (adminInfo) {
        // 初始化WebSocket连接
        webSocketStore.initWebSocket()
        // 启动token检查
        startTokenCheck()
      } else {
        // 获取管理员信息失败但不是401错误，显示提示
        message.warning('获取管理员信息失败，请重试')
      }
    } catch (error) {
      console.error('Error in App.vue onMounted:', error)
    }
  }
})

onUnmounted(() => {
  // 组件卸载时停止token检查
  stopTokenCheck()
})
</script>

<style>
#app {
  width: 100%;
  height: 100%;
}
</style>
