package com.modelbolt.blog.model.dto.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 系统配置DTO
 */
@Data
@Schema(description = "系统配置")
public class SystemConfigDTO {
    
    @Schema(description = "站点名称")
    private String siteName;
    
    @Schema(description = "站点描述")
    private String siteDescription;
    
    @Schema(description = "站点关键词")
    private String siteKeywords;
    
    @Schema(description = "站点URL")
    private String siteUrl;
    
    @Schema(description = "主题")
    private String theme;
    
    @Schema(description = "是否开启评论")
    private Boolean commentEnabled;
    
    @Schema(description = "是否开启评论审核")
    private Boolean commentAuditEnabled;
    
    @Schema(description = "是否开启注册")
    private Boolean registrationEnabled;
    
    @Schema(description = "是否开启邮箱验证")
    private Boolean emailVerificationEnabled;
    
    // 邮件配置
    @Schema(description = "SMTP服务器地址")
    private String smtpHost;
    
    @Schema(description = "SMTP服务器端口")
    private Integer smtpPort;
    
    @Schema(description = "SMTP用户名")
    private String smtpUsername;
    
    @Schema(description = "SMTP密码")
    private String smtpPassword;
    
    @Schema(description = "发件人邮箱")
    private String smtpFrom;
    
    // AI配置
    @Schema(description = "AI模型")
    private String aiModel;
    
    @Schema(description = "AI API密钥")
    private String aiApiKey;
    
    @Schema(description = "AI API端点")
    private String aiApiEndpoint;
    
    @Schema(description = "AI最大令牌数")
    private Integer aiMaxTokens;
    
    @Schema(description = "AI温度参数")
    private Double aiTemperature;
    
    @Schema(description = "AI请求限制")
    private Integer aiRequestLimit;
    
    @Schema(description = "AI令牌限制")
    private Integer aiTokenLimit;
    
    // 安全配置
    @Schema(description = "登录尝试次数")
    private Integer securityLoginAttempts;
    
    @Schema(description = "锁定时长(分钟)")
    private Integer securityLockDuration;
} 