import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'
import router from './router'

// 引入 Markdown 编辑器
import VMdEditor from '@kangc/v-md-editor'
import '@kangc/v-md-editor/lib/style/base-editor.css'
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js'
import '@kangc/v-md-editor/lib/theme/style/github.css'
// 引入代码高亮
import hljs from 'highlight.js'

// 配置 Markdown 编辑器
VMdEditor.use(githubTheme, {
  Hljs: hljs,
})

// 引入 TailwindCSS 样式
import '@/styles/tailwind.css'
// 引入全局样式
import '@/styles/index.scss'
import './styles/theme.scss'

import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { highlight } from './directives/highlight'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ElementPlus)
app.use(VMdEditor)
app.directive('highlight', highlight)

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.mount('#app')
