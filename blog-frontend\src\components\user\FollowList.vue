<template>
  <div class="follow-list">
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <el-tab-pane label="关注" name="following">
        <div class="follow-users">
          <div v-if="loading" class="flex justify-center py-8">
            <el-skeleton :rows="3" animated />
          </div>
          <template v-else>
            <div v-if="users.length === 0" class="empty-tip">
              <el-empty description="暂无关注" />
            </div>
            <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div v-for="user in users" :key="user.id" class="user-card">
                <el-card shadow="hover" class="h-full">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                      <router-link :to="'/user/' + user.id" class="flex-shrink-0">
                        <el-avatar 
                          :size="48" 
                          :src="getAvatarUrl(user.avatar)"
                          @error="imgError"
                        >
                          {{ user.username.charAt(0).toUpperCase() }}
                        </el-avatar>
                      </router-link>
                      <div class="flex-grow min-w-0">
                        <router-link :to="'/user/' + user.id" class="block">
                          <h3 class="text-base font-medium text-gray-900 truncate">
                            {{ user.username }}
                          </h3>
                        </router-link>
                        <p class="text-sm text-gray-500 truncate">
                          {{ user.bio || '这个人很懒，什么都没写~' }}
                        </p>
                        <p v-if="activeTab === 'followers' && user.isFollowEachOther" class="text-xs text-blue-600 mt-1">
                          互相关注
                        </p>
                      </div>
                    </div>
                    <follow-button
                      v-if="user.id"
                      :user-id="user.id"
                      :initial-is-following="activeTab === 'following'"
                      size="small"
                      @follow="handleFollow"
                      @unfollow="handleUnfollow"
                    />
                  </div>
                </el-card>
              </div>
            </div>
            
            <!-- 分页 -->
            <div class="mt-4 flex justify-center">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :total="total"
                :page-sizes="[12, 24, 36, 48]"
                layout="total, sizes, prev, pager, next"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </template>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="粉丝" name="followers">
        <div class="follow-users">
          <div v-if="loading" class="flex justify-center py-8">
            <el-skeleton :rows="3" animated />
          </div>
          <template v-else>
            <div v-if="users.length === 0" class="empty-tip">
              <el-empty description="暂无粉丝" />
            </div>
            <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div v-for="user in users" :key="user.id" class="user-card">
                <el-card shadow="hover" class="h-full">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                      <router-link :to="'/user/' + user.id" class="flex-shrink-0">
                        <el-avatar 
                          :size="48" 
                          :src="getAvatarUrl(user.avatar)"
                          @error="imgError"
                        >
                          {{ user.username.charAt(0).toUpperCase() }}
                        </el-avatar>
                      </router-link>
                      <div class="flex-grow min-w-0">
                        <router-link :to="'/user/' + user.id" class="block">
                          <h3 class="text-base font-medium text-gray-900 truncate">
                            {{ user.username }}
                          </h3>
                        </router-link>
                        <p class="text-sm text-gray-500 truncate">
                          {{ user.bio || '这个人很懒，什么都没写~' }}
                        </p>
                        <p v-if="user.isFollowEachOther" class="text-xs text-blue-600 mt-1">
                          互相关注
                        </p>
                      </div>
                    </div>
                    <follow-button
                      v-if="user.id"
                      :user-id="user.id"
                      :initial-is-following="user.isFollowEachOther"
                      size="small"
                      @follow="handleFollow"
                      @unfollow="handleUnfollow"
                    />
                  </div>
                </el-card>
              </div>
            </div>
            
            <!-- 分页 -->
            <div class="mt-4 flex justify-center">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :total="total"
                :page-sizes="[12, 24, 36, 48]"
                layout="total, sizes, prev, pager, next"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </template>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getFollowList } from '@/api/userCenter'
import type { FollowUserInfo } from '@/types/user'
import { getAvatarUrl, useImageFallback } from '@/utils/image'
import FollowButton from './FollowButton.vue'

const props = defineProps<{
  userId?: number
}>()

const emit = defineEmits<{
  (e: 'follow', userId: number): void
  (e: 'unfollow', userId: number): void
}>()

const { imgError } = useImageFallback()

const activeTab = ref<'following' | 'followers'>('following')
const loading = ref(false)
const users = ref<FollowUserInfo[]>([])
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)

// 加载关注/粉丝列表
const loadUsers = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      type: activeTab.value,
    }

    // 只有当userId存在时才添加到参数中
    if (props.userId !== undefined && props.userId !== null) {
      params.userId = props.userId
    }

    const { data } = await getFollowList(params)
    users.value = data.records.map(user => ({
      ...user,
      id: Number(user.userId || user.id) || 0,
      isFollowing: activeTab.value === 'following' ? true : Boolean(user.isFollowEachOther)
    }))
    total.value = data.total
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载失败，请重试')
  } finally {
    loading.value = false
  }
}

// 处理标签页切换
const handleTabChange = () => {
  currentPage.value = 1
  loadUsers()
}

// 处理页码改变
const handleCurrentChange = () => {
  loadUsers()
}

// 处理每页条数改变
const handleSizeChange = () => {
  currentPage.value = 1
  loadUsers()
}

// 处理关注事件
const handleFollow = (userId: number) => {
  emit('follow', userId)
}

// 处理取消关注事件
const handleUnfollow = (userId: number) => {
  emit('unfollow', userId)
  // 如果是在"关注"标签页，则从列表中移除该用户
  if (activeTab.value === 'following') {
    users.value = users.value.filter(user => user.id !== userId)
    total.value--
  }
}

onMounted(() => {
  loadUsers()
})
</script>

<style lang="scss" scoped>
.follow-list {
  .user-card {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
    }
  }
  
  .empty-tip {
    @apply py-8;
  }
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
  background-color: var(--el-border-color-light);
}

:deep(.el-tabs__active-bar) {
  height: 2px;
}
</style> 