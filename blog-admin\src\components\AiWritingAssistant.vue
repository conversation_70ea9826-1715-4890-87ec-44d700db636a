<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { aiApi } from '@/api/ai'
import type { AiWritingRequest, AiWritingResponse } from '@/types/ai'
import { WritingType } from '@/types/ai'

const props = defineProps<{
  mode: 'new' | 'edit'
  articleId?: number
  content?: string
  title?: string
  summary?: string
}>()

const emit = defineEmits<{
  (e: 'update:content', value: string): void
  (e: 'update:title', value: string): void
  (e: 'update:summary', value: string): void
}>()

// 对话框可见性
const visible = ref(false)
// 加载状态
const loading = ref(false)
// 当前选中的写作类型
const currentType = ref<WritingType>(WritingType.ARTICLE_GENERATION)
// AI响应结果
const aiResponse = ref<AiWritingResponse>()
// 当前步骤
const step = ref(1)
// 选中的大纲
const selectedOutline = ref<number | null>(null)
// 对话消息
const chatMessages = ref<Array<{ role: 'user' | 'assistant', content: string }>>([])
// 对话输入
const chatInput = ref('')

// 表单数据
const formData = reactive<AiWritingRequest>({
  type: WritingType.ARTICLE_GENERATION,
  keywords: [],
  targetAudience: '',
  articleType: '',
  optimizationGoal: '',
  retainKeywords: [],
  step: 1,
  selectedOutlineIndex: null
})

// 组件挂载标志
const isMounted = ref(false)

// 初始化状态
const initializeState = async () => {
  console.log('初始化状态 [开始]:', {
    props: {
      mode: props.mode,
      content: props.content,
      title: props.title,
      summary: props.summary
    },
    currentType: currentType.value,
    step: step.value
  })

  try {
    // 根据内容设置初始类型和步骤
    const hasContent = Boolean(props.content?.trim() && props.title?.trim())
    
    // 设置初始类型和步骤
    currentType.value = hasContent ? WritingType.ARTICLE_EDITING : WritingType.ARTICLE_GENERATION
    step.value = hasContent ? 3 : 1
    
    // 重置其他状态
    chatMessages.value = []
    chatInput.value = ''
    selectedOutline.value = null
    aiResponse.value = undefined
    
    // 重置表单数据
    Object.assign(formData, {
      type: currentType.value,
      keywords: [],
      targetAudience: '',
      articleType: '',
      optimizationGoal: '',
      retainKeywords: [],
      step: step.value,
      selectedOutlineIndex: null
    })

    await nextTick()
    console.log('初始化状态 [完成]:', {
      hasContent,
      currentType: currentType.value,
      step: step.value,
      formData: { ...formData }
    })
  } catch (error) {
    console.error('初始化状态失败:', error)
    message.error('初始化失败')
  }
}

// 组件挂载后的初始化
onMounted(() => {
  console.log('组件已挂载，初始状态:', {
    props: {
      mode: props.mode,
      content: props.content,
      title: props.title,
      summary: props.summary
    },
    currentType: currentType.value,
    step: step.value,
    formData: { ...formData }
  })
  isMounted.value = true
})

// 监听可见性变化
watch(() => visible.value, async (isVisible) => {
  console.log('对话框可见性变化:', {
    isVisible,
    isMounted: isMounted.value,
    currentType: currentType.value,
    step: step.value
  })
  
  if (isVisible && isMounted.value) {
    console.log('开始初始化状态')
    await initializeState()
  }
})

// 监听当前类型变化
watch(() => currentType.value, (newType, oldType) => {
  console.log('当前类型变化 [详细]:', {
    oldType: typeof oldType === 'object' ? JSON.stringify(oldType) : oldType,
    newType: typeof newType === 'object' ? JSON.stringify(newType) : newType,
    step: step.value,
    formData: {
      type: formData.type,
      step: formData.step,
      keywords: formData.keywords,
      targetAudience: formData.targetAudience,
      articleType: formData.articleType
    }
  })
}, { deep: true })

// 监听表单数据变化
watch(() => formData, (newData) => {
  console.log('表单数据变化:', {
    type: newData.type,
    step: newData.step,
    keywords: newData.keywords,
    targetAudience: newData.targetAudience,
    articleType: newData.articleType
  })
}, { deep: true })

// 监听 props 变化
watch(() => props.content, (newVal) => {
  console.log('content changed:', newVal)
  updateTypeOptions()
})

watch(() => props.title, (newVal) => {
  console.log('title changed:', newVal)
  updateTypeOptions()
})

// 更新类型选项
const updateTypeOptions = (force = false) => {
  console.log('更新类型选项:', { 
    force, 
    content: props.content,
    currentType: currentType.value,
    step: step.value
  })
  
  // 完全跳过自动切换当强制更新时（增强判断逻辑）
  if (force) return

  const hasContent = Boolean(props.content?.trim() && props.title?.trim())
  if (hasContent && currentType.value !== WritingType.ARTICLE_EDITING) {
    console.log('自动切换到编辑模式')
    currentType.value = WritingType.ARTICLE_EDITING
    step.value = 3
    formData.type = WritingType.ARTICLE_EDITING
    formData.step = 3
  }
}

// 写作类型选项
const typeOptions = computed(() => {
  console.log('计算类型选项:', {
    content: props.content,
    title: props.title,
    currentType: currentType.value,
    isGeneration: currentType.value === WritingType.ARTICLE_GENERATION,
    isEditing: currentType.value === WritingType.ARTICLE_EDITING,
    WritingType: WritingType
  })
  
  return [
    { 
      label: '文章生成', 
      value: WritingType.ARTICLE_GENERATION,
      disabled: false,
      selected: currentType.value === WritingType.ARTICLE_GENERATION
    },
    { 
      label: '文章编辑', 
      value: WritingType.ARTICLE_EDITING,
      disabled: !props.content?.trim() || !props.title?.trim(),
      selected: currentType.value === WritingType.ARTICLE_EDITING
    }
  ]
})

// 是否显示类型选择器
const showTypeSelector = computed(() => {
  return props.mode === 'new' || !props.content
})

// 打开助手
const showAssistant = async () => {
  console.log('打开AI助手 [开始]:', {
    props: {
      mode: props.mode,
      content: props.content,
      title: props.title,
      summary: props.summary
    },
    currentType: currentType.value,
    step: step.value,
    formData: { ...formData }
  })

  // 检查配额
  try {
    const { data: hasQuota } = await aiApi.checkQuota()
    if (!hasQuota) {
      message.warning('您今日的AI使用配额已用完')
      return
    }
  } catch (error) {
    message.error('配额检查失败')
    return
  }

  // 根据内容预设类型
  const hasContent = Boolean(props.content?.trim() && props.title?.trim())
  currentType.value = hasContent ? WritingType.ARTICLE_EDITING : WritingType.ARTICLE_GENERATION
  step.value = hasContent ? 3 : 1

  // 显示对话框，状态初始化将由 watch 处理
  visible.value = true
  
  console.log('AI助手已打开，等待状态初始化:', {
    hasContent,
    currentType: currentType.value,
    step: step.value
  })
}

// 处理写作类型变更
const handleTypeChange = async (type: string) => {
  // 显式转换为枚举类型
  const selectedType = type as WritingType

  console.log('切换写作类型 [开始]:', {
    from: currentType.value,
    to: selectedType,
    currentStep: step.value
  })

  // 如果是编辑模式但没有内容，不允许切换
  if (selectedType === WritingType.ARTICLE_EDITING && (!props.content || !props.title)) {
    console.log('切换到编辑模式被阻止：内容或标题为空')
    message.warning('请先填写文章标题和内容后再使用编辑模式')
    return
  }

  try {
    // 完全重置所有状态
    chatMessages.value = []
    chatInput.value = ''
    selectedOutline.value = null
    aiResponse.value = undefined
    
    // 设置新类型
    currentType.value = selectedType
    
    // 根据类型设置步骤和重置表单
    if (selectedType === WritingType.ARTICLE_GENERATION) {
      // 切换到生成模式时总是从第一步开始
      step.value = 1
      Object.assign(formData, {
        type: WritingType.ARTICLE_GENERATION,
        keywords: [],
        targetAudience: '',
        articleType: '',
        optimizationGoal: '',
        retainKeywords: [],
        step: 1,
        selectedOutlineIndex: null
      })
    } else {
      // 切换到编辑模式时设置到第三步
      step.value = 3
      Object.assign(formData, {
        type: WritingType.ARTICLE_EDITING,
        step: 3
      })
    }

    // 强制更新视图
    await nextTick()
    console.log('切换写作类型 [完成]:', {
      currentType: currentType.value,
      step: step.value,
      formData: { ...formData }
    })
  } catch (error) {
    console.error('类型切换出错:', error)
    message.error('切换模式失败')
  }
}

// 生成内容
const handleGenerate = async () => {
  // 确保type已设置
  formData.type = currentType.value
  
  if (formData.type === WritingType.ARTICLE_EDITING && !props.content) {
    message.warning('请先输入文章内容')
    return
  }

  // 更新formData中的step
  formData.step = step.value

  loading.value = true
  try {
    const request: AiWritingRequest = {
      ...formData,
      articleId: props.articleId,
      originalContent: props.content,
      step: step.value,
      selectedOutlineIndex: selectedOutline.value
    }

    console.log('发送请求:', request)
    const { data } = await aiApi.processWriting(request)
    console.log('收到响应:', data)
    aiResponse.value = data
    
    if (formData.type === WritingType.ARTICLE_GENERATION && step.value === 1) {
      message.success('大纲生成成功，请选择一个大纲继续')
      step.value = 2
    } else if (step.value === 2) {
      message.success('文章生成成功')
      step.value = 3
    }
  } catch (error) {
    console.error('生成失败:', error)
    message.error('生成失败')
  } finally {
    loading.value = false
  }
}

// 选择大纲
const selectOutline = (index: number) => {
  console.log('Selecting outline:', index)
  selectedOutline.value = index
  formData.selectedOutlineIndex = index
  // 确保formData中的其他数据保持不变
  formData.step = step.value
  console.log('Updated formData:', formData)
  message.success('已选择大纲，点击生成按钮继续')
}

// 应用创意
const applyIdea = () => {
  if (!aiResponse.value?.article) return
  
  const article = aiResponse.value.article
  emit('update:title', article.title)
  emit('update:content', article.content)
  emit('update:summary', article.summary)
  message.success('已应用文章')
  visible.value = false
}

// 处理对话
const handleChat = async () => {
  if (!chatInput.value.trim()) return
  
  const userMessage = chatInput.value
  chatInput.value = ''
  
  loading.value = true
  try {
    console.log('发送对话请求', {
      message: userMessage,
      articleId: props.articleId,
      originalContent: props.content,
      originalTitle: props.title,
      originalSummary: props.summary
    })

    const { data } = await aiApi.chat({
      message: userMessage,
      articleId: props.articleId,
      context: chatMessages.value,
      originalContent: props.content,
      originalTitle: props.title,
      originalSummary: props.summary
    })
    
    console.log('收到对话响应', data)
    
    // 如果返回了更新的内容，显示对比视图
    if (data.hasChanges && data.updates) {
      console.log('显示内容对比')
      aiResponse.value = {
        hasChanges: true,
        updates: data.updates
      }
    }
  } catch (error) {
    console.error('对话请求失败:', error)
    message.error('发送失败')
  } finally {
    loading.value = false
  }
}

// 应用更改
const applyChanges = () => {
  if (!aiResponse.value?.updates) return
  
  const { title, content, summary } = aiResponse.value.updates
  emit('update:title', title)
  emit('update:content', content)
  emit('update:summary', summary)
  message.success('已应用更改')
  
  // 重置为显示当前内容
  aiResponse.value = undefined
}

// 取消更改
const rejectChanges = () => {
  // 直接重置状态，显示原始内容
  aiResponse.value = undefined
  message.info('已取消应用')
}

defineExpose({
  showAssistant
})
</script>

<template>
  <div class="ai-writing-assistant">
    <a-modal
      v-model:visible="visible"
      :title="currentType === WritingType.ARTICLE_GENERATION ? 'AI文章生成' : 'AI文章编辑'"
      width="800px"
      :footer="null"
      :destroyOnClose="true"
    >
      <a-spin :spinning="loading">
        <div class="assistant-content">
          <!-- 写作类型选择 -->
          <div class="type-selector">
            <a-radio-group 
              v-model:value="currentType"
              button-style="solid"
              @change="(e) => handleTypeChange(String(e.target.value))"
            >
              <a-radio-button
                v-for="option in typeOptions"
                :key="option.value"
                :value="option.value"
                :disabled="option.disabled"
                :class="{
                  'selected': option.selected,
                  'disabled': option.disabled
                }"
              >
                {{ option.label }}
              </a-radio-button>
            </a-radio-group>
          </div>

          <!-- 文章生成模式 -->
          <template v-if="currentType === WritingType.ARTICLE_GENERATION">
            <!-- 步骤提示 -->
            <a-steps
              :current="step - 1"
              class="steps-section"
            >
              <a-step title="选择模式" description="设置创作参数" />
              <a-step title="选择大纲" description="从建议中选择合适的大纲" />
              <a-step title="生成文章" description="生成完整文章并进行调整" />
            </a-steps>

            <!-- 参数设置区域 -->
            <div class="params-section" v-if="step === 1">
              <a-form layout="vertical">
                <a-form-item label="关键词">
                  <a-select
                    v-model:value="formData.keywords"
                    mode="tags"
                    placeholder="请输入关键词，按回车确认"
                    :maxTagCount="5"
                  />
                </a-form-item>
                <a-form-item label="目标受众">
                  <a-input
                    v-model:value="formData.targetAudience"
                    placeholder="请描述目标读者群体"
                  />
                </a-form-item>
                <a-form-item label="文章类型">
                  <a-input
                    v-model:value="formData.articleType"
                    placeholder="如：教程、观点、分析等"
                  />
                </a-form-item>
              </a-form>

              <!-- 生成按钮 -->
              <div class="action-section">
                <a-button type="primary" @click="handleGenerate" :loading="loading">
                  生成大纲建议
                </a-button>
              </div>
            </div>

            <!-- 大纲选择区域 -->
            <div v-if="step === 2 && aiResponse?.ideas">
              <a-collapse>
                <a-collapse-panel
                  v-for="(idea, index) in aiResponse.ideas"
                  :key="index"
                  :header="`大纲 ${index + 1}: ${idea.title}`"
                >
                  <div class="idea-content">
                    <h4>大纲：</h4>
                    <div class="outline" v-html="idea.outline.replace(/\n/g, '<br>')"/>
                    <h4>摘要：</h4>
                    <p>{{ idea.summary }}</p>
                    <h4>关键词：</h4>
                    <a-tag v-for="keyword in idea.keywords" :key="keyword">
                      {{ keyword }}
                    </a-tag>
                    <div class="idea-actions">
                      <a-button 
                        type="primary" 
                        @click="selectOutline(index)"
                        :class="{ 'selected': selectedOutline === index }"
                      >
                        {{ selectedOutline === index ? '已选择' : '选择此大纲' }}
                      </a-button>
                    </div>
                  </div>
                </a-collapse-panel>
              </a-collapse>

              <!-- 生成按钮 -->
              <div class="action-section" v-if="selectedOutline !== null">
                <a-button type="primary" @click="handleGenerate" :loading="loading">
                  生成完整文章
                </a-button>
              </div>
            </div>

            <!-- 文章预览区域 -->
            <div v-if="step === 3 && aiResponse?.article" class="full-article">
              <div class="article-content">
                <h4>标题：</h4>
                <p>{{ aiResponse.article.title }}</p>
                <h4>内容：</h4>
                <div v-html="aiResponse.article.content.replace(/\n/g, '<br>')"/>
                <h4>摘要：</h4>
                <p>{{ aiResponse.article.summary }}</p>
                <div class="article-actions">
                  <a-button type="primary" @click="applyIdea">
                    应用文章
                  </a-button>
                </div>
              </div>
            </div>
          </template>

          <!-- 文章编辑模式 -->
          <template v-else>
            <!-- 如果没有内容，显示提示 -->
            <div v-if="!props.content || !props.title" class="empty-content-tip">
              <a-alert
                type="warning"
                message="请先填写文章标题和内容"
                description="使用文章编辑模式需要先填写文章标题和内容"
                show-icon
              />
            </div>

            <!-- 有内容时显示编辑界面 -->
            <div v-else class="flex flex-col gap-4">
              <!-- 当前内容显示 -->
              <div v-if="!aiResponse?.hasChanges" class="border rounded p-4 bg-gray-50">
                <h3 class="text-lg font-medium mb-2">当前文章内容</h3>
                <div class="mb-4">
                  <div class="text-sm text-gray-500 mb-1">标题</div>
                  <div class="font-medium">{{ props.title }}</div>
                </div>
                <div class="mb-4">
                  <div class="text-sm text-gray-500 mb-1">摘要</div>
                  <div>{{ props.summary }}</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500 mb-1">正文</div>
                  <div class="whitespace-pre-wrap">{{ props.content }}</div>
                </div>
              </div>

              <!-- 输入框 -->
              <div class="border rounded p-4">
                <div class="mb-2">请输入您想要调整的内容，AI 将帮您优化文章：</div>
                <a-textarea
                  v-model:value="chatInput"
                  :rows="4"
                  placeholder="例如：'帮我修改标题使其更吸引人'、'优化文章结构使其更通顺'等"
                  :disabled="loading"
                  @keyup.enter="handleChat"
                />
                <div class="flex justify-end mt-2">
                  <a-button type="primary" :loading="loading" @click="handleChat">发送</a-button>
                </div>
              </div>

              <!-- 内容对比视图 -->
              <div v-if="aiResponse?.hasChanges" class="grid grid-cols-2 gap-4">
                <!-- 原始内容 -->
                <div class="border rounded p-4">
                  <h3 class="text-lg font-medium mb-2 text-gray-500">原始内容</h3>
                  <div class="mb-4">
                    <div class="text-sm text-gray-500 mb-1">标题</div>
                    <div class="font-medium">{{ props.title }}</div>
                  </div>
                  <div class="mb-4">
                    <div class="text-sm text-gray-500 mb-1">摘要</div>
                    <div>{{ props.summary }}</div>
                  </div>
                  <div>
                    <div class="text-sm text-gray-500 mb-1">正文</div>
                    <div class="whitespace-pre-wrap">{{ props.content }}</div>
                  </div>
                </div>

                <!-- 更新后的内容 -->
                <div class="border rounded p-4 bg-blue-50">
                  <h3 class="text-lg font-medium mb-2 text-blue-600">修改后的内容</h3>
                  <div class="mb-4">
                    <div class="text-sm text-gray-500 mb-1">标题</div>
                    <div class="font-medium">{{ aiResponse.updates.title }}</div>
                  </div>
                  <div class="mb-4">
                    <div class="text-sm text-gray-500 mb-1">摘要</div>
                    <div>{{ aiResponse.updates.summary }}</div>
                  </div>
                  <div>
                    <div class="text-sm text-gray-500 mb-1">正文</div>
                    <div class="whitespace-pre-wrap">{{ aiResponse.updates.content }}</div>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="col-span-2 flex justify-end gap-2">
                  <a-button @click="rejectChanges">取消应用</a-button>
                  <a-button type="primary" @click="applyChanges">应用更改</a-button>
                </div>
              </div>
            </div>
          </template>
        </div>
      </a-spin>
    </a-modal>
  </div>
</template>

<style lang="scss" scoped>
.ai-writing-assistant {
  .assistant-content {
    .steps-section {
      margin-bottom: 24px;
    }

    .type-selector {
      margin-bottom: 24px;
      text-align: center;

      :deep(.ant-radio-button-wrapper) {
        min-width: 120px;
        text-align: center;
        transition: all 0.3s;
        
        &.selected {
          background-color: #1890ff;
          color: #fff;
          border-color: #1890ff;
          
          &:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
          }
          
          &:before {
            background-color: #1890ff;
          }
        }
        
        &.disabled {
          background-color: #f5f5f5;
          color: #d9d9d9;
          cursor: not-allowed;
          
          &:hover {
            color: #d9d9d9;
            background-color: #f5f5f5;
          }
        }
        
        &:not(.disabled):hover {
          color: #40a9ff;
        }
      }
    }

    .params-section {
      margin-bottom: 24px;
    }

    .action-section {
      margin-bottom: 24px;
      text-align: right;
    }

    .result-section {
      .result-alert {
        margin-bottom: 16px;
      }

      .idea-content {
        h4 {
          margin: 16px 0 8px;
          color: #1890ff;
        }

        .outline {
          background: #f5f5f5;
          padding: 12px;
          border-radius: 4px;
        }

        .idea-actions {
          margin-top: 16px;
          text-align: right;

          .selected {
            background-color: #52c41a !important;
            border-color: #52c41a !important;
          }
        }
      }

      .full-article {
        .article-content {
          margin-bottom: 24px;

          h4 {
            margin: 16px 0 8px;
            color: #1890ff;
          }
        }
      }

      .chat-section {
        margin-top: 24px;
        border-top: 1px solid #f0f0f0;
        padding-top: 16px;

        .content-comparison {
          margin-bottom: 24px;
          border-bottom: 1px solid #f0f0f0;
          padding-bottom: 16px;

          .content-box {
            background: #f8f8f8;
            padding: 16px;
            border-radius: 4px;
            margin-bottom: 16px;

            h5 {
              color: #1890ff;
              margin-bottom: 8px;
            }

            .title {
              font-weight: bold;
              margin-bottom: 16px;
            }

            .content {
              margin-bottom: 16px;
            }
          }

          .diff-explanation {
            background: #e6f7ff;
            padding: 16px;
            border-radius: 4px;
            margin-bottom: 16px;
          }

          .action-buttons {
            text-align: right;
            
            .ant-btn {
              margin-left: 8px;
            }
          }
        }

        .chat-messages {
          max-height: 300px;
          overflow-y: auto;
          margin-bottom: 16px;

          .message {
            margin-bottom: 12px;
            padding: 8px 12px;
            border-radius: 4px;

            &.user {
              background: #e6f7ff;
              margin-left: 20%;
            }

            &.assistant {
              background: #f5f5f5;
              margin-right: 20%;
            }
          }
        }

        .chat-input {
          margin-top: 12px;
        }
      }
    }

    .empty-content-tip {
      margin-bottom: 24px;
    }
  }

  .content-box {
    @apply border rounded p-4 mb-4;
    
    h4 {
      @apply text-lg font-medium mb-2;
    }
    
    h5 {
      @apply text-sm text-gray-500 mb-1;
    }
  }

  .chat-section {
    @apply flex flex-col gap-4;
  }

  .original-content, .updated-content {
    @apply border rounded p-4;

    &.updated-content {
      @apply bg-blue-50;
      
      h4 {
        @apply text-blue-600;
      }
    }
  }

  .diff-explanation {
    @apply mt-4 p-4 border rounded bg-gray-50;
    
    h4 {
      @apply text-lg font-medium mb-2;
    }
  }

  .action-buttons {
    @apply flex justify-end gap-2 mt-4;
  }

  .chat-input {
    @apply mt-4;
  }

  // 文本内容样式
  .title {
    @apply font-medium;
  }

  .content, .summary {
    @apply whitespace-pre-wrap;
  }
}
</style> 