package com.modelbolt.blog.model.vo;

import lombok.Data;
import java.util.List;

@Data
public class MonitoringData {
    private AiUsage aiUsage;
    private Performance performance;
    private UserBehavior userBehavior;
    private ContentQuality contentQuality;

    @Data
    public static class AiUsage {
        private long totalCalls;
        private long totalTokens;
        private double successRate;
        private double averageLatency;
        private List<DailyStat> dailyStats;

        @Data
        public static class DailyStat {
            private String date;
            private long calls;
            private long tokens;
        }
    }

    @Data
    public static class Performance {
        private double cpuUsage;
        private double memoryUsage;
        private double responseTime;
        private int concurrentUsers;
        private double errorRate;
    }

    @Data
    public static class UserBehavior {
        private int activeUsers;
        private int newUsers;
        private double retention;
        private double averageSessionTime;
        private List<PageView> topPages;

        @Data
        public static class PageView {
            private String page;
            private long views;
        }
    }

    @Data
    public static class ContentQuality {
        private double averageScore;
        private int totalArticles;
        private List<QualityDistribution> qualityDistribution;
        private List<AuthorScore> topAuthors;

        @Data
        public static class QualityDistribution {
            private String level;
            private int count;
        }

        @Data
        public static class AuthorScore {
            private Long authorId;
            private String authorName;
            private double score;
        }
    }
} 