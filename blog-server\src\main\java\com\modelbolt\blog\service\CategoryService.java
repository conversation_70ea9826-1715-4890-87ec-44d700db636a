package com.modelbolt.blog.service;

import com.modelbolt.blog.model.dto.category.CategoryVO;
import com.modelbolt.blog.model.dto.category.CreateCategoryDTO;
import com.modelbolt.blog.model.dto.article.ArticleVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

public interface CategoryService {
    /**
     * 获取分类列表
     */
    List<CategoryVO> getCategories();

    /**
     * 获取分类详情
     * @param id 分类ID
     * @return 分类详情
     */
    CategoryVO getCategoryById(Long id);

    /**
     * 获取分类下的文章列表
     * @param categoryId 分类ID
     * @param page 页码
     * @param size 每页大小
     * @return 文章列表分页数据
     */
    IPage<ArticleVO> getArticlesByCategoryId(Long categoryId, Integer page, Integer size);

    /**
     * 获取热门分类
     * @param limit 获取数量
     * @return 热门分类列表
     */
    List<CategoryVO> getHotCategories(Integer limit);

    /**
     * 获取文章的分类列表
     * @param articleId 文章ID
     * @return 分类列表
     */
    List<CategoryVO> getCategoriesByArticleId(Long articleId);

    /**
     * 获取分类树形列表
     * @return 分类树形列表
     */
    List<CategoryVO> getCategoryTree();

    /**
     * 创建分类
     * @param dto 创建分类请求
     * @return 创建的分类
     */
    CategoryVO createCategory(CreateCategoryDTO dto);

    /**
     * 更新分类
     * @param id 分类ID
     * @param dto 更新分类请求
     * @return 更新后的分类
     */
    CategoryVO updateCategory(Long id, CreateCategoryDTO dto);

    /**
     * 删除分类
     * @param id 分类ID
     */
    void deleteCategory(Long id);

    /**
     * 更新分类排序
     * @param id 分类ID
     * @param orderNum 排序号
     * @return 更新后的分类
     */
    CategoryVO updateCategoryOrder(Long id, Integer orderNum);
} 