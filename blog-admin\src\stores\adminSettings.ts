import { defineStore } from 'pinia'
import { ref } from 'vue'
import * as adminSettingsApi from '@/api/adminSettings'
import type { AdminSettings } from '@/types/admin'

export const useAdminSettingsStore = defineStore('adminSettings', () => {
  const settings = ref<AdminSettings>({
    emailVerificationEnabled: true
  })

  const isUpdating = ref(false)

  /**
   * 加载管理员设置
   */
  async function loadSettings() {
    try {
      const response = await adminSettingsApi.getSettings()
      if (response.code === 200 && response.data) {
        settings.value = response.data
        return true
      }
      return false
    } catch (error) {
      console.error('Failed to load admin settings:', error)
      return false
    }
  }

  /**
   * 更新管理员设置
   */
  async function updateSettings(newSettings: Partial<AdminSettings>) {
    isUpdating.value = true
    try {
      console.log('[Settings] Updating settings:', newSettings)
      const response = await adminSettingsApi.updateSettings(newSettings)
      
      if (response.code === 200) {
        // 合并新旧设置保证响应式更新
        settings.value = { 
          ...settings.value,
          ...newSettings
        }
        console.log('[Settings] Updated settings:', settings.value)
        return true
      }
      return false
    } catch (error) {
      console.error('[Settings] Update error:', error)
      return false
    } finally {
      isUpdating.value = false
    }
  }

  return {
    settings,
    loadSettings,
    updateSettings
  }
}) 