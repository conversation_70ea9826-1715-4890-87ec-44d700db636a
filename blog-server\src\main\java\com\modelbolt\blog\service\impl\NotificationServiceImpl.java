package com.modelbolt.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.entity.*;
import com.modelbolt.blog.mapper.NotificationMapper;
import com.modelbolt.blog.mapper.ArticleMapper;
import com.modelbolt.blog.mapper.CommentMapper;
import com.modelbolt.blog.model.vo.NotificationVO;
import com.modelbolt.blog.service.NotificationService;
import com.modelbolt.blog.service.UserService;
import com.modelbolt.blog.event.NotificationEvent;
import org.springframework.context.ApplicationEventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationServiceImpl extends ServiceImpl<NotificationMapper, Notification> implements NotificationService {

    private final UserService userService;
    private final ArticleMapper articleMapper;
    private final CommentMapper commentMapper;
    private final NotificationMapper notificationMapper;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public PageResult<NotificationVO> getUserNotifications(Long userId, String type, Integer page, Integer size) {
        // 构建查询条件
        LambdaQueryWrapper<Notification> wrapper = new LambdaQueryWrapper<Notification>()
                .eq(Notification::getUserId, userId)
                .eq(!"all".equals(type), Notification::getType, type)
                .orderByDesc(Notification::getCreatedAt);

        // 查询通知列表
        Page<Notification> pageParam = new Page<>(page, size);
        Page<Notification> notificationPage = this.page(pageParam, wrapper);
        
        if (notificationPage.getRecords().isEmpty()) {
            return new PageResult<>(new ArrayList<>(), 0L, page, size);
        }

        // 获取相关用户信息
        List<Long> senderIds = notificationPage.getRecords().stream()
                .map(Notification::getSenderId)
                .collect(Collectors.toList());
        Map<Long, User> userMap = userService.listByIds(senderIds).stream()
                .collect(Collectors.toMap(User::getId, user -> user));

        // 获取相关文章信息
        List<Long> articleIds = notificationPage.getRecords().stream()
                .filter(n -> "article".equals(n.getTargetType()))
                .map(Notification::getTargetId)
                .collect(Collectors.toList());
        Map<Long, Article> articleMap = articleIds.isEmpty() ? new HashMap<>() : 
                articleMapper.selectBatchIds(articleIds).stream()
                .collect(Collectors.toMap(Article::getId, article -> article));

        // 获取相关评论信息
        List<Long> commentIds = notificationPage.getRecords().stream()
                .filter(n -> "comment".equals(n.getTargetType()))
                .map(Notification::getTargetId)
                .collect(Collectors.toList());
        Map<Long, Comment> commentMap = commentIds.isEmpty() ? new HashMap<>() :
                commentMapper.selectBatchIds(commentIds).stream()
                .collect(Collectors.toMap(Comment::getId, comment -> comment));

        // 转换通知记录
        List<NotificationVO> voList = notificationPage.getRecords().stream().map(notification -> {
            NotificationVO vo = new NotificationVO();
            BeanUtils.copyProperties(notification, vo);
            
            // 设置发送者信息
            User sender = userMap.get(notification.getSenderId());
            if (sender != null) {
                vo.setSenderName(sender.getUsername());
                vo.setSenderAvatar(sender.getAvatar());
            }
            
            // 设置目标信息
            if ("article".equals(notification.getTargetType())) {
                Article article = articleMap.get(notification.getTargetId());
                if (article != null) {
                    vo.setTargetTitle(article.getTitle());
                }
                // 根据通知类型设置动作
                switch (notification.getType()) {
                    case "LIKE" -> vo.setAction("点赞了你的文章");
                    case "COMMENT" -> vo.setAction("评论了你的文章");
                    default -> vo.setAction("关注了你的文章");
                }
            } else if ("comment".equals(notification.getTargetType())) {
                Comment comment = commentMap.get(notification.getTargetId());
                if (comment != null) {
                    Article article = articleMap.get(comment.getArticleId());
                    if (article != null) {
                        vo.setTargetTitle(article.getTitle());
                    }
                }
                // 评论相关的动作
                switch (notification.getType()) {
                    case "LIKE" -> vo.setAction("点赞了你的评论");
                    case "REPLY" -> vo.setAction("回复了你的评论");
                    default -> vo.setAction("关注了你的评论");
                }
            } else if ("system".equals(notification.getTargetType())) {
                vo.setAction("系统通知");
            }
            
            return vo;
        }).collect(Collectors.toList());

        return new PageResult<>(voList, notificationPage.getTotal(), page, size);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAsRead(Long userId, Long notificationId) {
        return lambdaUpdate()
                .eq(Notification::getId, notificationId)
                .eq(Notification::getUserId, userId)
                .set(Notification::getIsRead, true)
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAllAsRead(Long userId) {
        return lambdaUpdate()
                .eq(Notification::getUserId, userId)
                .set(Notification::getIsRead, true)
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteNotification(Long userId, Long notificationId) {
        return remove(new LambdaQueryWrapper<Notification>()
                .eq(Notification::getId, notificationId)
                .eq(Notification::getUserId, userId));
    }

    @Override
    public int getUnreadCount(Long userId) {
        log.debug("Getting unread count for user: {}", userId);
        LambdaQueryWrapper<Notification> wrapper = new LambdaQueryWrapper<Notification>()
            .eq(Notification::getUserId, userId)
            .eq(Notification::getIsRead, false);
        long count = notificationMapper.selectCount(wrapper);
        log.debug("Found {} unread notifications for user {}", count, userId);
        return (int) count;
    }

    @Override
    public void createNotification(Long userId, Long senderId, NotificationType type,
                                 Long targetId, NotificationTargetType targetType, String content) {
        log.debug("Creating notification for user {}: type={}, content={}", userId, type, content);
        
        Notification notification = new Notification();
        notification.setUserId(userId);
        notification.setSenderId(senderId);
        notification.setType(type.name());
        notification.setTargetId(targetId);
        notification.setTargetType(targetType.name());
        notification.setContent(content);
        notification.setIsRead(false);
        notification.setCreatedAt(LocalDateTime.now());
        
        // 保存通知
        this.save(notification);
        
        // 发布通知事件
        try {
            NotificationEvent event = new NotificationEvent(this, userId, notification, this.getUnreadCount(userId));
            eventPublisher.publishEvent(event);
            log.debug("Published notification event for user {}", userId);
        } catch (Exception e) {
            log.error("Failed to publish notification event", e);
        }
    }
} 