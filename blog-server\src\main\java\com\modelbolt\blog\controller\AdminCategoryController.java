package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.model.dto.category.CategoryVO;
import com.modelbolt.blog.model.dto.category.CreateCategoryDTO;
import com.modelbolt.blog.service.CategoryService;
import com.modelbolt.blog.annotation.OperationLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/admin/category")
@RequiredArgsConstructor
@Tag(name = "管理后台-分类管理", description = "管理后台分类管理相关接口")
@PreAuthorize("hasRole('ADMIN')")
public class AdminCategoryController {

    private final CategoryService categoryService;

    @Operation(summary = "获取所有分类")
    @GetMapping("/list")
    public ApiResponse<List<CategoryVO>> getAllCategories() {
        log.debug("Getting all categories");
        return ApiResponse.success(categoryService.getCategories());
    }

    @Operation(summary = "获取分类树形列表")
    @GetMapping("/tree")
    public ApiResponse<List<CategoryVO>> getCategoryTree() {
        log.debug("Getting category tree");
        return ApiResponse.success(categoryService.getCategoryTree());
    }

    @Operation(summary = "创建分类")
    @PostMapping
    @OperationLog("创建分类")
    public ApiResponse<CategoryVO> createCategory(
        @Valid @RequestBody CreateCategoryDTO dto
    ) {
        log.debug("Creating category: {}", dto);
        return ApiResponse.success(categoryService.createCategory(dto));
    }

    @Operation(summary = "更新分类")
    @PutMapping("/{id}")
    @OperationLog("更新分类")
    public ApiResponse<CategoryVO> updateCategory(
        @Parameter(description = "分类ID") @PathVariable Long id,
        @Valid @RequestBody CreateCategoryDTO dto
    ) {
        log.debug("Updating category {}: {}", id, dto);
        return ApiResponse.success(categoryService.updateCategory(id, dto));
    }

    @Operation(summary = "删除分类")
    @DeleteMapping("/{id}")
    @OperationLog("删除分类")
    public ApiResponse<Void> deleteCategory(
        @Parameter(description = "分类ID") @PathVariable Long id
    ) {
        log.debug("Deleting category: {}", id);
        categoryService.deleteCategory(id);
        return ApiResponse.success();
    }

    @Operation(summary = "更新分类排序")
    @PutMapping("/{id}/order")
    @OperationLog("更新分类排序")
    public ApiResponse<CategoryVO> updateCategoryOrder(
        @Parameter(description = "分类ID") @PathVariable Long id,
        @Parameter(description = "排序号") @RequestParam Integer orderNum
    ) {
        log.debug("Updating category order: {} -> {}", id, orderNum);
        return ApiResponse.success(categoryService.updateCategoryOrder(id, orderNum));
    }
} 