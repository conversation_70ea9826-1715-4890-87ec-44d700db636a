import { http } from '@/utils/request'
import type { R } from '@/types/common'
import type { PosterConfig } from '@/types/share'

/**
 * 生成分享海报
 * @param config 海报配置
 */
export const generatePoster = (config: PosterConfig) => {
  return http.post<R<string>>('/share/poster', config)
}

/**
 * 记录分享事件
 * @param articleId 文章ID
 * @param platform 分享平台
 */
export const recordShare = (articleId: number, platform: string) => {
  return http.post<R<void>>('/share/record', { articleId, platform })
} 