package com.modelbolt.blog.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 系统配置实体
 */
@Data
@Accessors(chain = true)
@TableName("system_config")
@Schema(description = "系统配置实体")
public class SystemConfig {
    @TableId(value = "`key`")
    @Schema(description = "配置键")
    private String key;

    @Schema(description = "配置值")
    private String value;

    @Schema(description = "配置描述")
    private String description;
} 