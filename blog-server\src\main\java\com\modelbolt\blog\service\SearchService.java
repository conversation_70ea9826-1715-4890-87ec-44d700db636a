package com.modelbolt.blog.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.modelbolt.blog.model.vo.SearchVO;
import com.modelbolt.blog.model.vo.HotSearchVO;
import java.util.List;

public interface SearchService {
    IPage<SearchVO> search(String keyword, String type, Integer page, Integer size);
    List<SearchVO> getSuggestions(String keyword);
    List<HotSearchVO> getHotSearches();
} 