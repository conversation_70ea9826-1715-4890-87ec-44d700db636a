import { createRouter, createWebHistory } from 'vue-router'
import { useAdminStore } from '@/stores/admin'
import AdminLayout from '@/layouts/AdminLayout.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/auth/Login.vue'),
      meta: { guest: true }
    },
    {
      path: '/',
      component: AdminLayout,
      children: [
        {
          path: '',
          name: 'dashboard',
          component: () => import('@/views/dashboard/index.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'article',
          name: 'article',
          component: () => import('@/views/article/index.vue'),
          meta: { 
            requiresAuth: true,
            title: '文章管理'
          }
        },
        {
          path: 'article/edit/:id?',
          name: 'articleEdit',
          component: () => import('@/views/article/edit.vue'),
          meta: { 
            requiresAuth: true,
            title: '编辑文章'
          }
        },
        {
          path: 'article/preview/:id',
          name: 'articlePreview',
          component: () => import('@/views/article/preview.vue'),
          meta: { 
            requiresAuth: true,
            title: '预览文章'
          }
        },
        {
          path: 'category',
          name: 'category',
          component: () => import('@/views/category/index.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'tag',
          name: 'tag',
          component: () => import('@/views/tag/index.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'comment',
          name: 'comment',
          component: () => import('@/views/comment/index.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'user',
          name: 'user',
          component: () => import('@/views/user/index.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'setting',
          name: 'setting',
          component: () => import('@/views/setting/index.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'profile',
          name: 'Profile',
          component: () => import('@/views/profile/index.vue'),
          meta: {
            title: '个人资料',
            requiresAuth: true
          }
        }
      ]
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const adminStore = useAdminStore()
  const token = adminStore.token
  
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const isGuestPage = to.matched.some(record => record.meta.guest)
  
  if (requiresAuth && !token) {
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
  } else if (isGuestPage && token) {
    next('/')
  } else {
    next()
  }
})

export default router 