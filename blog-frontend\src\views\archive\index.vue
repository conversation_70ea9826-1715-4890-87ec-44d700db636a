<template>
  <div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row gap-8">
      <!-- 侧边栏筛选 -->
      <div class="md:w-80">
        <archive-filter
          :stats="archiveStore.stats"
          :categories="categories"
          :tags="tags"
        />
      </div>

      <!-- 主要内容 -->
      <div class="flex-1">
        <timeline-list
          :archives="archiveStore.archives"
          :loading="archiveStore.loading"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useArchiveStore } from '@/stores/archive'
import { getCategories } from '@/api/article'
import { getAllTags } from '@/api/tag'
import TimelineList from './components/TimelineList.vue'
import ArchiveFilter from './components/ArchiveFilter.vue'
import type { CategoryVO, TagVO } from '@/types/article'

const archiveStore = useArchiveStore()

// 分类和标签数据
const categories = ref<CategoryVO[]>([])
const tags = ref<TagVO[]>([])

// 加载分类和标签数据
const loadFilterData = async () => {
  try {
    const [categoriesRes, tagsRes] = await Promise.all([
      getCategories(),
      getAllTags()
    ])
    
    if (categoriesRes.code === 200) {
      categories.value = categoriesRes.data
    }
    
    if (tagsRes.code === 200) {
      tags.value = tagsRes.data
    }
  } catch (error) {
    console.error('加载筛选数据失败:', error)
  }
}

// 初始化数据
onMounted(async () => {
  await Promise.all([
    archiveStore.fetchArchives(),
    archiveStore.fetchStats(),
    loadFilterData()
  ])
})
</script>

<style scoped>
/* 响应式布局样式 */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
</style> 