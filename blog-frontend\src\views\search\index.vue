<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 搜索结果统计 -->
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-gray-900">
        搜索结果: {{ searchStore.keyword }}
      </h1>
      <p class="text-gray-500 mt-2">
        共找到 {{ total }} 条相关结果
      </p>
    </div>

    <!-- 搜索结果过滤器 -->
    <div class="mb-6">
      <el-radio-group v-model="currentType" size="large" @change="handleTypeChange">
        <el-radio-button value="all">全部</el-radio-button>
        <el-radio-button value="article">文章</el-radio-button>
        <el-radio-button value="tag">标签</el-radio-button>
        <el-radio-button value="category">分类</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 搜索结果列表 -->
    <div class="space-y-6">
      <el-empty v-if="results.length === 0" description="暂无搜索结果" />
      
      <template v-else>
        <!-- 文章结果 -->
        <div v-for="item in results" :key="item.id" class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
          <template v-if="item.type === 'article'">
            <router-link :to="`/article/${item.id}`" class="block" @click="handleArticleClick(item.id)">
              <h2 class="text-xl font-semibold text-gray-900 mb-2" v-html="highlightKeyword(item.title, searchStore.keyword)" />
              <p class="text-gray-600 mb-4 line-clamp-2" v-html="highlightKeyword(item.summary, searchStore.keyword)" />
              <div class="flex items-center text-sm text-gray-500">
                <span class="flex items-center">
                  <el-icon class="mr-1"><Calendar /></el-icon>
                  {{ formatDate(item.createTime) }}
                </span>
                <span class="mx-4">|</span>
                <span class="flex items-center">
                  <el-icon class="mr-1"><View /></el-icon>
                  {{ item.viewCount }} 阅读
                </span>
              </div>
            </router-link>
          </template>

          <!-- 标签结果 -->
          <template v-else-if="item.type === 'tag'">
            <router-link :to="{ name: 'tag-detail', params: { id: item.id }}" class="flex items-center">
              <el-tag size="large" class="mr-3">{{ item.name }}</el-tag>
              <span class="text-gray-500">{{ item.articleCount }} 篇文章</span>
            </router-link>
          </template>

          <!-- 分类结果 -->
          <template v-else-if="item.type === 'category'">
            <router-link :to="{ name: 'category-detail', params: { id: item.id }}" class="flex items-center">
              <el-tag type="success" size="large" class="mr-3">{{ item.name }}</el-tag>
              <span class="text-gray-500">{{ item.articleCount }} 篇文章</span>
            </router-link>
          </template>
        </div>
      </template>
    </div>

    <!-- 分页 -->
    <div class="mt-8 flex justify-center">
      <el-pagination
        v-if="total > 0"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useSearchStore } from '@/stores/search'
import { Calendar, View } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/date'

const route = useRoute()
const router = useRouter()
const searchStore = useSearchStore()

const currentType = ref('all')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const results = ref<any[]>([])
const loading = ref(false)

// 高亮关键词
const highlightKeyword = (text: string, keyword: string) => {
  if (!keyword || !text) return text
  const reg = new RegExp(`(${keyword})`, 'gi')
  return text.replace(reg, '<span class="text-blue-600">$1</span>')
}

// 获取搜索结果
const fetchSearchResults = async () => {
  if (!searchStore.keyword) {
    results.value = []
    total.value = 0
    return
  }
  
  loading.value = true
  try {
    const params = {
      keyword: searchStore.keyword,
      type: currentType.value,
      page: currentPage.value,
      size: pageSize.value
    }
    
    const response = await searchStore.search(params)
    if (response) {
      results.value = response.records || []
      total.value = response.total || 0
    } else {
      results.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('搜索失败:', error)
    results.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 处理类型切换
const handleTypeChange = () => {
  currentPage.value = 1
  fetchSearchResults()
}

// 处理页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchSearchResults()
}

// 处理每页数量变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchSearchResults()
}

// 处理文章点击
const handleArticleClick = async (articleId: number) => {
  // 如果是同一篇文章，强制刷新
  if (route.params.id === articleId.toString()) {
    await router.replace({ 
      name: 'article-detail', 
      params: { id: articleId }
    })
    window.location.reload()
  } else {
    await router.push({ 
      name: 'article-detail', 
      params: { id: articleId }
    })
  }
}

// 监听路由参数变化
watch(
  () => route.query.keyword,
  (newKeyword) => {
    if (newKeyword) {
      searchStore.keyword = newKeyword as string
      currentPage.value = 1
      fetchSearchResults()
    }
  }
)

// 初始化
onMounted(() => {
  const keyword = route.query.keyword
  if (keyword) {
    searchStore.keyword = keyword as string
    fetchSearchResults()
  }
})
</script> 