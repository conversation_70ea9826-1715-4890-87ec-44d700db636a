<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { tagApi } from '@/api/tag'
import type { TagVO } from '@/types/tag'

interface Tag extends TagVO {
  isEditing?: boolean
}

const loading = ref(false)
const tags = ref<Tag[]>([])
const editingTag = ref<Tag | null>(null)
const modalVisible = ref(false)
const modalLoading = ref(false)
const newTagName = ref('')

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
    sorter: (a, b) => a.id - b.id,
    defaultSortOrder: 'ascend'
  },
  {
    title: '标签名称',
    dataIndex: 'name',
  },
  {
    title: '文章数量',
    dataIndex: 'articleCount',
    width: 100,
    sorter: (a, b) => a.articleCount - b.articleCount
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
  },
]

const fetchTags = async () => {
  try {
    loading.value = true
    const { data } = await tagApi.getList()
    if (data) {
      // 不需要手动排序，让表格控件处理排序
      tags.value = data
    }
  } catch (error) {
    console.error('获取标签列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCreate = async () => {
  if (!newTagName.value.trim()) {
    message.warning('请输入标签名称')
    return
  }

  try {
    modalLoading.value = true
    await tagApi.create({ name: newTagName.value.trim() })
    message.success('创建成功')
    modalVisible.value = false
    newTagName.value = ''
    await fetchTags()
  } catch (error) {
    console.error('创建标签失败:', error)
  } finally {
    modalLoading.value = false
  }
}

const handleEdit = (record: Tag) => {
  editingTag.value = { ...record }
  record.isEditing = true
}

const handleSave = async (record: Tag) => {
  if (!editingTag.value) return
  if (!editingTag.value.name.trim()) {
    message.warning('请输入标签名称')
    return
  }

  try {
    await tagApi.update(record.id, { name: editingTag.value.name.trim() })
    message.success('更新成功')
    record.name = editingTag.value.name
    record.isEditing = false
    editingTag.value = null
    await fetchTags()
  } catch (error) {
    console.error('更新标签失败:', error)
  }
}

const handleCancel = (record: Tag) => {
  record.isEditing = false
  editingTag.value = null
}

const handleDelete = (record: Tag) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除标签"${record.name}"吗？`,
    okText: '确定',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      try {
        await tagApi.delete(record.id)
        message.success('删除成功')
        await fetchTags()
      } catch (error) {
        console.error('删除标签失败:', error)
      }
    },
  })
}

const handleTableChange = (pagination: any, filters: any, sorter: any) => {
  console.log('Table change:', { pagination, filters, sorter })
  // 如果是多列排序，sorter 是数组
  const { field, order } = Array.isArray(sorter) ? sorter[0] : sorter
  
  if (field && order) {
    const sortedTags = [...tags.value].sort((a, b) => {
      const isAsc = order === 'ascend'
      if (field === 'id') {
        return isAsc ? a.id - b.id : b.id - a.id
      }
      if (field === 'articleCount') {
        return isAsc ? a.articleCount - b.articleCount : b.articleCount - a.articleCount
      }
      return 0
    })
    tags.value = sortedTags
  }
}

onMounted(() => {
  fetchTags()
})
</script>

<template>
  <div class="tag-list">
    <div class="mb-4">
      <a-button type="primary" @click="modalVisible = true">
        <template #icon>
          <PlusOutlined />
        </template>
        新建标签
      </a-button>
    </div>

    <a-table
      :columns="columns"
      :data-source="tags"
      :loading="loading"
      row-key="id"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'name'">
          <div v-if="record.isEditing">
            <a-input
              v-model:value="editingTag!.name"
              placeholder="请输入标签名称"
              @pressEnter="handleSave(record)"
            />
          </div>
          <span v-else>{{ record.name }}</span>
        </template>

        <template v-if="column.key === 'action'">
          <a-space>
            <template v-if="record.isEditing">
              <a-button type="link" @click="handleSave(record)">保存</a-button>
              <a-button type="link" @click="handleCancel(record)">取消</a-button>
            </template>
            <template v-else>
              <a-button type="link" @click="handleEdit(record)">编辑</a-button>
              <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
            </template>
          </a-space>
        </template>
      </template>
    </a-table>

    <a-modal
      v-model:visible="modalVisible"
      title="新建标签"
      :confirm-loading="modalLoading"
      @ok="handleCreate"
    >
      <a-form layout="vertical">
        <a-form-item label="标签名称" required>
          <a-input
            v-model:value="newTagName"
            placeholder="请输入标签名称"
            @pressEnter="handleCreate"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<style scoped>
.tag-list {
  padding: 24px;
  background: #fff;
  border-radius: 2px;
}
</style>