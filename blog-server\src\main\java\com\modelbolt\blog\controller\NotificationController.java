package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.vo.NotificationVO;
import com.modelbolt.blog.service.NotificationService;
import com.modelbolt.blog.utils.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Tag(name = "通知管理", description = "通知相关接口")
@RestController
@RequestMapping("/user/notifications")
@RequiredArgsConstructor
public class NotificationController {

    private final NotificationService notificationService;

    @Operation(summary = "获取通知列表")
    @GetMapping
    public ApiResponse<PageResult<NotificationVO>> getNotifications(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "通知类型") @RequestParam(defaultValue = "all") String type) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<NotificationVO> result = notificationService.getUserNotifications(userId, type, page, size);
        return ApiResponse.success(result);
    }

    @Operation(summary = "标记通知已读")
    @PutMapping("/{id}/read")
    public ApiResponse<Void> markAsRead(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        boolean success = notificationService.markAsRead(userId, id);
        return success ? ApiResponse.success() : ApiResponse.error("标记已读失败");
    }

    @Operation(summary = "标记所有通知已读")
    @PutMapping("/read-all")
    public ApiResponse<Void> markAllAsRead() {
        Long userId = SecurityUtils.getCurrentUserId();
        boolean success = notificationService.markAllAsRead(userId);
        return success ? ApiResponse.success() : ApiResponse.error("标记全部已读失败");
    }

    @Operation(summary = "删除通知")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteNotification(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        boolean success = notificationService.deleteNotification(userId, id);
        return success ? ApiResponse.success() : ApiResponse.error("删除通知失败");
    }

    @Operation(summary = "获取未读通知数量")
    @GetMapping("/unread-count")
    public ApiResponse<Integer> getUnreadCount() {
        Long userId = SecurityUtils.getCurrentUserId();
        int count = notificationService.getUnreadCount(userId);
        return ApiResponse.success(count);
    }
} 