<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { Lock } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

interface ResetPasswordForm {
  password: string
  confirmPassword: string
}

const resetPasswordForm = reactive<ResetPasswordForm>({
  password: '',
  confirmPassword: ''
})

const resetPasswordFormRef = ref<FormInstance>()

const validatePass = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else {
    if (resetPasswordForm.confirmPassword !== '') {
      if (resetPasswordFormRef.value) {
        resetPasswordFormRef.value.validateField('confirmPassword')
      }
    }
    callback()
  }
}

const validatePass2 = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== resetPasswordForm.password) {
    callback(new Error('两次输入密码不一致!'))
  } else {
    callback()
  }
}

const rules = reactive<FormRules>({
  password: [
    { required: true, validator: validatePass, trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, validator: validatePass2, trigger: 'blur' }
  ]
})

const loading = ref(false)

const handleSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  
  await formEl.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const token = route.query.token as string
        if (!token) {
          ElMessage.error('重置链接无效')
          return
        }
        
        await userStore.resetPassword({
          token,
          password: resetPasswordForm.password
        })
        
        ElMessage.success('密码重置成功')
        router.push('/auth/login')
      } catch (error: any) {
        ElMessage.error(error.response?.data?.message || error.message || '重置失败')
      } finally {
        loading.value = false
      }
    }
  })
}

const goToLogin = () => {
  router.push('/auth/login')
}
</script>

<template>
  <div class="auth-page">
    <div class="auth-container">
      <div class="auth-card">
        <div class="auth-header">
          <img src="@/assets/logo.png" alt="Logo" class="auth-logo">
          <h2 class="auth-title">重置密码</h2>
          <p class="auth-subtitle">请设置您的新密码</p>
        </div>

        <el-form
          ref="resetPasswordFormRef"
          :model="resetPasswordForm"
          :rules="rules"
          class="auth-form"
        >
          <el-form-item prop="password">
            <el-input
              v-model="resetPasswordForm.password"
              type="password"
              placeholder="新密码"
              :prefix-icon="Lock"
              class="auth-input"
            />
          </el-form-item>

          <el-form-item prop="confirmPassword">
            <el-input
              v-model="resetPasswordForm.confirmPassword"
              type="password"
              placeholder="确认新密码"
              :prefix-icon="Lock"
              class="auth-input"
            />
          </el-form-item>

          <el-button
            type="primary"
            class="submit-button"
            :loading="loading"
            @click="handleSubmit(resetPasswordFormRef)"
          >
            重置密码
          </el-button>

          <div class="back-link">
            <router-link to="/auth/login">
              返回登录
            </router-link>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f6f8ff 0%, #f0f4ff 100%);
  padding: 20px;
}

.auth-container {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
}

.auth-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  padding: 32px;
}

.auth-header {
  text-align: center;
  margin-bottom: 32px;
}

.auth-logo {
  width: 64px;
  height: 64px;
  margin-bottom: 24px;
}

.auth-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.auth-subtitle {
  font-size: 14px;
  color: #666;
}

.auth-form {
  .el-form-item {
    margin-bottom: 24px;
  }
}

.auth-input {
  :deep(.el-input__wrapper) {
    border-radius: 8px;
    height: 44px;
    
    &.is-focus {
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }
  }
}

.submit-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  background: var(--primary-color);
  border: none;
  border-radius: 8px;
  margin-bottom: 24px;
  
  &:hover {
    background: var(--primary-color-hover);
    transform: translateY(-1px);
  }
}

.back-link {
  text-align: center;
  font-size: 14px;
  
  a {
    color: var(--primary-color);
    text-decoration: none;
    
    &:hover {
      color: var(--primary-color-hover);
    }
  }
}
</style> 