import { http } from '@/utils/request'
import type { R, PageResult } from '@/types/common'
import type { UserCommentVO, UserCommentQuery } from '@/types/userComment'

// 获取用户评论列表
export const getUserComments = (params: UserCommentQuery) => {
  return http.get<R<PageResult<UserCommentVO>>>('/user/comments', { params })
}

// 删除用户评论
export const deleteUserComment = (id: number) => {
  return http.delete<R<void>>(`/user/comments/${id}`)
} 