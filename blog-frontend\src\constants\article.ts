// 文章状态
export const ARTICLE_STATUS = {
  DRAFT: 0,
  PUBLISHED: 1,
  DISABLED: 2
} as const

// 文章状态选项
export const ARTICLE_STATUS_OPTIONS = [
  { label: '草稿', value: ARTICLE_STATUS.DRAFT },
  { label: '已发布', value: ARTICLE_STATUS.PUBLISHED },
  { label: '已下架', value: ARTICLE_STATUS.DISABLED }
]

// 文章状态标签样式
export const ARTICLE_STATUS_TAG_TYPE = {
  [ARTICLE_STATUS.DRAFT]: 'info',
  [ARTICLE_STATUS.PUBLISHED]: 'success',
  [ARTICLE_STATUS.DISABLED]: 'danger'
}

// 文章状态文本
export const ARTICLE_STATUS_TEXT = {
  [ARTICLE_STATUS.DRAFT]: '草稿',
  [ARTICLE_STATUS.PUBLISHED]: '已发布',
  [ARTICLE_STATUS.DISABLED]: '已下架'
} 