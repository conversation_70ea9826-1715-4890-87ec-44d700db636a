package com.modelbolt.blog.config;

import com.modelbolt.blog.model.entity.User;
import com.modelbolt.blog.model.entity.RewardConfig;
import com.modelbolt.blog.model.entity.AdminSettings;
import com.modelbolt.blog.mapper.UserMapper;
import com.modelbolt.blog.mapper.RewardConfigMapper;
import com.modelbolt.blog.mapper.AdminSettingsMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.beans.factory.annotation.Value;
import org.apache.commons.io.FileUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.util.Arrays;

@Slf4j
@Component
@RequiredArgsConstructor
public class InitialDataConfig implements CommandLineRunner {

    private final UserMapper userMapper;
    private final RewardConfigMapper rewardConfigMapper;
    private final AdminSettingsMapper adminSettingsMapper;
    private final PasswordEncoder passwordEncoder;

    @Value("${file.upload.path}")
    private String uploadPath;

    // 头像URL配置
    private static final String DEFAULT_AVATAR = "/images/avatar/default-avatar.png";
    private static final String ADMIN_AVATAR = "/images/avatar/admin-avatar.png";

    // 静态资源目录配置
    private static final String[] REQUIRED_DIRECTORIES = {
        "article/cover",
        "avatar",
        "qrcode"
    };

    // 源文件在resources/static/images/下的相对路径
    private static final String[][] REQUIRED_FILES = {
        {"article/cover/default-cover.jpg", "article/cover/default-cover.jpg"},
        {"avatar/admin-avatar.png", "avatar/admin-avatar.png"},
        {"avatar/default-avatar.png", "avatar/default-avatar.png"},
        {"qrcode/alipay_dadong.jpg", "qrcode/alipay_dadong.jpg"},
        {"qrcode/wechat_dadong.jpg", "qrcode/wechat_dadong.jpg"}
    };

    @Override
    @Transactional
    public void run(String... args) {
        try {
            // cleanAndInitStaticResources();
            initAdminUser();
            initTestUser();
            initAdminRewardConfig();
            initAdminSettings();
        } catch (Exception e) {
            log.error("初始化数据失败", e);
        }
    }

    private void cleanAndInitStaticResources() {
        try {
            Path imagesPath = Paths.get(uploadPath, "");
            
            // 如果目录存在，先完全删除
            if (Files.exists(imagesPath)) {
                FileUtils.deleteDirectory(imagesPath.toFile());
                log.info("已清理旧的静态资源目录");
            }

            // 创建基础images目录
            Files.createDirectories(imagesPath);
            log.info("创建基础images目录成功: {}", imagesPath);

            // 创建必要的目录
            for (String dir : REQUIRED_DIRECTORIES) {
                Path dirPath = Paths.get(imagesPath.toString(), dir);
                Files.createDirectories(dirPath);
                log.info("创建目录成功: {}", dirPath);
            }

            // 复制必要的文件
            for (String[] fileInfo : REQUIRED_FILES) {
                String targetPath = fileInfo[0];
                String sourcePath = "static/images/" + fileInfo[1];

                Path targetFilePath = Paths.get(imagesPath.toString(), targetPath);
                Resource resource = new ClassPathResource(sourcePath);
                Files.copy(resource.getInputStream(), targetFilePath, StandardCopyOption.REPLACE_EXISTING);
                log.info("复制文件成功: {}", targetFilePath);
            }
            
            log.info("静态资源初始化完成");
        } catch (IOException e) {
            log.error("初始化静态资源失败", e);
        }
    }

    private void initAdminUser() {
        // 检查管理员是否已存在
        if (userMapper.findByUsername("admin") == null) {
            User admin = new User();
            admin.setUsername("admin");
            admin.setPassword(passwordEncoder.encode("dadongwo")); // 管理员默认密码
            admin.setEmail("<EMAIL>");
            admin.setAvatar(ADMIN_AVATAR); // 设置管理员头像
            admin.setRole("ADMIN");
            admin.setStatus(1);
            admin.setCreatedAt(LocalDateTime.now());
            admin.setUpdatedAt(LocalDateTime.now());

            userMapper.insert(admin);
            log.info("初始化管理员账号成功");
        } else {
            log.info("管理员账号已存在，跳过初始化");
        }
    }

    private void initTestUser() {
        // 检查测试用户是否已存在
        if (userMapper.findByUsername("test") == null) {
            User testUser = new User();
            testUser.setUsername("test");
            testUser.setPassword(passwordEncoder.encode("123456")); // 测试用户默认密码
            testUser.setEmail("<EMAIL>");
            testUser.setAvatar(DEFAULT_AVATAR); // 设置默认头像
            testUser.setRole("USER");
            testUser.setStatus(1);
            testUser.setCreatedAt(LocalDateTime.now());
            testUser.setUpdatedAt(LocalDateTime.now());

            try {
                // 使用 MyBatis-Plus 的 save 方法，避免直接使用表名
                int result = userMapper.insert(testUser);
                if (result > 0) {
                    log.info("初始化测试账号成功");
                } else {
                    log.error("初始化测试账号失败");
                }
            } catch (Exception e) {
                log.error("初始化测试账号失败", e);
            }
        } else {
            log.info("测试账号已存在，跳过初始化");
        }
    }

    private void initAdminRewardConfig() {
        User admin = userMapper.findByUsername("admin");
        if (admin != null) {
            // 检查是否已存在打赏配置
            RewardConfig existingConfig = rewardConfigMapper.selectByUserId(admin.getId());
            if (existingConfig == null) {
                RewardConfig rewardConfig = new RewardConfig();
                rewardConfig.setUserId(admin.getId());
                rewardConfig.setEnabled(true);
                rewardConfig.setAmounts(Arrays.asList(5, 10, 20, 50, 100));
                rewardConfig.setQrcodeWechat("/images/qrcode/wechat_dadong.jpg");
                rewardConfig.setQrcodeAlipay("/images/qrcode/alipay_dadong.jpg");
                rewardConfig.setCreatedAt(LocalDateTime.now());
                rewardConfig.setUpdatedAt(LocalDateTime.now());

                rewardConfigMapper.insert(rewardConfig);
                log.info("初始化管理员打赏配置成功");
            } else {
                log.info("管理员打赏配置已存在，跳过初始化");
            }
        } else {
            log.info("管理员账号不存在，跳过打赏配置初始化");
        }
    }

    private void initAdminSettings() {
        User admin = userMapper.findByUsername("admin");
        if (admin != null) {
            // 检查是否已存在管理员设置
            AdminSettings existingSettings = adminSettingsMapper.selectByAdminId(admin.getId());
            if (existingSettings == null) {
                AdminSettings adminSettings = new AdminSettings();
                adminSettings.setAdminId(admin.getId());
                adminSettings.setEmailVerificationEnabled(false);
                adminSettings.setCreatedAt(LocalDateTime.now());
                adminSettings.setUpdatedAt(LocalDateTime.now());

                adminSettingsMapper.insert(adminSettings);
                log.info("初始化管理员设置成功");
            } else {
                log.info("管理员设置已存在，跳过初始化");
            }
        } else {
            log.info("管理员账号不存在，跳过管理员设置初始化");
        }
    }
} 