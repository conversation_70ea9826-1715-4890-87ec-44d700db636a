package com.modelbolt.blog.exception.business;

import com.modelbolt.blog.exception.BusinessException;

/**
 * 标签相关异常类
 */
public class TagException extends BusinessException {
    
    private TagException(int code, String message) {
        super(code, message);
    }
    
    public static class TagNotFound extends TagException {
        public TagNotFound() {
            super(404, "标签不存在");
        }
    }
    
    public static class DuplicateName extends TagException {
        public DuplicateName() {
            super(400, "标签名称已存在，请使用其他名称");
        }
    }
    
    public static class HasArticles extends TagException {
        public HasArticles() {
            super(400, "该标签下还有关联的文章，无法删除");
        }
    }
} 