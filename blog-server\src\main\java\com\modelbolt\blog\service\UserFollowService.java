package com.modelbolt.blog.service;

import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.user.UserFollowDTO;

public interface UserFollowService {
    
    /**
     * 获取用户关注列表
     *
     * @param userId 用户ID
     * @param current 当前页
     * @param size 每页大小
     * @return 关注列表
     */
    PageResult<UserFollowDTO> getFollowingList(Long userId, Integer current, Integer size);
    
    /**
     * 获取用户粉丝列表
     *
     * @param userId 用户ID
     * @param current 当前页
     * @param size 每页大小
     * @return 粉丝列表
     */
    PageResult<UserFollowDTO> getFollowersList(Long userId, Integer current, Integer size);
    
    /**
     * 获取用户关注统计
     *
     * @param userId 用户ID
     * @return 关注统计
     */
    UserFollowDTO.FollowStats getFollowStats(Long userId);
    
    /**
     * 关注用户
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否成功
     */
    boolean followUser(Long followerId, Long followingId);
    
    /**
     * 取消关注
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否成功
     */
    boolean unfollowUser(Long followerId, Long followingId);
    
    /**
     * 检查是否已关注
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否已关注
     */
    boolean isFollowing(Long followerId, Long followingId);
} 