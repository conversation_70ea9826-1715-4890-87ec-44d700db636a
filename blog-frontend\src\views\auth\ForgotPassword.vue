<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { Message, Check } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

interface ForgotPasswordForm {
  email: string
}

const forgotPasswordForm = reactive<ForgotPasswordForm>({
  email: ''
})

const forgotPasswordFormRef = ref<FormInstance>()

const rules = reactive<FormRules>({
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
})

const loading = ref(false)
const resetSent = ref(false)

const handleSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  
  await formEl.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        await userStore.forgotPassword(forgotPasswordForm.email)
        resetSent.value = true
        ElMessage.success('重置密码邮件已发送，请查收')
      } catch (error: any) {
        ElMessage.error(error.message || '发送失败')
      } finally {
        loading.value = false
      }
    }
  })
}

const goToLogin = () => {
  router.push('/auth/login')
}
</script>

<template>
  <div class="auth-page">
    <div class="auth-container">
      <div class="auth-card">
        <div class="auth-header">
          <img src="@/assets/logo.png" alt="Logo" class="auth-logo">
          <h2 class="auth-title">找回密码</h2>
          <p class="auth-subtitle">请输入您的邮箱地址，我们将发送重置密码链接</p>
        </div>

        <template v-if="!resetSent">
          <el-form
            ref="forgotPasswordFormRef"
            :model="forgotPasswordForm"
            :rules="rules"
            class="auth-form"
          >
            <el-form-item prop="email">
              <el-input
                v-model="forgotPasswordForm.email"
                placeholder="邮箱地址"
                :prefix-icon="Message"
                class="auth-input"
              />
            </el-form-item>

            <el-button
              type="primary"
              class="submit-button"
              :loading="loading"
              @click="handleSubmit(forgotPasswordFormRef)"
            >
              发送重置密码邮件
            </el-button>

            <div class="back-link">
              <router-link to="/auth/login">
                返回登录
              </router-link>
            </div>
          </el-form>
        </template>

        <template v-else>
          <div class="success-message">
            <div class="success-icon">
              <el-icon class="check-icon"><Check /></el-icon>
            </div>
            <h3 class="success-title">重置密码邮件已发送</h3>
            <p class="success-description">
              我们已向您的邮箱发送了重置密码的链接，请查收邮件并按照提示操作。
            </p>
            <el-button
              type="primary"
              class="submit-button"
              @click="goToLogin"
            >
              返回登录
            </el-button>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f6f8ff 0%, #f0f4ff 100%);
  padding: 20px;
}

.auth-container {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
}

.auth-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  padding: 32px;
}

.auth-header {
  text-align: center;
  margin-bottom: 32px;
}

.auth-logo {
  width: 64px;
  height: 64px;
  margin-bottom: 24px;
}

.auth-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.auth-subtitle {
  font-size: 14px;
  color: #666;
  max-width: 300px;
  margin: 0 auto;
}

.auth-form {
  .el-form-item {
    margin-bottom: 24px;
  }
}

.auth-input {
  :deep(.el-input__wrapper) {
    border-radius: 8px;
    height: 44px;
    
    &.is-focus {
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }
  }
}

.submit-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  background: var(--primary-color);
  border: none;
  border-radius: 8px;
  margin-bottom: 24px;
  
  &:hover {
    background: var(--primary-color-hover);
    transform: translateY(-1px);
  }
}

.back-link {
  text-align: center;
  font-size: 14px;
  
  a {
    color: var(--primary-color);
    text-decoration: none;
    
    &:hover {
      color: var(--primary-color-hover);
    }
  }
}

.success-message {
  text-align: center;
  padding: 20px 0;
}

.success-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 24px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .check-icon {
    font-size: 32px;
    color: white;
  }
}

.success-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.success-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 24px;
  line-height: 1.6;
}
</style> 