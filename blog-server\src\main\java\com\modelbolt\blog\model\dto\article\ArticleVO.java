package com.modelbolt.blog.model.dto.article;

import com.modelbolt.blog.model.dto.tag.TagVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "文章视图对象")
public class ArticleVO {
    @Schema(description = "文章ID")
    private Long id;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "摘要")
    private String summary;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "封面图片URL")
    private String cover;

    @Schema(description = "作者ID")
    private Long authorId;

    @Schema(description = "作者名称")
    private String authorName;

    @Schema(description = "作者头像")
    private String authorAvatar;

    @Schema(description = "浏览量")
    private Integer viewCount;

    @Schema(description = "评论数")
    private Integer commentCount;

    @Schema(description = "点赞数")
    private Integer likeCount;

    @Schema(description = "收藏数")
    private Integer favoriteCount;

    @Schema(description = "是否置顶")
    private Boolean isTop;

    @Schema(description = "文章状态：0-草稿，1-已发布，2-已下架")
    private Integer status;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "标签列表")
    private List<TagVO> tags;

    @Schema(description = "当前用户是否点赞")
    private Boolean liked;

    @Schema(description = "当前用户是否收藏")
    private Boolean favorited;
} 