import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import type { ApiResponse } from '@/types/api'
import router from '@/router'

// 自定义AxiosRequestConfig类型
declare module 'axios' {
  interface AxiosRequestConfig {
    _retryCount?: number;
    _skipAuthRefresh?: boolean;
  }
}

// 创建axios实例
const http: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 防止多次弹出Token过期提示
let isTokenExpiredModalVisible = false
// 标记是否正在刷新令牌
let isRefreshing = false
// 等待令牌刷新的请求队列
let requestsQueue: Array<{
  resolve: (value: unknown) => void;
  reject: (reason?: any) => void;
  config: AxiosRequestConfig;
}> = []

// 处理队列中的请求
const processRequestsQueue = (error: any) => {
  requestsQueue.forEach(request => {
    if (error) {
      request.reject(error)
    } else {
      request.resolve(http(request.config))
    }
  })
  requestsQueue = []
}

// token 检查函数
const checkTokenValidity = async () => {
  const userStore = useUserStore()
  if (userStore.token) {
    try {
      await http.get('/user/check-token')
    } catch (error: any) {
      if (error.response?.status === 401) {
        // 使用公共方法处理认证错误
        handleAuthError(error.response)
      }
    }
  }
}

// 启动定期检查
let tokenCheckInterval: number | null = null

export const startTokenCheck = () => {
  if (tokenCheckInterval !== null) {
    clearInterval(tokenCheckInterval)
  }
  // 每5分钟检查一次token有效性
  tokenCheckInterval = window.setInterval(checkTokenValidity, 5 * 60 * 1000)
}

export const stopTokenCheck = () => {
  if (tokenCheckInterval !== null) {
    clearInterval(tokenCheckInterval)
    tokenCheckInterval = null
  }
}

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    const userStore = useUserStore()
    // 如果有token，添加到请求头
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }
    return config
  },
  (error) => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// 处理认证错误的通用方法
const handleAuthError = async (response: any) => {
  const userStore = useUserStore()
  const currentPath = router.currentRoute.value.fullPath

  // 获取详细的错误信息
  const errorData = response.data || {}
  const errorMessage = errorData.message || '登录已过期，请重新登录'
  const errorCode = errorData.error || 'AUTHENTICATION_FAILED'
  
  // 仅在没有显示过期提示的情况下显示
  if (!isTokenExpiredModalVisible) {
    isTokenExpiredModalVisible = true
    
    // 使用确认框替代普通消息提示
    try {
      await ElMessageBox.confirm(
        errorMessage,
        '登录提示',
        {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      // 用户点击了确认按钮
      userStore.logout()
      
      // 检查当前路由是否已经是登录页，避免重复跳转
      if (!currentPath.startsWith('/auth/')) {
        await router.replace({
          path: '/auth/login',
          query: { redirect: currentPath }
        })
      }
    } catch (err) {
      // 用户点击了取消按钮，只清除登录状态但不跳转
      userStore.logout()
    } finally {
      // 重置标志，允许下次显示提示
      setTimeout(() => {
        isTokenExpiredModalVisible = false
      }, 1000)
    }
  }
}

// 响应拦截器
http.interceptors.response.use(
  (response: AxiosResponse) => {
    const responseData = response.data as ApiResponse<any>
    const { code, message } = responseData

    // 如果code不是200，说明请求出错
    if (code !== 200) {
      ElMessage.error(message || '请求失败')
      return Promise.reject(new Error(message || '请求失败'))
    }

    return responseData as any
  },
  async (error) => {
    const originalRequest = error.config
    
    // 防止无限重试
    const retryCount = originalRequest._retryCount || 0
    if (retryCount > 3) {
      return Promise.reject(error)
    }
    
    // 检查是否是令牌过期错误
    if (error.response?.status === 401 
        && !originalRequest._skipAuthRefresh 
        && !originalRequest.url?.includes('/refresh-token')) {
          
      const userStore = useUserStore()
      
      // 如果有刷新令牌且没有正在刷新，尝试刷新令牌
      if (userStore.refreshToken && !isRefreshing) {
        isRefreshing = true
        
        try {
          // 尝试刷新令牌
          const refreshSuccess = await userStore.refreshAccessToken()
          
          if (refreshSuccess) {
            // 刷新成功，重试原始请求
            originalRequest._retryCount = (originalRequest._retryCount || 0) + 1
            
            // 更新原始请求的认证令牌
            originalRequest.headers.Authorization = `Bearer ${userStore.token}`
            
            // 处理队列中的请求
            processRequestsQueue(null)
            
            return http(originalRequest)
          } else {
            // 刷新失败，处理队列中的请求并显示登录模态框
            processRequestsQueue(error)
            await handleAuthError(error.response)
            return Promise.reject(error)
          }
        } catch (refreshError) {
          // 发生错误，处理队列中的请求并显示登录模态框
          processRequestsQueue(refreshError)
          await handleAuthError(error.response)
          return Promise.reject(error)
        } finally {
          isRefreshing = false
        }
      } else if (isRefreshing) {
        // 如果正在刷新，将请求加入队列
        return new Promise((resolve, reject) => {
          requestsQueue.push({
            resolve,
            reject,
            config: originalRequest
          })
        })
      } else {
        // 没有刷新令牌或已经尝试过刷新，显示登录模态框
        await handleAuthError(error.response)
      }
    } else {
      // 其他错误处理
      if (error.response) {
        const { status } = error.response
        
        switch (status) {
          case 403:
            ElMessage.error('没有权限访问')
            break
          case 404:
            ElMessage.error('请求的资源不存在')
            break
          case 500:
            ElMessage.error('服务器错误')
            break
          default:
            ElMessage.error(error.response.data?.message || '请求失败')
        }
      } else if (error.code === 'ECONNABORTED') {
        ElMessage.error('请求超时，请稍后重试')
      } else {
        ElMessage.error('网络错误，请检查网络连接')
      }
    }

    return Promise.reject(error)
  }
)

// 封装GET请求
export const get = <T = any>(url: string, config?: AxiosRequestConfig) => {
  return http.get<any, ApiResponse<T>>(url, config)
}

// 封装POST请求
export const post = <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => {
  return http.post<any, ApiResponse<T>>(url, data, config)
}

// 封装PUT请求
export const put = <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => {
  return http.put<any, ApiResponse<T>>(url, data, config)
}

// 封装DELETE请求
export const del = <T = any>(url: string, config?: AxiosRequestConfig) => {
  return http.delete<any, ApiResponse<T>>(url, config)
}

export { http } 