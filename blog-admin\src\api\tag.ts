import { http } from '@/utils/request'
import type { R } from '@/types/common'
import type { TagVO } from '@/types/tag'

/**
 * 标签管理相关接口
 */
export const tagApi = {
  /**
   * 获取标签列表
   */
  getList() {
    return http.get<R<TagVO[]>>('/admin/tag/list')
  },

  /**
   * 创建标签
   */
  create(data: { name: string }) {
    return http.post<R<TagVO>>('/admin/tag', data)
  },

  /**
   * 更新标签
   */
  update(id: number, data: { name: string }) {
    return http.put<R<TagVO>>(`/admin/tag/${id}`, data)
  },

  /**
   * 删除标签
   */
  delete(id: number) {
    return http.delete<R<void>>(`/admin/tag/${id}`)
  }
} 