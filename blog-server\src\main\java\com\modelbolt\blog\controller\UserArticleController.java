package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.article.ArticleVO;
import com.modelbolt.blog.model.dto.article.CreateArticleDTO;
import com.modelbolt.blog.model.dto.article.UpdateArticleDTO;
import com.modelbolt.blog.exception.business.AuthException;
import com.modelbolt.blog.service.ArticleService;
import com.modelbolt.blog.service.FileService;
import com.modelbolt.blog.utils.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;

@Tag(name = "用户文章管理", description = "用户文章管理相关接口")
@RestController
@RequestMapping("/user/articles")
@RequiredArgsConstructor
@SecurityRequirement(name = "Bearer")
public class UserArticleController {

    private final ArticleService articleService;
    private final FileService fileService;

    @Operation(summary = "获取用户文章列表")
    @GetMapping
    public ApiResponse<PageResult<ArticleVO>> getUserArticles(
            @Parameter(description = "关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "状态：draft-草稿，published-已发布") @RequestParam(required = false) String status,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer size) {
        return ApiResponse.success(articleService.getUserArticles(keyword, status, page, size));
    }

    @Operation(summary = "创建文章")
    @PostMapping
    public ApiResponse<Long> createArticle(@RequestBody @Valid CreateArticleDTO dto) {
        return ApiResponse.success(articleService.createArticle(dto));
    }

    @Operation(summary = "更新文章")
    @PutMapping("/{id}")
    public ApiResponse<Void> updateArticle(
            @Parameter(description = "文章ID") @PathVariable Long id,
            @RequestBody @Valid UpdateArticleDTO dto) {
        articleService.updateArticle(id, dto);
        return ApiResponse.success();
    }

    @Operation(summary = "删除文章")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteArticle(
            @Parameter(description = "文章ID") @PathVariable Long id) {
        articleService.deleteArticle(id);
        return ApiResponse.success();
    }

    @Operation(summary = "获取文章编辑信息")
    @GetMapping("/{id}/edit")
    public ApiResponse<ArticleVO> getArticleForEdit(
            @Parameter(description = "文章ID") @PathVariable Long id) {
        return ApiResponse.success(articleService.getArticleForEdit(id));
    }

    @Operation(summary = "上传文章封面")
    @PostMapping("/cover")
    public ApiResponse<String> uploadCover(@RequestParam("cover") MultipartFile cover) {
        Long userId = SecurityUtils.getCurrentUserId();
        if (userId == null) {
            throw new AuthException.UserNotFound();
        }
        
        // 调用文件上传服务
        String coverUrl = fileService.uploadImage(cover, "article/cover");
        return ApiResponse.<String>builder()
                .code(200)
                .data(coverUrl)
                .build();
    }
} 