package com.modelbolt.blog.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户配置实体
 */
@Data
@TableName("user_settings")
@Schema(description = "用户配置实体")
public class UserSettings {
    @TableId
    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "主题设置")
    private String theme;

    @Schema(description = "通知开关")
    private Boolean notificationEnabled;

    @Schema(description = "语言设置")
    private String language;
} 