/**
 * 文章状态
 */
export enum ArticleStatus {
  /** 草稿 */
  DRAFT = 0,
  /** 已发布 */
  PUBLISHED = 1,
  /** 已下架 */
  DISABLED = 2
}

/**
 * 文章表单数据
 */
export interface ArticleForm {
  /** 标题 */
  title: string
  /** 内容 */
  content: string
  /** 摘要 */
  summary: string
  /** 封面图片URL */
  cover: string
  /** 分类ID列表 */
  categoryIds: number[]
  /** 标签ID列表 */
  tagIds: number[]
  /** 状态 */
  status: ArticleStatus
  /** 是否置顶 */
  isTop: boolean
}

/**
 * 文章信息
 */
export interface Article extends ArticleForm {
  /** ID */
  id: number
  /** 浏览量 */
  viewCount: number
  /** 评论数 */
  commentCount: number
  /** 点赞数 */
  likeCount: number
  /** 收藏数 */
  favoriteCount: number
  /** 作者ID */
  authorId: number
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
}

/**
 * 文章查询参数
 */
export interface ArticleQuery {
  /** 页码 */
  current: number
  /** 每页大小 */
  size: number
  /** 关键词 */
  keyword?: string
  /** 状态 */
  status?: ArticleStatus
  /** 分类ID */
  categoryId?: number
  /** 标签ID */
  tagId?: number
} 