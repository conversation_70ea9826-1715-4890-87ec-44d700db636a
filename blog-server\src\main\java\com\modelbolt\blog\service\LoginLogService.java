package com.modelbolt.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.modelbolt.blog.model.entity.LoginLog;
import jakarta.servlet.http.HttpServletRequest;

public interface LoginLogService extends IService<LoginLog> {
    
    /**
     * 记录登录日志
     *
     * @param userId 用户ID
     * @param username 用户名
     * @param status 状态
     * @param msg 消息
     * @param request HTTP请求
     */
    void recordLoginLog(Long userId, String username, Integer status, String msg, HttpServletRequest request);
    
    /**
     * 清理指定天数之前的登录日志
     *
     * @param days 天数
     * @return 清理的记录数
     */
    int cleanLoginLog(int days);
} 