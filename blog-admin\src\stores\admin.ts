import { defineStore } from 'pinia'
import { ref } from 'vue'
import * as adminApi from '@/api/admin'
import type { AdminInfo } from '@/types/admin'
import { clearImageFailedCache, getRefreshedImageUrl } from '@/utils/image'

export const useAdminStore = defineStore('admin', () => {
  const token = ref(localStorage.getItem('token') || '')
  const refreshToken = ref(localStorage.getItem('refreshToken') || '')
  const user = ref<AdminInfo | null>(null)
  const needVerification = ref(false)

  async function login(loginData: any) {
    try {
      console.log('[Admin Login] Attempting login with:', loginData)
      const response = await adminApi.login(loginData)
      console.log('[Admin Login] Raw response:', response)
      
      if (response.code === 200 && response.data) {
        console.log('[Admin Login] Login successful, processing data:', response.data)
        
        if (response.data.needVerification) {
          console.log('[Admin Login] Email verification required')
          needVerification.value = true
          user.value = response.data.user || null
          return {
            needVerification: true,
            user: response.data.user
          }
        } else if (response.data.token) {
          console.log('[Admin Login] Setting token and user data')
          token.value = response.data.token
          
          // 存储刷新令牌
          if (response.data.refreshToken) {
            refreshToken.value = response.data.refreshToken
            localStorage.setItem('refreshToken', response.data.refreshToken)
          }
          
          if (user.value?.avatar !== response.data.user?.avatar) {
            clearImageFailedCache(user.value?.avatar)
          }
          user.value = response.data.user || null
          needVerification.value = false
          localStorage.setItem('token', response.data.token)
          
          console.log('[Admin Login] User data set to:', user.value)
          return {
            success: true,
            token: response.data.token,
            refreshToken: response.data.refreshToken,
            user: response.data.user
          }
        }
      }
      
      console.warn('[Admin Login] Invalid response format:', response)
      return {
        success: false,
        message: response.message || '登录失败'
      }
    } catch (error: any) {
      console.error('[Admin Login] Error:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || '登录失败'
      }
    }
  }

  // 尝试刷新令牌
  async function tryRefreshToken() {
    console.log('[Admin RefreshToken] Attempting to refresh token')
    
    if (!refreshToken.value) {
      console.error('[Admin RefreshToken] No refresh token available')
      return false
    }
    
    try {
      const response = await adminApi.refreshToken(refreshToken.value)
      
      if (response.code === 200 && response.data) {
        // 更新访问令牌
        token.value = response.data.accessToken
        localStorage.setItem('token', response.data.accessToken)
        console.log('[Admin RefreshToken] Token refreshed successfully')
        return true
      }
      
      console.error('[Admin RefreshToken] Failed to refresh token:', response.message)
      return false
    } catch (error: any) {
      console.error('[Admin RefreshToken] Error refreshing token:', error)
      return false
    }
  }

  async function sendVerificationCode(email: string) {
    try {
      const response = await adminApi.sendVerificationCode(email)
      console.log('[Admin Send Code] Response:', response)
      return response.data
    } catch (error: any) {
      console.error('[Admin Send Code] Error:', error)
      throw new Error(error.response?.data?.message || error.message || '发送验证码失败')
    }
  }

  async function getAdminInfo() {
    try {
      console.log('[Admin Info] Fetching admin info...')
      let response = await adminApi.getAdminInfo()
      
      // 添加响应数据校验
      if (response?.code === 200 && response.data?.user) {
        console.log('[Admin Info] Valid response received:', response.data.user)
        
        // 确保avatar字段存在且有效
        const newAvatar = response.data.user.avatar || ''
        if (user.value?.avatar !== newAvatar) {
          clearImageFailedCache(user.value?.avatar)
        }
        
        // 使用Object.assign确保响应式更新
        user.value = Object.assign({}, response.data.user)
        
        // 强制刷新头像（关键）
        if (user.value.avatar) {
          user.value.avatar = getRefreshedImageUrl(user.value.avatar)
        }
        
        console.log('[Admin Info] User data updated:', user.value)
        return user.value
      }
      
      // 添加请求次数限制
      let retryCount = 0
      const maxRetries = 3
      
      while (!response.data && retryCount < maxRetries) {
        console.warn(`[Admin Info] Retry ${retryCount + 1}/${maxRetries}`)
        await new Promise(resolve => setTimeout(resolve, 1000))
        response = await adminApi.getAdminInfo()
        retryCount++
      }

      if (response.code === 200 && response.data) {
        console.log('[Admin Info] Response code is 200')
        if (response.data) {
          if (user.value?.avatar !== response.data.avatar) {
            clearImageFailedCache(user.value?.avatar)
          }
          console.log('[Admin Info] Setting user data:', response.data)
          user.value = response.data
          console.log('[Admin Info] User data set to:', user.value)
          return response.data
        } else {
          console.warn('[Admin Info] Response data is empty')
        }
      } else {
        console.warn('[Admin Info] Response code is not 200:', response.code)
      }
      
      // 如果仍然失败则登出
      if(retryCount >= maxRetries) {
        console.error('[Admin Info] Max retries reached')
        await logout()
      }
      
      return null
    } catch (error: any) {
      console.error('[Admin Info] Error:', error)
      if (error.response?.status === 401) {
        console.log('[Admin Info] Unauthorized, clearing token and user')
        token.value = ''
        user.value = null
        localStorage.removeItem('token')
      }
      return null
    }
  }

  async function logout() {
    try {
      console.log('[Admin Logout] Logging out...')
      await adminApi.logout()
      console.log('[Admin Logout] Logout API call successful')
    } finally {
      console.log('[Admin Logout] Clearing local data')
      token.value = ''
      refreshToken.value = ''
      user.value = null
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
      clearImageFailedCache()
    }
  }

  return {
    token,
    refreshToken,
    user,
    needVerification,
    login,
    tryRefreshToken,
    sendVerificationCode,
    getAdminInfo,
    logout
  }
}) 