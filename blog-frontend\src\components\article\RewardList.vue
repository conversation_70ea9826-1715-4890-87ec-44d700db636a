<!-- 暂时注释整个组件 -->
<!--
<template>
  <div class="reward-list">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-bold">打赏记录</h3>
      <div class="text-blue-600 font-medium">
        总金额：{{ totalAmount }}元
      </div>
    </div>

    <!-- 打赏记录列表 -->
    <div v-loading="loading" class="space-y-4">
      <div
        v-for="record in records"
        :key="record.id"
        class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg"
      >
        <el-avatar 
          :size="40" 
          :src="getAvatarUrl(record.userAvatar)"
          @error="() => handleAvatarError($event, record)"
        >
          {{ record.username?.charAt(0)?.toUpperCase() || '?' }}
        </el-avatar>
        <div class="flex-1">
          <div class="flex justify-between items-center">
            <span class="font-medium">{{ record.username || '匿名用户' }}</span>
            <span class="text-red-600 font-medium">{{ record.amount }}元</span>
          </div>
          <div class="flex justify-between items-center text-sm text-gray-500 mt-1">
            <span>{{ record.message || '这位好心人没有留言' }}</span>
            <span>{{ formatDate(record.createdAt) }}</span>
          </div>
        </div>
      </div>

      <!-- 无数据提示 -->
      <el-empty
        v-if="!loading && records.length === 0"
        description="暂无打赏记录"
      />

      <!-- 加载更多 -->
      <div
        v-if="hasMore"
        class="text-center mt-4"
      >
        <el-button
          type="primary"
          text
          @click="loadMore"
          :loading="loading"
        >
          加载更多
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { RewardRecord } from '@/api/reward'
import { getRewardRecords, getTotalRewardAmount } from '@/api/reward'
import { formatDate } from '@/utils/date'
import { getAvatarUrl } from '@/utils/image'

const props = defineProps<{
  articleId: number
}>()

// 打赏记录数据
const records = ref<RewardRecord[]>([])
const totalAmount = ref(0)
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)

// 处理头像加载错误
const handleAvatarError = (event: Event, record: RewardRecord) => {
  const defaultAvatar = '/images/avatar/default-avatar.png'
  if (event?.target) {
    const img = event.target as HTMLImageElement
    img.src = defaultAvatar
  }
  // 防止重复请求，更新记录中的头像URL
  if (record) {
    record.userAvatar = defaultAvatar
  }
}

// 获取打赏记录
const fetchRewardRecords = async (page: number = 1) => {
  if (loading.value) return
  loading.value = true
  try {
    const { data } = await getRewardRecords(props.articleId, page, pageSize.value)
    if (page === 1) {
      records.value = data.records
    } else {
      records.value.push(...data.records)
    }
    hasMore.value = data.records.length === pageSize.value
  } catch (error) {
    console.error('获取打赏记录失败:', error)
    ElMessage.error('获取打赏记录失败')
  } finally {
    loading.value = false
  }
}

// 获取总打赏金额
const fetchTotalAmount = async () => {
  try {
    const { data } = await getTotalRewardAmount(props.articleId)
    totalAmount.value = data
  } catch (error) {
    console.error('获取总打赏金额失败:', error)
  }
}

// 加载更多
const loadMore = () => {
  if (!hasMore.value || loading.value) return
  currentPage.value++
  fetchRewardRecords(currentPage.value)
}

// 刷新数据
const refresh = () => {
  currentPage.value = 1
  hasMore.value = true
  fetchRewardRecords()
  fetchTotalAmount()
}

defineExpose({
  refresh
})

onMounted(() => {
  fetchRewardRecords()
  fetchTotalAmount()
})
</script>
-->