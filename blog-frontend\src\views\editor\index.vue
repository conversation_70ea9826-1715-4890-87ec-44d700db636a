<template>
  <div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-sm p-6">
      <div class="mb-6 flex justify-between items-center">
        <el-button type="info" plain @click="handleGoBack">
          <el-icon><ArrowLeft /></el-icon>
          <span>返回</span>
        </el-button>
        <div class="flex space-x-4">
          <el-button @click="saveDraft">保存草稿</el-button>
          <el-button type="primary" @click="publishArticle">发布文章</el-button>
        </div>
      </div>

      <div class="mb-6">
        <!-- 文章标题 -->
        <el-input
          v-model="article.title"
          placeholder="请输入文章标题"
          class="mb-4"
          :maxlength="200"
          show-word-limit
        />
        
        <!-- Markdown编辑器 -->
        <div class="editor-container">
          <v-md-editor
            v-model="article.content"
            height="600px"
          />
        </div>
      </div>

      <!-- 文章设置 -->
      <div class="mb-6">
        <el-form :model="article" label-width="80px">
          <el-form-item label="文章摘要">
            <el-input
              v-model="article.summary"
              type="textarea"
              :rows="3"
              placeholder="请输入文章摘要"
              :maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="封面图片">
            <el-upload
              class="avatar-uploader"
              :show-file-list="false"
              :http-request="customUpload"
              :before-upload="beforeImageUpload"
            >
              <img v-if="article.cover" :src="getCoverUrl(article.cover)" class="avatar" />
              <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            </el-upload>
          </el-form-item>

          <el-form-item label="文章分类">
            <el-select
              v-model="article.categoryIds"
              multiple
              placeholder="请选择文章分类"
            >
              <el-option
                v-for="category in categories"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="文章标签">
            <el-select
              v-model="article.tagIds"
              multiple
              placeholder="请选择标签"
              class="w-full"
            >
              <el-option
                v-for="tag in tags"
                :key="tag.id"
                :label="tag.name"
                :value="tag.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, ArrowLeft } from '@element-plus/icons-vue'
import { getAllCategories } from '@/api/category'
import { getAllTags, uploadArticleCover, getArticleForEdit, createArticle, updateArticle } from '@/api/article'
import type { CategoryVO, TagVO } from '@/types/article'
import { http } from '@/utils/request'
import { getCoverUrl } from '@/utils/image'

const route = useRoute()
const router = useRouter()
const isEdit = ref(false)
const articleId = ref<number | null>(null)

// 文章数据
const article = ref({
  title: '',
  content: '',
  summary: '',
  cover: '',
  categoryIds: [] as number[],
  tagIds: [] as number[],
  status: 'draft'
})

// 分类和标签数据
const categories = ref<CategoryVO[]>([])
const tags = ref<TagVO[]>([])

// 加载文章详情
const loadArticleDetail = async (id: number) => {
  try {
    const { data } = await getArticleForEdit(id)
    if (data) {
      article.value = {
        title: data.title,
        content: data.content,
        summary: data.summary || '',
        cover: data.cover || '',
        categoryIds: data.categoryIds || [],
        tagIds: data.tagIds || [],
        status: data.status
      }
      
      // 确保分类和标签数据已加载
      await fetchCategoriesAndTags()
    }
  } catch (error) {
    ElMessage.error('加载文章失败')
    console.error('加载文章失败:', error)
  }
}

// 获取分类和标签数据
const fetchCategoriesAndTags = async () => {
  try {
    const [categoriesRes, tagsRes] = await Promise.all([
      getAllCategories(),
      getAllTags()
    ])
    
    if (categoriesRes.code === 200 && categoriesRes.data) {
      categories.value = categoriesRes.data
    }
    
    if (tagsRes.code === 200 && tagsRes.data) {
      tags.value = tagsRes.data
    }
  } catch (error) {
    console.error('获取分类和标签数据失败:', error)
    ElMessage.error('获取分类和标签数据失败')
  }
}

// 自定义上传方法
const customUpload = async (options: any) => {
  try {
    const { data } = await uploadArticleCover(options.file)
    handleCoverSuccess({ code: 200, data })
    options.onSuccess()
  } catch (error) {
    options.onError(error)
    ElMessage.error('上传失败')
  }
}

// 封面图片上传成功回调
const handleCoverSuccess = (res: any) => {
  if (res.code === 200) {
    article.value.cover = res.data
  } else {
    ElMessage.error(res.message || '上传失败')
  }
}

const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 保存草稿
const saveDraft = async () => {
  if (!article.value.title || !article.value.content) {
    ElMessage.warning('请填写文章标题和内容')
    return
  }

  try {
    article.value.status = 'draft'
    const data = {
      ...article.value,
      status: 'draft'
    }
    
    if (isEdit.value && articleId.value) {
      await updateArticle(articleId.value, data)
      ElMessage.success('草稿保存成功')
    } else {
      const { data: newArticleId } = await createArticle(data)
      articleId.value = newArticleId
      isEdit.value = true
      ElMessage.success('草稿保存成功')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 发布文章
const publishArticle = async () => {
  if (!article.value.title || !article.value.content) {
    ElMessage.warning('请填写文章标题和内容')
    return
  }

  try {
    const data = {
      ...article.value,
      status: 'published'
    }
    
    if (isEdit.value && articleId.value) {
      await updateArticle(articleId.value, data)
    } else {
      await createArticle(data)
    }
    
    ElMessage.success('文章发布成功')
    router.push('/user/articles')
  } catch (error) {
    console.error('发布失败:', error)
    ElMessage.error('发布失败')
  }
}

const handleGoBack = () => {
  router.push('/user/articles')
}

onMounted(async () => {
  // 先加载分类和标签数据
  await fetchCategoriesAndTags()
  
  // 获取路由参数中的文章ID
  const id = route.params.id
  if (id && !isNaN(Number(id))) {
    isEdit.value = true
    articleId.value = Number(id)
    await loadArticleDetail(Number(id))
  }
})
</script>

<style scoped>
.editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 178px;
  height: 178px;
}

.avatar-uploader:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style> 