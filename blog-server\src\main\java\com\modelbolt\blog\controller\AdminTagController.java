package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.model.dto.tag.TagVO;
import com.modelbolt.blog.model.dto.tag.CreateTagDTO;
import com.modelbolt.blog.service.TagService;
import com.modelbolt.blog.annotation.OperationLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/admin/tag")
@RequiredArgsConstructor
@Tag(name = "管理后台-标签管理", description = "管理后台标签管理相关接口")
@PreAuthorize("hasRole('ADMIN')")
public class AdminTagController {

    private final TagService tagService;

    @Operation(summary = "获取所有标签")
    @GetMapping("/list")
    public ApiResponse<List<TagVO>> getAllTags() {
        log.debug("Getting all tags");
        return ApiResponse.success(tagService.getAllTags());
    }

    @Operation(summary = "创建标签")
    @PostMapping
    @OperationLog("创建标签")
    public ApiResponse<TagVO> createTag(
        @Valid @RequestBody CreateTagDTO dto
    ) {
        log.debug("Creating tag: {}", dto);
        return ApiResponse.success(tagService.createTag(dto));
    }

    @Operation(summary = "更新标签")
    @PutMapping("/{id}")
    @OperationLog("更新标签")
    public ApiResponse<TagVO> updateTag(
        @Parameter(description = "标签ID") @PathVariable Long id,
        @Valid @RequestBody CreateTagDTO dto
    ) {
        log.debug("Updating tag {}: {}", id, dto);
        return ApiResponse.success(tagService.updateTag(id, dto));
    }

    @Operation(summary = "删除标签")
    @DeleteMapping("/{id}")
    @OperationLog("删除标签")
    public ApiResponse<Void> deleteTag(
        @Parameter(description = "标签ID") @PathVariable Long id
    ) {
        log.debug("Deleting tag: {}", id);
        tagService.deleteTag(id);
        return ApiResponse.success();
    }
} 