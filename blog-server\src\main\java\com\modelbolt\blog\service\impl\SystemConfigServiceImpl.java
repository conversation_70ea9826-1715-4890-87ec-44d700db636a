package com.modelbolt.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.modelbolt.blog.model.entity.AiChatHistory;
import com.modelbolt.blog.model.entity.AiUsageStats;
import com.modelbolt.blog.model.entity.Article;
import com.modelbolt.blog.model.entity.OperationLog;
import com.modelbolt.blog.model.entity.SystemConfig;
import com.modelbolt.blog.mapper.AiChatHistoryMapper;
import com.modelbolt.blog.mapper.AiUsageStatsMapper;
import com.modelbolt.blog.mapper.ArticleMapper;
import com.modelbolt.blog.mapper.OperationLogMapper;
import com.modelbolt.blog.mapper.SystemConfigMapper;
import com.modelbolt.blog.mapper.UserMapper;
import com.modelbolt.blog.mapper.VisitHistoryMapper;
import com.modelbolt.blog.model.dto.system.SystemConfigDTO;
import com.modelbolt.blog.model.vo.MonitoringData;
import com.modelbolt.blog.model.vo.OperationLogExportVO;
import com.modelbolt.blog.service.SystemConfigService;
import com.modelbolt.blog.utils.RedisCache;
import com.modelbolt.blog.utils.ExcelUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.servlet.http.HttpServletResponse;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.lang.management.ManagementFactory;
import java.lang.management.OperatingSystemMXBean;

@Slf4j
@Service
@RequiredArgsConstructor
public class SystemConfigServiceImpl extends ServiceImpl<SystemConfigMapper, SystemConfig> implements SystemConfigService {

    private final SystemConfigMapper systemConfigMapper;
    private final OperationLogMapper operationLogMapper;
    private final AiChatHistoryMapper aiChatHistoryMapper;
    private final AiUsageStatsMapper aiUsageStatsMapper;
    private final UserMapper userMapper;
    private final VisitHistoryMapper visitHistoryMapper;
    private final ArticleMapper articleMapper;
    private final RedisCache redisCache;
    private final JavaMailSender mailSender;

    private static final String CONFIG_CACHE_KEY = "system:config";
    private static final String MONITORING_CACHE_KEY = "system:monitoring";

    @Override
    public SystemConfigDTO getConfig() {
        List<SystemConfig> configs = systemConfigMapper.selectAllConfigs();
        Map<String, String> configMap = configs.stream()
                .filter(config -> config.getKey() != null && !config.getKey().isEmpty())
                .collect(Collectors.toMap(
                    SystemConfig::getKey,
                    SystemConfig::getValue,
                    (existing, replacement) -> existing // 如果有重复键，保留第一个值
                ));
        
        SystemConfigDTO dto = new SystemConfigDTO();
        // 设置配置值
        dto.setSiteName(configMap.getOrDefault("siteName", "ModelBolt Blog"));
        dto.setSiteDescription(configMap.getOrDefault("siteDescription", ""));
        dto.setSiteKeywords(configMap.getOrDefault("siteKeywords", ""));
        dto.setSiteUrl(configMap.getOrDefault("siteUrl", ""));
        
        dto.setSmtpHost(configMap.getOrDefault("smtpHost", ""));
        dto.setSmtpPort(Integer.parseInt(configMap.getOrDefault("smtpPort", "587")));
        dto.setSmtpUsername(configMap.getOrDefault("smtpUsername", ""));
        dto.setSmtpPassword(configMap.getOrDefault("smtpPassword", ""));
        dto.setSmtpFrom(configMap.getOrDefault("smtpFrom", ""));
        
        dto.setAiModel(configMap.getOrDefault("aiModel", "DeepSeek"));
        dto.setAiApiKey(configMap.getOrDefault("aiApiKey", ""));
        dto.setAiApiEndpoint(configMap.getOrDefault("aiApiEndpoint", ""));
        dto.setAiMaxTokens(Integer.parseInt(configMap.getOrDefault("aiMaxTokens", "2000")));
        dto.setAiTemperature(Double.parseDouble(configMap.getOrDefault("aiTemperature", "0.7")));
        dto.setAiRequestLimit(Integer.parseInt(configMap.getOrDefault("aiRequestLimit", "1000")));
        dto.setAiTokenLimit(Integer.parseInt(configMap.getOrDefault("aiTokenLimit", "100000")));
        
        dto.setSecurityLoginAttempts(Integer.parseInt(configMap.getOrDefault("securityLoginAttempts", "5")));
        dto.setSecurityLockDuration(Integer.parseInt(configMap.getOrDefault("securityLockDuration", "30")));
        
        // 缓存配置
        redisCache.setCacheObject(CONFIG_CACHE_KEY, dto);
        
        return dto;
    }

    @Override
    @Transactional
    public void saveConfig(SystemConfigDTO config) {
        // 删除旧配置
        remove(null);
        
        // 保存站点信息
        saveConfigItem("siteName", config.getSiteName());
        saveConfigItem("siteDescription", config.getSiteDescription());
        saveConfigItem("siteKeywords", config.getSiteKeywords());
        saveConfigItem("siteUrl", config.getSiteUrl());
        
        // 保存邮件配置
        saveConfigItem("smtpHost", config.getSmtpHost());
        saveConfigItem("smtpPort", String.valueOf(config.getSmtpPort()));
        saveConfigItem("smtpUsername", config.getSmtpUsername());
        saveConfigItem("smtpPassword", config.getSmtpPassword());
        saveConfigItem("smtpFrom", config.getSmtpFrom());
        
        // 保存AI配置
        saveConfigItem("aiModel", config.getAiModel());
        saveConfigItem("aiApiKey", config.getAiApiKey());
        saveConfigItem("aiApiEndpoint", config.getAiApiEndpoint());
        saveConfigItem("aiMaxTokens", String.valueOf(config.getAiMaxTokens()));
        saveConfigItem("aiTemperature", String.valueOf(config.getAiTemperature()));
        saveConfigItem("aiRequestLimit", String.valueOf(config.getAiRequestLimit()));
        saveConfigItem("aiTokenLimit", String.valueOf(config.getAiTokenLimit()));
        
        // 保存安全配置
        saveConfigItem("securityLoginAttempts", String.valueOf(config.getSecurityLoginAttempts()));
        saveConfigItem("securityLockDuration", String.valueOf(config.getSecurityLockDuration()));
        
        // 清除缓存
        redisCache.deleteObject(CONFIG_CACHE_KEY);
    }

    @Override
    public void testEmailConfig() {
        SystemConfigDTO config = getConfig();
        SimpleMailMessage message = new SimpleMailMessage();
        message.setFrom(config.getSmtpFrom());
        message.setTo(config.getSmtpUsername());
        message.setSubject("ModelBolt Blog - 邮件配置测试");
        message.setText("这是一封测试邮件，如果您收到这封邮件，说明邮件配置正确。");
        mailSender.send(message);
    }

    @Override
    public void testAiConfig() {
        // TODO: 实现AI配置测试
        SystemConfigDTO config = getConfig();
        // 这里需要根据实际使用的AI API实现测试逻辑
    }

    @Override
    public MonitoringData getMonitoringData() {
        // 先从缓存获取
        MonitoringData data = redisCache.getCacheObject(MONITORING_CACHE_KEY);
        if (data != null) {
            return data;
        }

        // 创建监控数据
        data = new MonitoringData();
        
        // 设置AI使用统计
        MonitoringData.AiUsage aiUsage = new MonitoringData.AiUsage();
        // 从AI使用统计表获取数据
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(7);
        
        // 获取总调用次数和Token数
        Long totalRequests = aiUsageStatsMapper.sumRequestsByDateRange(null, startDate, endDate);
        Long totalTokens = aiUsageStatsMapper.sumTokensByDateRange(null, startDate, endDate);
        
        // 计算成功率（这里假设没有tokens的记录为失败）
        List<AiChatHistory> aiHistory = aiChatHistoryMapper.findByUserIdAndTimeRange(
            null, LocalDateTime.now().minusDays(7)
        );
        long successCalls = aiHistory.stream().filter(h -> h.getTokens() > 0).count();
        double successRate = totalRequests > 0 ? (double) successCalls / totalRequests * 100 : 0;
        
        // 计算平均延迟
        double averageLatency = aiHistory.stream()
            .mapToLong(h -> Optional.ofNullable(h.getTokens()).orElse(0))
            .average()
            .orElse(0);
            
        // 获取每日统计数据
        List<AiUsageStats> dailyStats = aiUsageStatsMapper.findByDateRange(null, startDate, endDate);
        List<MonitoringData.AiUsage.DailyStat> dailyStatsList = dailyStats.stream()
            .map(stat -> {
                MonitoringData.AiUsage.DailyStat ds = new MonitoringData.AiUsage.DailyStat();
                ds.setDate(stat.getDate().toString());
                ds.setCalls(stat.getTotalRequests());
                ds.setTokens(stat.getTotalTokens());
                return ds;
            })
            .sorted(Comparator.comparing(MonitoringData.AiUsage.DailyStat::getDate))
            .collect(Collectors.toList());
            
        aiUsage.setTotalCalls(totalRequests);
        aiUsage.setTotalTokens(totalTokens);
        aiUsage.setSuccessRate(successRate);
        aiUsage.setAverageLatency(averageLatency);
        aiUsage.setDailyStats(dailyStatsList);
        data.setAiUsage(aiUsage);
        
        // 设置性能监控
        MonitoringData.Performance performance = new MonitoringData.Performance();
        // 获取系统性能数据
        OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
        if (osBean instanceof com.sun.management.OperatingSystemMXBean) {
            com.sun.management.OperatingSystemMXBean sunOsBean = (com.sun.management.OperatingSystemMXBean) osBean;
            performance.setCpuUsage(sunOsBean.getSystemCpuLoad() * 100);
            performance.setMemoryUsage(
                (sunOsBean.getTotalPhysicalMemorySize() - sunOsBean.getFreePhysicalMemorySize()) * 100.0 
                / sunOsBean.getTotalPhysicalMemorySize()
            );
        }
        
        // 从Redis获取性能统计数据
        String responseTimeKey = "monitoring:response_time";
        String concurrentUsersKey = "monitoring:concurrent_users";
        String errorRateKey = "monitoring:error_rate";
        
        // 设置默认值并安全解析Redis数据
        String responseTimeStr = redisCache.getCacheObject(responseTimeKey);
        String concurrentUsersStr = redisCache.getCacheObject(concurrentUsersKey);
        String errorRateStr = redisCache.getCacheObject(errorRateKey);
        
        performance.setResponseTime(responseTimeStr != null ? Double.parseDouble(responseTimeStr) : 0.0);
        performance.setConcurrentUsers(concurrentUsersStr != null ? Integer.parseInt(concurrentUsersStr) : 0);
        performance.setErrorRate(errorRateStr != null ? Double.parseDouble(errorRateStr) : 0.0);
        data.setPerformance(performance);
        
        // 设置用户行为
        MonitoringData.UserBehavior userBehavior = new MonitoringData.UserBehavior();
        // 获取用户统计数据
        int activeUsers = userMapper.count();
        int newUsers = userMapper.getNewUserCountByDate(LocalDate.now());
        
        // 计算留存率（7天）
        int totalNewUsers = userMapper.getNewUserCountByDate(LocalDate.now().minusDays(7));
        int retainedUsers = userMapper.count() - totalNewUsers;
        double retention = totalNewUsers > 0 ? (double) retainedUsers / totalNewUsers * 100 : 0;
        
        // 获取平均会话时长（从访问记录计算）
        Long totalVisits = visitHistoryMapper.sumVisitsByDateRange(startDate, endDate);
        double avgSessionTime = totalVisits > 0 ? 30.0 : 0; // 假设每次访问平均30分钟
        
        userBehavior.setActiveUsers(activeUsers);
        userBehavior.setNewUsers(newUsers);
        userBehavior.setRetention(retention);
        userBehavior.setAverageSessionTime(avgSessionTime);
        
        // 获取热门页面
        List<MonitoringData.UserBehavior.PageView> topPages = visitHistoryMapper.findTopPages(startDate, endDate, 10);
        userBehavior.setTopPages(topPages);
        data.setUserBehavior(userBehavior);
        
        // 设置内容质量
        MonitoringData.ContentQuality contentQuality = new MonitoringData.ContentQuality();
        // 获取文章统计数据
        long totalArticles = articleMapper.count();
        double avgScore = 0;
        List<MonitoringData.ContentQuality.QualityDistribution> qualityDist = new ArrayList<>();
        List<MonitoringData.ContentQuality.AuthorScore> topAuthors = new ArrayList<>();
        
        // 计算平均质量分（基于浏览量、点赞数、评论数等）
        List<Article> articles = articleMapper.selectList(null);
        if (!articles.isEmpty()) {
            avgScore = articles.stream()
                .mapToDouble(article -> 
                    (article.getViewCount() * 0.4 + 
                     article.getLikeCount() * 0.3 + 
                     article.getCommentCount() * 0.3) / 100.0)
                .average()
                .orElse(0);
                
            // 计算质量分布
            Map<String, Long> distribution = articles.stream()
                .collect(Collectors.groupingBy(article -> {
                    double score = (article.getViewCount() * 0.4 + 
                                  article.getLikeCount() * 0.3 + 
                                  article.getCommentCount() * 0.3) / 100.0;
                    if (score >= 8) return "优秀";
                    else if (score >= 6) return "良好";
                    else if (score >= 4) return "一般";
                    else return "待改进";
                }, Collectors.counting()));
                
            distribution.forEach((level, count) -> {
                MonitoringData.ContentQuality.QualityDistribution dist = 
                    new MonitoringData.ContentQuality.QualityDistribution();
                dist.setLevel(level);
                dist.setCount(count.intValue());
                qualityDist.add(dist);
            });
            
            // 获取top作者
            Map<Long, Double> authorScores = articles.stream()
                .collect(Collectors.groupingBy(
                    Article::getAuthorId,
                    Collectors.averagingDouble(article -> 
                        (article.getViewCount() * 0.4 + 
                         article.getLikeCount() * 0.3 + 
                         article.getCommentCount() * 0.3) / 100.0)
                ));
                
            authorScores.entrySet().stream()
                .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
                .limit(10)
                .forEach(entry -> {
                    MonitoringData.ContentQuality.AuthorScore authorScore = 
                        new MonitoringData.ContentQuality.AuthorScore();
                    authorScore.setAuthorId(entry.getKey());
                    authorScore.setAuthorName(userMapper.selectUsernameById(entry.getKey()));
                    authorScore.setScore(entry.getValue());
                    topAuthors.add(authorScore);
                });
        }
        
        contentQuality.setAverageScore(avgScore);
        contentQuality.setTotalArticles((int) totalArticles);
        contentQuality.setQualityDistribution(qualityDist);
        contentQuality.setTopAuthors(topAuthors);
        data.setContentQuality(contentQuality);

        // 缓存监控数据（1分钟）
        redisCache.setCacheObject(MONITORING_CACHE_KEY, data, 60);
        
        return data;
    }

    @Override
    public void exportOperationLogs(HttpServletResponse response) {
        log.debug("Starting operation logs export process");
        
        try {
            // 查询所有操作日志
            List<OperationLog> logs = operationLogMapper.selectList(
                new LambdaQueryWrapper<OperationLog>()
                    .orderByDesc(OperationLog::getCreatedAt)
            );
            log.debug("Found {} operation logs to export", logs.size());

            // 转换为导出VO
            List<OperationLogExportVO> exportList = logs.stream()
                .map(this::convertToExportVO)
                .collect(Collectors.toList());
            log.debug("Converted {} logs to export format", exportList.size());

            // 使用EasyExcel导出
            log.debug("Starting Excel export process...");
            ExcelUtils.export(response, "操作日志", OperationLogExportVO.class, exportList);
            log.debug("Excel export completed successfully");
            
        } catch (Exception e) {
            log.error("Failed to export operation logs. Error: {}", e.getMessage(), e);
            throw new RuntimeException("导出操作日志失败：" + e.getMessage(), e);
        }
    }

    private OperationLogExportVO convertToExportVO(OperationLog log) {
        OperationLogExportVO vo = new OperationLogExportVO();
        BeanUtils.copyProperties(log, vo);
        return vo;
    }

    private void saveConfigItem(String key, String value) {
        SystemConfig config = new SystemConfig();
        config.setKey(key);
        config.setValue(value);
        save(config);
    }
} 