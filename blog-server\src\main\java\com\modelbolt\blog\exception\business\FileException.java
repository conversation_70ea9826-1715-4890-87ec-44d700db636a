package com.modelbolt.blog.exception.business;

public class FileException extends BusinessException {
    
    private FileException(int code, String message) {
        super(code, message);
    }
    
    public static class EmptyFile extends FileException {
        public EmptyFile() {
            super(400, "文件不能为空");
        }
    }
    
    public static class InvalidFileType extends FileException {
        public InvalidFileType() {
            super(400, "不支持的文件类型");
        }
    }
    
    public static class SaveFailed extends FileException {
        public SaveFailed(String reason) {
            super(500, "文件保存失败: " + reason);
        }
    }
} 