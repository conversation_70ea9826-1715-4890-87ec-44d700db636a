package com.modelbolt.blog.service;

import org.springframework.web.multipart.MultipartFile;
import java.util.concurrent.TimeUnit;

/**
 * 文件服务接口
 * 提供文件上传、下载、缓存等相关功能
 * 
 * <AUTHOR> Team
 */
public interface FileService {
    /**
     * 上传图片文件
     * @param file 文件
     * @param subDir 子目录，例如：avatar, cover, article
     * @return 文件访问URL
     */
    String uploadImage(MultipartFile file, String subDir);
    
    /**
     * 从缓存中获取图片
     * 
     * @param fileUrl 图片URL
     * @return 缓存的Base64编码图片数据，如果不存在则返回null
     */
    String getImageFromCache(String fileUrl);
    
    /**
     * 将图片缓存到Redis
     * 
     * @param fileUrl 图片URL
     * @param imageBytes 图片字节数组
     */
    void cacheImage(String fileUrl, byte[] imageBytes);
    
    /**
     * 将图片缓存到Redis，并设置过期时间
     * 
     * @param fileUrl 图片URL
     * @param imageBytes 图片字节数组
     * @param timeout 过期时间
     * @param unit 时间单位
     */
    void cacheImage(String fileUrl, byte[] imageBytes, long timeout, TimeUnit unit);
    
    /**
     * 清除图片缓存
     * 
     * @param fileUrl 图片URL
     */
    void clearImageCache(String fileUrl);
    
    /**
     * 清除所有图片缓存
     */
    void clearAllImageCache();
} 