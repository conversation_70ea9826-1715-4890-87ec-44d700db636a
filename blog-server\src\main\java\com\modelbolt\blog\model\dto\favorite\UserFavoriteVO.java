package com.modelbolt.blog.model.dto.favorite;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "用户收藏视图对象")
public class UserFavoriteVO {
    @Schema(description = "文章ID")
    private Long articleId;

    @Schema(description = "文章标题")
    private String articleTitle;

    @Schema(description = "文章摘要")
    private String articleSummary;

    @Schema(description = "文章封面")
    private String articleCover;

    @Schema(description = "作者名称")
    private String authorName;

    @Schema(description = "浏览量")
    private Integer viewCount;

    @Schema(description = "评论数")
    private Integer commentCount;

    @Schema(description = "收藏时间")
    private LocalDateTime favoriteTime;
} 