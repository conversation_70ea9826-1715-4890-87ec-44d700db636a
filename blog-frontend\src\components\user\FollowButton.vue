<template>
  <el-button
    :type="isFollowing ? 'default' : 'primary'"
    :size="size"
    :loading="loading"
    @click="handleFollow"
    class="follow-button"
    :class="{ 'is-following': isFollowing }"
  >
    <template v-if="!loading">
      {{ isFollowing ? '已关注' : '关注' }}
    </template>
  </el-button>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { followUser, unfollowUser } from '@/api/userCenter'

const props = defineProps<{
  userId: number | null
  initialIsFollowing?: boolean
  size?: 'small' | 'default' | 'large'
}>()

const emit = defineEmits<{
  (e: 'update:isFollowing', value: boolean): void
  (e: 'follow', userId: number): void
  (e: 'unfollow', userId: number): void
}>()

const userStore = useUserStore()
const loading = ref(false)
const isFollowing = ref(props.initialIsFollowing || false)

// 按钮文本
const buttonText = computed(() => {
  if (isFollowing.value) {
    return '已关注'
  }
  return '关注'
})

// 处理关注/取消关注
const handleFollow = async () => {
  if (!userStore.isAuthenticated) {
    ElMessage.warning('请先登录')
    return
  }

  // 不能关注自己
  if (props.userId === userStore.userInfo?.id) {
    ElMessage.warning('不能关注自己')
    return
  }

  // 检查userId是否有效
  if (!props.userId || isNaN(props.userId)) {
    ElMessage.error('用户ID无效')
    return
  }

  loading.value = true
  try {
    if (isFollowing.value) {
      await unfollowUser(props.userId)
      isFollowing.value = false
      emit('update:isFollowing', false)
      emit('unfollow', props.userId)
      ElMessage.success('已取消关注')
    } else {
      await followUser(props.userId)
      isFollowing.value = true
      emit('update:isFollowing', true)
      emit('follow', props.userId)
      ElMessage.success('关注成功')
    }
  } catch (error) {
    console.error('关注操作失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}

// 监听 initialIsFollowing 属性变化
watch(() => props.initialIsFollowing, (newValue) => {
  isFollowing.value = newValue || false
}, { immediate: true })

onMounted(() => {
  // 确保初始状态正确
  isFollowing.value = props.initialIsFollowing || false
})
</script>

<style lang="scss" scoped>
.follow-button {
  min-width: 80px;
  transition: all 0.3s ease;

  &.is-following {
    @apply bg-gray-100 text-gray-600 border-gray-300;
    
    &:hover {
      @apply bg-red-50 text-red-600 border-red-600;
      
      &::before {
        content: '取消关注';
      }
      
      span {
        display: none;
      }
    }
  }
}
</style> 