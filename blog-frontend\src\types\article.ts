/**
 * 文章相关类型定义
 */

import type { ARTICLE_STATUS } from '@/constants/article'

// 文章状态类型
export type ArticleStatus = typeof ARTICLE_STATUS[keyof typeof ARTICLE_STATUS]

/**
 * 文章数据
 */
export interface ArticleVO {
  id: number
  title: string
  summary: string
  content?: string
  cover: string
  authorId?: number
  authorName: string
  authorAvatar?: string
  categoryId?: number
  categoryName?: string
  viewCount: number
  commentCount: number
  likeCount: number
  isTop: boolean
  status?: number
  createdAt: string
  updatedAt?: string
  tags?: TagVO[]
  liked?: boolean
  favorited?: boolean
}

/**
 * 分类数据
 */
export interface CategoryVO {
  id: number
  name: string
  parentId?: number
  children?: CategoryVO[]
  articleCount: number
  orderNum?: number
}

/**
 * 标签数据
 */
export interface TagVO {
  id: number
  name: string
  articleCount?: number
}

/**
 * 评论数据
 */
export interface CommentVO {
  id: number
  content: string
  articleId: number
  userId: number
  userName: string
  userAvatar?: string
  parentId?: number
  rootId?: number
  replyCount?: number
  createdAt: string
  children?: CommentVO[]
}

/**
 * 文章详情
 */
export interface Article {
  id: number
  title: string
  content: string
  summary?: string
  cover?: string
  status: ArticleStatus
  viewCount: number
  likeCount: number
  commentCount: number
  categories?: CategoryVO[]
  tags?: TagVO[]
  createdAt: string
  updatedAt: string
}

/**
 * 创建文章参数
 */
export interface CreateArticleDTO {
  title: string
  content: string
  summary?: string
  cover?: string
  status: ArticleStatus
  categoryIds?: number[]
  tagIds?: number[]
}

/**
 * 更新文章参数
 */
export interface UpdateArticleDTO extends Partial<CreateArticleDTO> {
  id: number
}

/**
 * 文章查询参数
 */
export interface ArticleQuery {
  keyword?: string
  status?: ArticleStatus
  categoryId?: number
  tagId?: number
  page: number
  size: number
} 