<template>
  <div 
    class="toc-container sticky top-24 max-h-[calc(100vh-6rem)] overflow-y-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm"
  >
    <h3 class="text-lg font-bold mb-4 text-gray-900 dark:text-gray-100">目录</h3>
    
    <div class="toc-list space-y-2">
      <template v-if="tocItems.length > 0">
        <div
          v-for="item in tocItems"
          :key="item.id"
          class="toc-item"
          :class="[
            `pl-${(item.level - 1) * 4}`,
            { 'active': activeId === item.id }
          ]"
        >
          <a
            :href="`#${item.id}`"
            class="block py-1 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
            :class="{ 'text-blue-600 dark:text-blue-400': activeId === item.id }"
            @click.prevent="scrollToHeading(item.id)"
          >
            {{ item.text }}
          </a>
        </div>
      </template>
      <div v-else class="text-gray-500 dark:text-gray-400 text-sm">
        暂无目录
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'

interface TocItem {
  id: string
  text: string
  level: number
}

const props = defineProps<{
  content: string
}>()

const tocItems = ref<TocItem[]>([])
const activeId = ref('')

// 解析内容中的标题
const parseTocItems = async () => {
  await nextTick()
  
  // 增加等待时间，确保 markdown 内容完全渲染
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 修改选择器以匹配实际的类名
  const articleContent = document.querySelector('.markdown-content')
  if (!articleContent) {
    console.warn('Article content element not found, retrying...')
    if (retryCount < MAX_RETRIES) {
      retryCount++
      setTimeout(parseTocItems, 1000)
      return
    } else {
      console.warn('Max retries reached, giving up')
      return
    }
  }
  
  retryCount = 0 // 重置重试计数
  const headings = articleContent.querySelectorAll('h1, h2, h3, h4, h5, h6')
  const items: TocItem[] = []
  
  headings.forEach((heading, index) => {
    const level = parseInt(heading.tagName.charAt(1))
    const text = heading.textContent || ''
    const id = `heading-${index}`
    
    // 为标题设置 ID，以便点击跳转
    heading.id = id
    
    items.push({
      id,
      text,
      level
    })
  })
  
  tocItems.value = items
}

// 滚动到指定标题
const scrollToHeading = (id: string) => {
  const element = document.getElementById(id)
  if (element) {
    const offset = 80 // 顶部固定导航栏的高度
    const top = element.offsetTop - offset
    window.scrollTo({
      top,
      behavior: 'smooth'
    })
  }
}

// 监听滚动更新当前位置
const updateActiveHeading = () => {
  const headings = tocItems.value.map(item => document.getElementById(item.id))
  const scrollPosition = window.scrollY + 100 // 添加一些偏移量

  for (let i = headings.length - 1; i >= 0; i--) {
    const heading = headings[i]
    if (heading && heading.offsetTop <= scrollPosition) {
      activeId.value = heading.id
      break
    }
  }
}

// 节流函数
const throttle = (fn: Function, delay: number) => {
  let timer: number | null = null
  return () => {
    if (timer) return
    timer = window.setTimeout(() => {
      fn()
      timer = null
    }, delay)
  }
}

const throttledUpdateActiveHeading = throttle(updateActiveHeading, 100)

// 监听内容变化
watch(() => props.content, async (newContent) => {
  if (!newContent) {
    tocItems.value = []
    return
  }
  
  await nextTick()
  await parseTocItems()
}, { immediate: true })

// 添加重试计数
let retryCount = 0
const MAX_RETRIES = 3

onMounted(() => {
  if (props.content) {
    parseTocItems()
  }
  window.addEventListener('scroll', throttledUpdateActiveHeading)
})

onUnmounted(() => {
  window.removeEventListener('scroll', throttledUpdateActiveHeading)
})
</script>

<style lang="scss" scoped>
.toc-container {
  width: 100%;
  
  .toc-list {
    @apply text-sm;
  }
  
  .toc-item {
    &.active > a {
      @apply font-medium;
    }
  }
}
</style> 