package com.modelbolt.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.modelbolt.blog.model.dto.category.CreateCategoryDTO;
import com.modelbolt.blog.model.dto.article.ArticleVO;
import com.modelbolt.blog.model.dto.category.CategoryVO;
import com.modelbolt.blog.model.entity.Category;
import com.modelbolt.blog.model.entity.Article;
import com.modelbolt.blog.exception.business.CategoryException;
import com.modelbolt.blog.mapper.CategoryMapper;
import com.modelbolt.blog.service.CategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category> implements CategoryService {

    private final CategoryMapper categoryMapper;
    private static final Logger log = LoggerFactory.getLogger(CategoryServiceImpl.class);

    @Override
    public List<CategoryVO> getCategories() {
        // 按排序号升序获取所有分类
        LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<Category>()
            .orderByAsc(Category::getOrderNum);

        return categoryMapper.selectList(wrapper).stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
    }

    @Override
    public CategoryVO getCategoryById(Long id) {
        Category category = categoryMapper.selectById(id);
        if (category == null) {
            throw new CategoryException.CategoryNotFound();
        }
        return convertToVO(category);
    }

    @Override
    public IPage<ArticleVO> getArticlesByCategoryId(Long categoryId, Integer page, Integer size) {
        // 先检查分类是否存在
        Category category = categoryMapper.selectById(categoryId);
        if (category == null) {
            throw new CategoryException.CategoryNotFound();
        }

        // 获取分类下的文章列表
        Page<Article> pageParam = new Page<>(page, size);
        IPage<Article> articlePage = categoryMapper.getArticlesByCategoryId(pageParam, categoryId);

        // 转换为VO
        return articlePage.convert(article -> {
            ArticleVO vo = new ArticleVO();
            BeanUtils.copyProperties(article, vo);
            return vo;
        });
    }

    @Override
    public List<CategoryVO> getHotCategories(Integer limit) {
        // 获取文章数量最多的分类
        List<Category> hotCategories = categoryMapper.getHotCategories(limit);
        return hotCategories.stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
    }

    @Override
    public List<CategoryVO> getCategoriesByArticleId(Long articleId) {
        return categoryMapper.selectCategoriesByArticleId(articleId)
            .stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
    }

    @Override
    public List<CategoryVO> getCategoryTree() {
        // 获取所有分类
        List<Category> allCategories = categoryMapper.selectList(
            new LambdaQueryWrapper<Category>().orderByAsc(Category::getOrderNum)
        );

        // 转换为树形结构
        return buildCategoryTree(allCategories);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CategoryVO createCategory(CreateCategoryDTO dto) {
        // 检查排序号是否大于0
        if (dto.getOrderNum() <= 0) {
            throw new CategoryException.InvalidOrderNum();
        }

        // 检查父分类是否存在
        if (dto.getParentId() != null) {
            Category parent = categoryMapper.selectById(dto.getParentId());
            if (parent == null) {
                throw new CategoryException.CategoryNotFound();
            }
        }

        // 检查分类名称是否重复
        if (isCategoryNameExists(dto.getName(), dto.getParentId())) {
            throw new CategoryException.DuplicateName();
        }

        // 检查同级分类下是否有重复的排序号
        checkDuplicateOrderNum(dto, null);

        // 创建分类
        Category category = new Category();
        category.setName(dto.getName());
        category.setParentId(dto.getParentId());
        category.setOrderNum(dto.getOrderNum());

        categoryMapper.insert(category);
        return convertToVO(category);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CategoryVO updateCategory(Long id, CreateCategoryDTO dto) {
        // 检查分类是否存在
        Category category = categoryMapper.selectById(id);
        if (category == null) {
            throw new CategoryException.CategoryNotFound();
        }

        // 检查排序号是否大于0
        if (dto.getOrderNum() <= 0) {
            throw new CategoryException.InvalidOrderNum();
        }

        // 检查父分类是否存在
        if (dto.getParentId() != null) {
            if (dto.getParentId().equals(id)) {
                throw new CategoryException.CircularReference();
            }
            Category parent = categoryMapper.selectById(dto.getParentId());
            if (parent == null) {
                throw new CategoryException.CategoryNotFound();
            }
            // 检查是否会形成循环引用
            if (isCircularReference(id, dto.getParentId())) {
                throw new CategoryException.CircularReference();
            }
        }

        // 检查同级分类下是否有重复的排序号
        checkDuplicateOrderNum(dto, id);

        // 检查分类名称是否重复（只在同级分类中检查）
        if (!category.getName().equals(dto.getName())) {
            boolean nameExists = isCategoryNameExists(dto.getName(), dto.getParentId());
            if (nameExists) {
                throw new CategoryException.DuplicateName();
            }
        }

        // 更新分类
        boolean needUpdate = false;
        if (!category.getName().equals(dto.getName())) {
            category.setName(dto.getName());
            needUpdate = true;
        }
        
        // 修改 parentId 的比较逻辑
        boolean parentIdChanged = (category.getParentId() == null && dto.getParentId() != null) ||
                                (dto.getParentId() == null && category.getParentId() != null) ||
                                (category.getParentId() != null && dto.getParentId() != null && 
                                 !category.getParentId().equals(dto.getParentId()));
        
        log.debug("Category {} parentId change check: current={}, new={}, changed={}", 
                 id, category.getParentId(), dto.getParentId(), parentIdChanged);
        
        if (parentIdChanged) {
            category.setParentId(dto.getParentId());
            needUpdate = true;
            log.debug("Updating category {} parentId to {}", id, dto.getParentId());
        }
        
        if (!category.getOrderNum().equals(dto.getOrderNum())) {
            category.setOrderNum(dto.getOrderNum());
            needUpdate = true;
        }

        if (needUpdate) {
            // 强制更新所有字段，确保 parentId 被更新
            Category updateCategory = new Category();
            updateCategory.setId(category.getId());
            updateCategory.setName(category.getName());
            updateCategory.setParentId(category.getParentId());
            updateCategory.setOrderNum(category.getOrderNum());
            
            log.debug("Executing update for category {}: {}", id, updateCategory);
            int rows = categoryMapper.updateById(updateCategory);
            log.debug("Updated {} rows for category {}", rows, id);
            
            // 验证更新结果
            Category updatedCategory = categoryMapper.selectById(id);
            log.debug("Category after update: {}", updatedCategory);
        }

        return convertToVO(category);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCategory(Long id) {
        // 检查分类是否存在
        Category category = categoryMapper.selectById(id);
        if (category == null) {
            throw new CategoryException.CategoryNotFound();
        }

        // 检查是否有子分类
        List<Category> children = categoryMapper.selectList(
            new LambdaQueryWrapper<Category>()
                .eq(Category::getParentId, id)
        );
        if (!children.isEmpty()) {
            throw new CategoryException.HasChildren();
        }

        // 检查是否有关联的文章
        Integer articleCount = categoryMapper.countArticles(id);
        if (articleCount > 0) {
            throw new CategoryException.HasArticles();
        }

        // 删除分类
        categoryMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CategoryVO updateCategoryOrder(Long id, Integer orderNum) {
        // 检查分类是否存在
        Category category = categoryMapper.selectById(id);
        if (category == null) {
            throw new CategoryException.CategoryNotFound();
        }

        // 更新排序号
        category.setOrderNum(orderNum);
        categoryMapper.updateById(category);

        return convertToVO(category);
    }

    private CategoryVO convertToVO(Category category) {
        CategoryVO vo = new CategoryVO();
        BeanUtils.copyProperties(category, vo);
        
        // 获取分类下的文章数量
        Integer articleCount = categoryMapper.countArticles(category.getId());
        vo.setArticleCount(articleCount);
        
        return vo;
    }

    private List<CategoryVO> buildCategoryTree(List<Category> categories) {
        // 转换所有分类为VO
        Map<Long, CategoryVO> categoryMap = categories.stream()
            .map(this::convertToVO)
            .collect(Collectors.toMap(CategoryVO::getId, vo -> vo));

        List<CategoryVO> tree = new ArrayList<>();

        // 构建树形结构
        for (Category category : categories) {
            CategoryVO vo = categoryMap.get(category.getId());
            if (category.getParentId() == null) {
                tree.add(vo);
            } else {
                CategoryVO parent = categoryMap.get(category.getParentId());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(vo);
                }
            }
        }

        return tree;
    }

    private boolean isCategoryNameExists(String name, Long parentId) {
        // 检查所有分类中是否有重复的名称，不再考虑父分类
        LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<Category>()
            .eq(Category::getName, name);

        return categoryMapper.selectCount(wrapper) > 0;
    }

    private boolean isCircularReference(Long categoryId, Long parentId) {
        if (categoryId == null || parentId == null) {
            return false;
        }

        // 获取所有分类
        List<Category> allCategories = categoryMapper.selectList(null);
        if (allCategories == null || allCategories.isEmpty()) {
            return false;
        }
        
        // 从父分类开始向上查找，如果找到当前分类，说明会形成循环引用
        Long currentParentId = parentId;
        
        while (currentParentId != null) {
            if (currentParentId.equals(categoryId)) {
                return true;
            }
            
            // 使用一个 final 变量来在 lambda 中使用
            final Long searchParentId = currentParentId;
            Category parentCategory = allCategories.stream()
                .filter(c -> c.getId().equals(searchParentId))
                .findFirst()
                .orElse(null);
                
            // 如果找不到父分类，说明数据有问题，终止检查
            if (parentCategory == null) {
                return false;
            }
            
            // 继续向上查找
            currentParentId = parentCategory.getParentId();
        }
        
        return false;
    }

    private void checkDuplicateOrderNum(CreateCategoryDTO dto, Long excludeId) {
        // 检查同级分类下是否有重复的排序号
        LambdaQueryWrapper<Category> orderWrapper = new LambdaQueryWrapper<>();
        
        if (dto.getParentId() == null) {
            // 如果是根分类（parentId 为 null），检查所有根分类
            orderWrapper.isNull(Category::getParentId);
        } else {
            // 如果是子分类，检查同一父分类下的分类
            orderWrapper.eq(Category::getParentId, dto.getParentId());
        }
        
        // 添加排序号条件
        orderWrapper.eq(Category::getOrderNum, dto.getOrderNum());
        
        // 如果是更新操作，排除自己
        if (excludeId != null) {
            orderWrapper.ne(Category::getId, excludeId);
        }
        
        if (categoryMapper.selectCount(orderWrapper) > 0) {
            throw new CategoryException.DuplicateOrderNum();
        }
    }
} 