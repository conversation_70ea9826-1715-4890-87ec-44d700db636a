package com.modelbolt.blog.service;

import java.util.Map;

public interface DashboardService {
    /**
     * 获取仪表盘统计数据
     * @return 统计数据
     */
    Map<String, Object> getDashboardStats();

    /**
     * 获取今日访问量
     * @return 今日访问量
     */
    int getTodayVisits();

    /**
     * 获取今日评论数
     * @return 今日评论数
     */
    int getTodayComments();

    /**
     * 获取系统负载
     * @return 系统负载百分比
     */
    String getSystemLoad();
} 