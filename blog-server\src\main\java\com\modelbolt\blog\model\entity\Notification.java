package com.modelbolt.blog.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("notification")
public class Notification {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long userId;
    
    private Long senderId;
    
    private String type;
    
    private Long targetId;
    
    private String targetType;
    
    private String content;
    
    private Boolean isRead;
    
    private LocalDateTime createdAt;
} 