package com.modelbolt.blog.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("login_log")
@Schema(description = "登录日志实体")
public class LoginLog {
    
    @TableId(type = IdType.AUTO)
    @Schema(description = "日志ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "IP地址")
    private String ipAddress;

    @Schema(description = "浏览器类型")
    private String browser;

    @Schema(description = "操作系统")
    private String os;

    @Schema(description = "状态：0失败 1成功")
    private Integer status;

    @Schema(description = "提示信息")
    private String msg;

    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "登录时间")
    private LocalDateTime loginTime;
} 