import {http} from '@/utils/request'
import type { R, PageResult } from '@/types/common'
import type { HotSearch, SearchSuggestion, ArticleVO } from '@/types/search'

export interface SearchParams {
  keyword: string
  type?: 'all' | 'article' | 'tag' | 'category'
  page?: number
  size?: number
}

export const search = (params: SearchParams) => {
  return http<R<PageResult<ArticleVO>>>({
    url: '/search',
    method: 'GET',
    params
  })
}

export const getSuggestions = (keyword: string) => {
  return http<R<SearchSuggestion[]>>({
    url: '/search/suggestions',
    method: 'GET',
    params: { keyword }
  })
}

export const getHotSearches = () => {
  return http<R<HotSearch[]>>({
    url: '/search/hot',
    method: 'GET'
  })
} 