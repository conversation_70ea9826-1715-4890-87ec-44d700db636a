package com.modelbolt.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.user.*;
import com.modelbolt.blog.model.entity.User;
import com.modelbolt.blog.model.dto.user.UserInfoDTO;
import com.modelbolt.blog.model.dto.user.AuthorInfoDTO;
import com.modelbolt.blog.model.dto.user.UserSettingsDTO;
import com.modelbolt.blog.model.dto.user.UserProfileDTO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

public interface UserService extends IService<User> {
    /**
     * 获取用户信息
     *
     * @param username 用户名
     * @return 用户信息DTO
     */
    UserInfoDTO getUserInfo(String username);
    
    /**
     * 获取作者详细信息
     *
     * @param id 作者ID
     * @return 作者详细信息DTO
     */
    AuthorInfoDTO getAuthorInfo(Long id);
    
    /**
     * 获取用户设置
     */
    UserSettingsDTO getUserSettings(Long userId);
    
    /**
     * 更新用户设置
     */
    void updateUserSettings(Long userId, UserSettingsDTO settings);
    
    /**
     * 更新用户资料
     */
    void updateUserProfile(Long userId, UserProfileDTO profile);
    
    /**
     * 更新用户头像
     */
    String updateUserAvatar(Long userId, MultipartFile avatar);
    
    /**
     * 修改密码
     */
    void updatePassword(Long userId, String oldPassword, String newPassword);
    
    /**
     * 关注用户
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否成功
     */
    boolean followUser(Long followerId, Long followingId);
    
    /**
     * 取消关注用户
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否成功
     */
    boolean unfollowUser(Long followerId, Long followingId);
    
    /**
     * 检查是否已关注
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否已关注
     */
    boolean isFollowing(Long followerId, Long followingId);

    /**
     * 获取用户文章数量
     */
    int getArticleCount(Long userId);

    /**
     * 获取用户评论数量
     */
    int getCommentCount(Long userId);

    /**
     * 获取用户收藏数量
     */
    int getFavoriteCount(Long userId);

    /**
     * 获取管理后台用户列表
     *
     * @param query 查询参数
     * @return 分页结果
     */
    PageResult<AdminUserVO> getAdminUserList(AdminUserQuery query);

    /**
     * 更新用户状态
     *
     * @param id 用户ID
     * @param status 状态
     */
    void updateUserStatus(Long id, Integer status);

    /**
     * 导出用户数据
     *
     * @param query 查询参数
     * @param response HTTP响应
     */
    void exportUsers(AdminUserQuery query, HttpServletResponse response);

    /**
     * 更新用户信息
     *
     * @param id 用户ID
     * @param dto 更新信息
     */
    void updateUserInfo(Long id, UpdateUserDTO dto);

    /**
     * 获取用户主页信息
     *
     * @param userId 用户ID
     * @return 用户主页信息
     */
    UserHomeDTO getUserHomeInfo(Long userId);
}