package com.modelbolt.blog.exception.business;

public class ArticleException extends BusinessException {
    
    private ArticleException(int code, String message) {
        super(code, message);
    }
    
    public static class ArticleNotFound extends ArticleException {
        public ArticleNotFound() {
            super(404, "文章不存在");
        }
    }
    
    public static class AlreadyLiked extends ArticleException {
        public AlreadyLiked() {
            super(400, "已经点赞过了");
        }
    }
    
    public static class NotLikedYet extends ArticleException {
        public NotLikedYet() {
            super(400, "还没有点赞");
        }
    }
    
    public static class AlreadyFavorited extends ArticleException {
        public AlreadyFavorited() {
            super(400, "已经收藏过了");
        }
    }
    
    public static class NotFavoritedYet extends ArticleException {
        public NotFavoritedYet() {
            super(400, "还没有收藏");
        }
    }
    
    public static class OperationFailed extends ArticleException {
        public OperationFailed(String message) {
            super(500, message);
        }
    }

    public static class UpdateFailed extends ArticleException {
        public UpdateFailed() {
            super(500, "更新文章数据失败");
        }
    }
} 