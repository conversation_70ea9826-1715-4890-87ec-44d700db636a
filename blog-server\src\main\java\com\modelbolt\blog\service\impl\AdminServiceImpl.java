package com.modelbolt.blog.service.impl;

import com.modelbolt.blog.model.entity.User;
import com.modelbolt.blog.exception.BusinessException;
import com.modelbolt.blog.mapper.UserMapper;
import com.modelbolt.blog.service.AdminService;
import com.modelbolt.blog.utils.FileUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdminServiceImpl implements AdminService {

    private final UserMapper userMapper;
    private final FileUtils fileUtils;
    private final PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public String updateAdminAvatar(Long adminId, MultipartFile avatar) {
        log.debug("Updating avatar for admin: {}", adminId);
        
        // 验证文件
        fileUtils.validateFile(avatar);

        try {
            // 保存文件
            String avatarUrl = fileUtils.saveFile(avatar, "avatar");

            // 更新用户头像
            User user = userMapper.selectById(adminId);
            if (user == null) {
                throw new BusinessException("管理员不存在");
            }
            
            // 验证是否为管理员
            if (!"ADMIN".equals(user.getRole())) {
                throw new BusinessException("非管理员账号");
            }
            
            user.setAvatar(avatarUrl);
            userMapper.updateById(user);

            return avatarUrl;
        } catch (IOException e) {
            log.error("Failed to save avatar", e);
            throw new BusinessException("头像上传失败");
        }
    }

    @Override
    @Transactional
    public void updateAdminPassword(Long adminId, String oldPassword, String newPassword) {
        log.debug("Updating password for admin: {}", adminId);
        
        User user = userMapper.selectById(adminId);
        if (user == null) {
            throw new BusinessException("管理员不存在");
        }
        
        // 验证是否为管理员
        if (!"ADMIN".equals(user.getRole())) {
            throw new BusinessException("非管理员账号");
        }

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new BusinessException("原密码错误");
        }

        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        userMapper.updateById(user);
    }
} 