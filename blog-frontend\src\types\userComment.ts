// 用户评论管理专用类型

// 评论状态常量
export const USER_COMMENT_STATUS = {
  ALL: '',           // 全部
  PENDING: '0',      // 待审核
  APPROVED: '1',     // 已通过
  REJECTED: '2'      // 已拒绝
} as const;

// 评论状态文本
export const USER_COMMENT_STATUS_TEXT = {
  [USER_COMMENT_STATUS.PENDING]: '待审核',
  [USER_COMMENT_STATUS.APPROVED]: '已通过',
  [USER_COMMENT_STATUS.REJECTED]: '已拒绝'
} as const;

// 评论状态标签类型
export const USER_COMMENT_STATUS_TAG_TYPE = {
  [USER_COMMENT_STATUS.PENDING]: 'warning',
  [USER_COMMENT_STATUS.APPROVED]: 'success',
  [USER_COMMENT_STATUS.REJECTED]: 'danger'
} as const;

// 用户评论列表项
export interface UserCommentVO {
  id: number;
  content: string;
  articleId: number;
  articleTitle: string;
  status: string;
  createdAt: string;
}

// 用户评论查询参数
export interface UserCommentQuery {
  keyword?: string;
  status?: string;
  page: number;
  size: number;
} 