// 基础查询参数接口
export interface BaseQuery {
  page: number
  pageSize: number
}

export interface User {
  id: number
  username: string
  email: string
  avatar?: string
  role: string
  status: number
  createdAt: string
  updatedAt: string
}

export interface UserQuery extends BaseQuery {
  username?: string
  email?: string
  role?: string
  status?: number
}

export interface UserForm {
  id?: number
  username: string
  email: string
  role: string
  status: number
}

export interface StatusUpdateDTO {
  status: number
}

export const UserRole = {
  ADMIN: 'ADMIN',
  USER: 'USER'
} as const

export const UserStatus = {
  DISABLED: 0,
  ENABLED: 1
} as const 