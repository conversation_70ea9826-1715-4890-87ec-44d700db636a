package com.modelbolt.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.comment.CommentAuditDTO;
import com.modelbolt.blog.model.dto.comment.CommentQuery;
import com.modelbolt.blog.model.dto.comment.CommentVO;
import com.modelbolt.blog.model.dto.comment.CommentForm;
import com.modelbolt.blog.model.dto.comment.UserCommentVO;
import com.modelbolt.blog.model.entity.*;
import com.modelbolt.blog.exception.business.BusinessException;
import com.modelbolt.blog.exception.business.CommentException;
import com.modelbolt.blog.mapper.CommentLikeMapper;
import com.modelbolt.blog.mapper.CommentMapper;
import com.modelbolt.blog.mapper.UserMapper;
import com.modelbolt.blog.service.ArticleService;
import com.modelbolt.blog.service.CommentService;
import com.modelbolt.blog.service.NotificationService;
import com.modelbolt.blog.service.UserService;
import com.modelbolt.blog.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CommentServiceImpl extends ServiceImpl<CommentMapper, Comment> implements CommentService {

    private final CommentMapper commentMapper;
    private final CommentLikeMapper commentLikeMapper;
    private final UserMapper userMapper;
    private final ArticleService articleService;
    private final UserService userService;
    private final NotificationService notificationService;

    @Override
    @Transactional
    public CommentVO submitComment(CommentForm form) {
        log.debug("Submitting comment: {}", form);
        
        // 获取当前用户ID
        Long userId = SecurityUtils.getCurrentUserId();
        
        // 创建评论实体
        Comment comment = new Comment();
        comment.setContent(form.getContent());
        comment.setArticleId(form.getArticleId());
        comment.setUserId(userId);
        comment.setStatus(0); // 待审核状态
        
        // 保存评论
        save(comment);
        log.debug("Comment saved with ID: {}", comment.getId());
        
        // 更新文章评论数
        articleService.updateCommentCount(comment.getArticleId());
        
        // 获取文章作者ID
        Article article = articleService.getById(comment.getArticleId());
        
        // 只有当评论状态为已审核(1)时才发送通知
        if (comment.getStatus() == 1) {
            // 如果是回复评论
            if (comment.getParentId() != null) {
                // 获取父评论
                Comment parentComment = getById(comment.getParentId());
                if (parentComment != null && !parentComment.getUserId().equals(comment.getUserId())) {
                    // 创建回复通知
                    notificationService.createNotification(
                        parentComment.getUserId(),  // 接收者是父评论的作者
                        comment.getUserId(),        // 发送者是当前评论的作者
                        NotificationType.REPLY,     // 通知类型是回复
                        comment.getId(),            // 目标是当前评论
                        NotificationTargetType.COMMENT,
                        comment.getContent()
                    );
                }
            }
            // 如果不是回复，且评论者不是文章作者
            else if (!comment.getUserId().equals(article.getAuthorId())) {
                // 创建评论通知
                notificationService.createNotification(
                    article.getAuthorId(),        // 接收者是文章作者
                    comment.getUserId(),        // 发送者是评论者
                    NotificationType.COMMENT,   // 通知类型是评论
                    article.getId(),            // 目标是文章
                    NotificationTargetType.ARTICLE,
                    comment.getContent()
                );
            }
        }
        
        // 转换为VO并返回
        return convertToVO(comment);
    }

    @Override
    @Transactional
    public CommentVO replyComment(CommentForm form) {
        log.debug("Replying to comment: {}", form);
        
        // 获取当前用户ID
        Long userId = SecurityUtils.getCurrentUserId();
        
        // 验证父评论是否存在
        Comment parentComment = getById(form.getParentId());
        if (parentComment == null) {
            throw new BusinessException("父评论不存在");
        }
        
        // 创建回复评论
        Comment reply = new Comment();
        reply.setContent(form.getContent());
        reply.setArticleId(form.getArticleId());
        reply.setUserId(userId);
        reply.setParentId(form.getParentId());
        reply.setRootId(form.getRootId() != null ? form.getRootId() : form.getParentId());
        reply.setStatus(0); // 待审核状态
        
        // 保存回复
        save(reply);
        log.debug("Reply saved with ID: {}", reply.getId());
        
        // 更新父评论的回复数
        commentMapper.incrementReplyCount(form.getParentId());
        
        // 更新文章评论数
        articleService.updateCommentCount(form.getArticleId());
        
        // 获取文章作者ID
        Article article = articleService.getById(form.getArticleId());
        
        // 如果是回复评论
        if (form.getParentId() != null) {
            // 创建回复通知
            notificationService.createNotification(
                parentComment.getUserId(),  // 接收者是父评论的作者
                reply.getUserId(),         // 发送者是当前回复的作者
                NotificationType.REPLY,     // 通知类型是回复
                reply.getId(),             // 目标是当前回复
                NotificationTargetType.COMMENT,
                reply.getContent()
            );
        }
        // 如果不是回复，且回复者不是文章作者
        else if (!reply.getUserId().equals(article.getAuthorId())) {
            // 创建回复通知
            notificationService.createNotification(
                article.getAuthorId(),        // 接收者是文章作者
                reply.getUserId(),          // 发送者是回复者
                NotificationType.REPLY,       // 通知类型是回复
                article.getId(),            // 目标是文章
                NotificationTargetType.ARTICLE,
                reply.getContent()
            );
        }
        
        // 转换为VO并返回
        return convertToVO(reply);
    }

    @Override
    @Transactional
    public void deleteComment(Long id) {
        log.debug("Deleting comment: {}", id);
        
        // 获取当前用户ID
        Long userId = SecurityUtils.getCurrentUserId();
        
        // 验证评论是否存在且属于当前用户
        Comment comment = getById(id);
        if (comment == null) {
            throw new BusinessException("评论不存在");
        }
        if (!comment.getUserId().equals(userId)) {
            throw new BusinessException("无权删除该评论");
        }
        
        // 删除评论
        removeById(id);
        
        // 如果是回复，更新父评论的回复数
        if (comment.getParentId() != null) {
            commentMapper.decrementReplyCount(comment.getParentId());
        }
        
        // 更新文章评论数
        articleService.updateCommentCount(comment.getArticleId());
        
        log.debug("Comment deleted: {}", id);
    }

    @Override
    @Transactional
    public void likeComment(Long id) {
        log.debug("Liking comment: {}", id);
        
        // 获取当前用户ID
        Long userId = SecurityUtils.getCurrentUserId();
        
        // 验证评论是否存在
        Comment comment = getById(id);
        if (comment == null) {
            throw new BusinessException("评论不存在");
        }
        
        // 检查是否已点赞
        boolean hasLiked = hasLiked(id, userId);
        
        if (hasLiked) {
            // 如果已点赞，则取消点赞
            commentLikeMapper.delete(
                new LambdaQueryWrapper<CommentLike>()
                    .eq(CommentLike::getCommentId, id)
                    .eq(CommentLike::getUserId, userId)
            );
            
            // 更新评论点赞数
            commentMapper.decrementLikeCount(id);
            log.debug("Comment unliked: {}", id);
        } else {
            // 如果未点赞，则添加点赞
            CommentLike like = new CommentLike();
            like.setCommentId(id);
            like.setUserId(userId);
            commentLikeMapper.insert(like);
            
            // 更新评论点赞数
            commentMapper.incrementLikeCount(id);
            
            // 发送通知给评论作者（仅在点赞时发送，取消点赞不发送通知）
            if (!userId.equals(comment.getUserId())) {
                User currentUser = userService.getById(userId);
                String content = currentUser.getUsername() + " 点赞了你的评论";
                notificationService.createNotification(
                    comment.getUserId(),  // 接收者ID
                    userId,              // 发送者ID
                    NotificationType.LIKE,
                    id,
                    NotificationTargetType.COMMENT,
                    content
                );
            }
            
            log.debug("Comment liked: {}", id);
        }
    }

    @Override
    @Transactional
    public void unlikeComment(Long id) {
        log.debug("Unliking comment: {}", id);
        
        // 获取当前用户ID
        Long userId = SecurityUtils.getCurrentUserId();
        
        // 验证评论是否存在
        Comment comment = getById(id);
        if (comment == null) {
            throw new BusinessException("评论不存在");
        }
        
        // 检查是否已点赞
        if (!hasLiked(id, userId)) {
            throw new BusinessException("尚未点赞该评论");
        }
        
        // 删除点赞记录
        commentLikeMapper.delete(
            new LambdaQueryWrapper<CommentLike>()
                .eq(CommentLike::getCommentId, id)
                .eq(CommentLike::getUserId, userId)
        );
        
        // 更新评论点赞数
        commentMapper.decrementLikeCount(id);
        
        log.debug("Comment unliked: {}", id);
    }

    @Override
    public PageResult<CommentVO> getReplies(Long id, Integer page, Integer size) {
        log.debug("Getting replies for comment {}, page: {}, size: {}", id, page, size);
        
        // 构建分页查询条件
        Page<Comment> commentPage = new Page<>(page, size);
        LambdaQueryWrapper<Comment> wrapper = new LambdaQueryWrapper<Comment>()
            .eq(Comment::getRootId, id)
            .eq(Comment::getStatus, 1)  // 已审核的评论
            .orderByDesc(Comment::getCreatedAt);
        
        // 执行分页查询
        Page<Comment> pageResult = page(commentPage, wrapper);
        
        // 转换为VO列表
        List<CommentVO> records = pageResult.getRecords().stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
        
        return new PageResult<>(records, pageResult.getTotal(), page, size);
    }

    /**
     * 检查用户是否已点赞评论
     */
    private boolean hasLiked(Long commentId, Long userId) {
        return commentLikeMapper.selectCount(
            new LambdaQueryWrapper<CommentLike>()
                .eq(CommentLike::getCommentId, commentId)
                .eq(CommentLike::getUserId, userId)
        ) > 0;
    }

    /**
     * 将评论实体转换为VO对象
     */
    private CommentVO convertToVO(Comment comment) {
        CommentVO vo = new CommentVO();
        BeanUtils.copyProperties(comment, vo);
        
        // 设置用户信息
        String username = userMapper.selectUsernameById(comment.getUserId());
        String userAvatar = userMapper.selectUserAvatarById(comment.getUserId());
        vo.setUserName(username);
        vo.setUserAvatar(userAvatar);
        
        // 设置是否点赞
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId != null) {
            vo.setLiked(hasLiked(comment.getId(), currentUserId));
        }
        
        return vo;
    }

    @Override
    public PageResult<UserCommentVO> getUserComments(Long userId, String keyword, String status, Integer page, Integer size) {
        // 构建查询条件
        LambdaQueryWrapper<Comment> wrapper = new LambdaQueryWrapper<Comment>()
            .eq(Comment::getUserId, userId)
            .eq(StringUtils.isNotBlank(status), Comment::getStatus, status)
            .and(StringUtils.isNotBlank(keyword), w -> w
                .like(Comment::getContent, keyword)
            )
            .orderByDesc(Comment::getCreatedAt);

        // 执行分页查询
        Page<Comment> commentPage = page(new Page<>(page, size), wrapper);
        
        // 转换为VO
        List<UserCommentVO> records = commentPage.getRecords().stream()
            .map(comment -> {
                UserCommentVO vo = new UserCommentVO();
                BeanUtils.copyProperties(comment, vo);
                
                // 获取文章标题
                Article article = articleService.getById(comment.getArticleId());
                if (article != null) {
                    vo.setArticleTitle(article.getTitle());
                }
                
                return vo;
            })
            .collect(Collectors.toList());

        return new PageResult<>(records, commentPage.getTotal(), page, size);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUserComment(Long userId, Long commentId) {
        // 检查评论是否存在且属于当前用户
        Comment comment = getById(commentId);
        if (comment == null) {
            throw new CommentException.CommentNotFound();
        }
        
        if (!comment.getUserId().equals(userId)) {
            throw new CommentException.NotAllowedOperation();
        }

        // 删除评论
        if (!removeById(commentId)) {
            throw new CommentException.DeleteFailed();
        }

        // 更新文章评论数
        articleService.updateCommentCount(comment.getArticleId());
    }

    @Override
    public PageResult<CommentVO> getCommentList(CommentQuery query) {
        // 计算偏移量
        long offset = (query.getPage() - 1L) * query.getSize();
        
        // 查询数据
        List<CommentVO> records = commentMapper.selectCommentPage(query, offset, query.getSize());
        
        // 查询总数
        Long total = commentMapper.selectCommentCount(query);
        
        // 返回分页结果
        return new PageResult<>(records, total, query.getPage(), query.getSize());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditComment(Long id, CommentAuditDTO dto) {
        // 检查评论是否存在
        Comment comment = getById(id);
        if (comment == null) {
            throw new CommentException.CommentNotFound();
        }

        // 检查评论状态
        if (comment.getStatus() != 0) {
            throw new CommentException.AlreadyAudited();
        }

        // 更新评论状态
        try {
            commentMapper.updateStatus(id, dto.getStatus());
        } catch (Exception e) {
            log.error("Failed to audit comment: {}", e.getMessage(), e);
            throw new CommentException.AuditFailed();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteComments(List<Long> ids) {
        try {
            removeBatchByIds(ids);
        } catch (Exception e) {
            log.error("Failed to batch delete comments: {}", e.getMessage(), e);
            throw new CommentException.DeleteFailed();
        }
    }
} 