package com.modelbolt.blog.service;

import java.util.Set;

/**
 * 用户在线状态管理服务
 */
public interface UserOnlineService {
    /**
     * 更新用户在线状态
     * @param userId 用户ID
     */
    void updateOnlineStatus(Long userId);
    
    /**
     * 标记用户为在线状态
     * @param userId 用户ID
     */
    void markUserOnline(Long userId);
    
    /**
     * 标记用户为离线状态
     * @param userId 用户ID
     */
    void markUserOffline(Long userId);
    
    /**
     * 获取在线用户数量
     * @return 在线用户数量
     */
    int getOnlineUsersCount();
    
    /**
     * 检查用户是否在线
     * @param userId 用户ID
     * @return 是否在线
     */
    boolean isUserOnline(Long userId);
    
    /**
     * 获取所有在线用户ID
     * @return 在线用户ID集合
     */
    Set<Long> getOnlineUserIds();
} 