package com.modelbolt.blog.service.impl;

import com.modelbolt.blog.service.EmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class EmailServiceImpl implements EmailService {

    private final JavaMailSender mailSender;
    private final TemplateEngine templateEngine;

    @Value("${spring.mail.username}")
    private String from;

    public EmailServiceImpl(JavaMailSender mailSender, TemplateEngine templateEngine) {
        this.mailSender = mailSender;
        this.templateEngine = templateEngine;
    }

    @Async
    @Override
    public void sendVerificationCode(String to, String code) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("code", code);
        String content = processTemplate("mail/verification-code", variables);
        sendHtmlMail(to, "验证码", content);
    }

    @Async
    @Override
    public void sendResetPasswordEmail(String to, String resetLink) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("resetLink", resetLink);
        String content = processTemplate("mail/reset-password", variables);
        sendHtmlMail(to, "重置密码", content);
    }

    @Async
    @Override
    public void sendNotification(String to, String subject, String content) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("content", content);
        String htmlContent = processTemplate("mail/notification", variables);
        sendHtmlMail(to, subject, htmlContent);
    }

    private String processTemplate(String templateName, Map<String, Object> variables) {
        Context context = new Context();
        variables.forEach(context::setVariable);
        return templateEngine.process(templateName, context);
    }

    private void sendHtmlMail(String to, String subject, String content) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true);
            mailSender.send(message);
            log.info("邮件发送成功：{} -> {}", from, to);
        } catch (MessagingException e) {
            log.error("邮件发送失败：{}", e.getMessage());
            throw new RuntimeException("邮件发送失败", e);
        }
    }
} 