package com.modelbolt.blog.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class JwtUtils {

    @Value("${jwt.expiration}")
    private Long expiration;
    
    @Value("${jwt.remember-me-expiration}")
    private Long rememberMeExpiration;

    @Value("${jwt.secret}")
    private String secret;

    private Key signingKey;

    @PostConstruct
    public void init() {
        byte[] keyBytes = secret.getBytes(StandardCharsets.UTF_8);
        this.signingKey = Keys.hmacShaKeyFor(keyBytes);
        log.debug("JWT signing key initialized");
    }

    public String generateToken(String username, Long userId, String role) {
        return generateToken(username, userId, role, expiration);
    }

    public String generateToken(String username, Long userId, String role, long tokenExpiration) {
        // 打印日志，记录正在为哪个用户生成 Token
        log.debug("Generating new token for user: {}", username);

        // 生成一个唯一的 Token ID，使用 UUID 随机生成字符串，确保每个 Token 的唯一性
        // 获取当前时间，用于设置 Token 的签发时间和过期时间
        // 计算 Token 的过期时间，tokenExpiration 是以秒为单位的有效期，乘以 1000 转换为毫秒
        // 创建一个 Map 来存储自定义的 Claims（声明），Claims 是 JWT 中携带的附加信息
        // 使用 Jwts.builder() 构建 JWT Token
        // 打印日志，记录生成的唯一 Token ID
        // 返回生成的 JWT Token
        String tokenId = UUID.randomUUID().toString();
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + tokenExpiration * 1000);
        Map<String, Object> claims = new HashMap<>();
        claims.put("username", username);
        claims.put("userId", userId);
        claims.put("role", role);
        claims.put("tokenId", tokenId);
        claims.put("createdAt", now.getTime());
        String token = Jwts.builder()
                .setClaims(claims)
                .setSubject(username)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(signingKey, SignatureAlgorithm.HS512)
                .compact();
        log.debug("Generated unique token with ID: {}", tokenId);
        return token;
    }
    
    /**
     * 生成刷新令牌
     * 
     * @param username 用户名
     * @param userId 用户ID
     * @param role 用户角色
     * @return 刷新令牌
     */
    public String generateRefreshToken(String username, Long userId, String role) {
        log.debug("Generating refresh token for user: {}", username);
        
        String tokenId = UUID.randomUUID().toString();
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration * 1000);
        
        Map<String, Object> claims = new HashMap<>();
        claims.put("username", username);
        claims.put("userId", userId);
        claims.put("role", role);
        claims.put("tokenId", tokenId);
        claims.put("createdAt", now.getTime());
        claims.put("type", "refresh");
        
        String token = Jwts.builder()
                .setClaims(claims)
                .setSubject(username)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(signingKey, SignatureAlgorithm.HS512)
                .compact();
        
        log.debug("Generated refresh token with ID: {}", tokenId);
        return token;
    }
    
    /**
     * 从刷新令牌生成新的访问令牌
     * 
     * @param refreshToken 刷新令牌
     * @return 新的访问令牌，如果刷新令牌无效则返回null
     */
    public String generateAccessTokenFromRefreshToken(String refreshToken) {
        log.debug("Generating new access token from refresh token");
        
        try {
            Claims claims = getClaimsFromToken(refreshToken);
            
            // 验证是否是刷新令牌
            String tokenType = claims.get("type", String.class);
            if (!"refresh".equals(tokenType)) {
                log.error("Invalid token type: {}", tokenType);
                return null;
            }
            
            // 验证令牌是否过期
            if (claims.getExpiration().before(new Date())) {
                log.error("Refresh token has expired");
                return null;
            }
            
            String username = claims.getSubject();
            Long userId = claims.get("userId", Long.class);
            String role = claims.get("role", String.class);
            
            // 生成新的访问令牌
            return generateToken(username, userId, role);
            
        } catch (Exception e) {
            log.error("Failed to generate access token from refresh token: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 验证刷新令牌
     * 
     * @param refreshToken 刷新令牌
     * @return 是否有效
     */
    public boolean validateRefreshToken(String refreshToken) {
        log.debug("Validating refresh token");
        
        try {
            Claims claims = getClaimsFromToken(refreshToken);
            
            // 验证是否是刷新令牌
            String tokenType = claims.get("type", String.class);
            if (!"refresh".equals(tokenType)) {
                log.error("Invalid token type: {}", tokenType);
                return false;
            }
            
            // 验证令牌是否过期
            boolean isExpired = claims.getExpiration().before(new Date());
            if (isExpired) {
                log.debug("Refresh token has expired");
                return false;
            }
            
            log.debug("Refresh token validation successful");
            return true;
            
        } catch (Exception e) {
            log.error("Refresh token validation failed: {}", e.getMessage());
            return false;
        }
    }

    public String getUsernameFromToken(String token) {
        // 从 Token 中提取用户名，并打印日志记录提取的用户名
        Claims claims = getClaimsFromToken(token);
        String username = claims.getSubject();
        log.debug("Extracted username from token: {}", username);
        return username;
    }

    public Long getUserIdFromToken(String token) {
        // 从 Token 中提取用户 ID，并打印日志记录提取的用户 ID
        Claims claims = getClaimsFromToken(token);
        Long userId = claims.get("userId", Long.class);
        log.debug("Extracted userId from token: {}", userId);
        return userId;
    }

    public String getRoleFromToken(String token) {
        // 从 Token 中提取用户角色，并打印日志记录提取的角色
        Claims claims = getClaimsFromToken(token);
        String role = claims.get("role", String.class);
        log.debug("Extracted role from token: {}", role);
        return role;
    }

    public String getTokenIdFromToken(String token) {
        // 从 Token 中提取唯一的 Token ID，并打印日志记录提取的 Token ID
        Claims claims = getClaimsFromToken(token);
        String tokenId = claims.get("tokenId", String.class);
        log.debug("Extracted token ID from token: {}", tokenId);
        return tokenId;
    }

    public boolean validateToken(String token) {
        // 验证 Token 是否有效，包括检查 Token 是否过期，并打印相关日志记录验证结果
        try {
            log.debug("Validating token");
            Claims claims = getClaimsFromToken(token);
            boolean isExpired = claims.getExpiration().before(new Date());
            if (isExpired) {
                log.debug("Token has expired");
                return false;
            }
            Long createdAt = claims.get("createdAt", Long.class);
            String tokenId = claims.get("tokenId", String.class);
            log.debug("Token validation successful - ID: {}, Created: {}", tokenId, new Date(createdAt));
            return true;
        } catch (Exception e) {
            log.error("Token validation failed: {}", e.getMessage());
            return false;
        }
    }

    private Claims getClaimsFromToken(String token) {
        // 解析 Token 并返回其中的 Claims 声明
        return Jwts.parserBuilder()
                .setSigningKey(signingKey)
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    public String extractTokenId(String token) {
        // 尝试从 Token 中提取唯一的 Token ID，并打印日志记录提取结果
        log.debug("Extracting token ID from token");
        try {
            Claims claims = getClaimsFromToken(token);
            String tokenId = claims.get("tokenId", String.class);
            log.debug("Extracted token ID: {}", tokenId);
            return tokenId;
        } catch (Exception e) {
            log.error("Failed to extract token ID from token", e);
            return null;
        }
    }

    public Long extractUserId(String token) {
        // 尝试从 Token 中提取用户 ID，并打印日志记录提取结果
        log.debug("Extracting user ID from token");
        try {
            Claims claims = getClaimsFromToken(token);
            Long userId = claims.get("userId", Long.class);
            log.debug("Extracted user ID: {}", userId);
            return userId;
        } catch (Exception e) {
            log.error("Failed to extract user ID from token", e);
            return null;
        }
    }

    public String extractUsername(String token) {
        // 尝试从 Token 中提取用户名，并打印日志记录提取结果
        log.debug("Extracting username from token");
        try {
            Claims claims = getClaimsFromToken(token);
            String username = claims.getSubject();
            log.debug("Extracted username: {}", username);
            return username;
        } catch (Exception e) {
            log.error("Failed to extract username from token", e);
            return null;
        }
    }
}
