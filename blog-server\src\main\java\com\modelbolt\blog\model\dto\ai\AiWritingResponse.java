package com.modelbolt.blog.model.dto.ai;

import lombok.Data;
import java.util.List;

@Data
public class AiWritingResponse {
    /**
     * 写作类型
     */
    private AiWritingRequest.WritingType type;

    /**
     * 文章创意列表（生成模式第一步使用）
     */
    private List<ArticleIdea> ideas;

    /**
     * 生成的文章（生成模式第二步或编辑模式使用）
     */
    private Article article;

    /**
     * 是否有内容更新（编辑模式使用）
     */
    private boolean hasChanges;

    /**
     * 更新的内容（编辑模式使用）
     */
    private Updates updates;

    @Data
    public static class ArticleIdea {
        private String title;
        private String outline;
        private String summary;
        private List<String> keywords;
    }

    @Data
    public static class Article {
        private String title;
        private String content;
        private String summary;
    }

    @Data
    public static class Updates {
        private String title;
        private String content;
        private String summary;
        private String diff;
    }
} 