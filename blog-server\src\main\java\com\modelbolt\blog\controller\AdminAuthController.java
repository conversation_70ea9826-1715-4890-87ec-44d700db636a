package com.modelbolt.blog.controller;

import com.modelbolt.blog.annotation.OperationLog;
import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.model.dto.auth.AdminLoginRequest;
import com.modelbolt.blog.model.dto.auth.AdminLoginResponse;
import com.modelbolt.blog.model.dto.auth.RefreshTokenRequest;
import com.modelbolt.blog.model.dto.auth.RefreshTokenResponse;
import com.modelbolt.blog.model.dto.auth.SendVerificationCodeRequest;
import com.modelbolt.blog.service.AdminAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.core.Authentication;

@Tag(name = "管理员认证接口", description = "管理员认证相关接口")
@RestController
@RequestMapping("/auth/admin")
@RequiredArgsConstructor
public class AdminAuthController {

    private final AdminAuthService adminAuthService;

    @Operation(summary = "管理员登录")
    @PostMapping("/login")
//    @OperationLog("管理员登录")
    public ApiResponse<AdminLoginResponse> login(@RequestBody @Valid AdminLoginRequest request, HttpServletRequest servletRequest) {
        return adminAuthService.login(request, servletRequest);
    }

    @Operation(summary = "发送验证码")
    @PostMapping("/send-verification-code")
    @OperationLog("发送管理员验证码")
    public ApiResponse<Void> sendVerificationCode(@RequestBody @Valid SendVerificationCodeRequest request) {
        return adminAuthService.sendVerificationCode(request.getEmail());
    }

    @Operation(summary = "管理员登出")
    @PostMapping("/logout")
    public ApiResponse<Void> logout(@RequestHeader(value = "Authorization", required = false) String token) {
        if (token != null && token.startsWith("Bearer ")) {
            // 移除 Bearer 前缀
            token = token.substring(7);
        }
        return adminAuthService.logout(token);
    }

    @Operation(summary = "检查token有效性")
    @GetMapping("/check-token")
    public ApiResponse<Void> checkToken() {
        // 如果请求能通过认证过滤器，说明token有效
        return ApiResponse.success();
    }
    
    @Operation(summary = "刷新令牌")
    @PostMapping("/refresh-token")
    public ApiResponse<RefreshTokenResponse> refreshToken(@RequestBody @Valid RefreshTokenRequest request) {
        return adminAuthService.refreshToken(request);
    }
} 