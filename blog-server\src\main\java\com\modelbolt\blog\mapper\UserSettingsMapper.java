package com.modelbolt.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.modelbolt.blog.model.entity.UserSettings;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface UserSettingsMapper extends BaseMapper<UserSettings> {
    @Select("SELECT * FROM user_settings WHERE user_id = #{userId}")
    UserSettings selectByUserId(@Param("userId") Long userId);

    @Insert("INSERT INTO user_settings (user_id, theme, notification_enabled, language) " +
            "VALUES (#{userId}, #{theme}, #{notificationEnabled}, #{language})")
    int insertSettings(UserSettings settings);

    @Update("UPDATE user_settings SET theme = #{settings.theme}, " +
            "notification_enabled = #{settings.notificationEnabled}, " +
            "language = #{settings.language} " +
            "WHERE user_id = #{settings.userId}")
    int updateSettings(@Param("settings") UserSettings settings);
} 