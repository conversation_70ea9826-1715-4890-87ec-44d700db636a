import { defineStore } from 'pinia'
import { ref, nextTick } from 'vue'
import { http } from '@/utils/request'
import type { LoginParams, RegisterParams, UserInfo } from '@/types/user'
import router from '@/router'
import { useNotificationStore } from './notification'
import { useOnlineStore } from './online'
import { clearImageFailedCache } from '@/utils/image'
import { useSettingsStore } from './settings'

export const useUserStore = defineStore('user', () => {
  const token = ref(localStorage.getItem('token') || sessionStorage.getItem('token') || '')
  const refreshToken = ref(localStorage.getItem('refreshToken') || sessionStorage.getItem('refreshToken') || '')
  const userInfo = ref<UserInfo | null>(null)
  const isAuthenticated = ref(!!token.value)
  const notificationStore = useNotificationStore()
  const onlineStore = useOnlineStore()

  const getUserInfo = async () => {
    if (!token.value) {
      isAuthenticated.value = false
      return null
    }

    try {
      const response = await http.get<UserInfo>('/user/info')
      const data = response.data
      
      if (!data || typeof data.id !== 'number' || !data.username || !data.email) {
        throw new Error('Invalid user info response')
      }

      // 如果头像发生变化，清除旧头像的缓存
      if (userInfo.value?.avatar !== data.avatar) {
        clearImageFailedCache(userInfo.value?.avatar)
      }

      userInfo.value = data
      isAuthenticated.value = true
      
      // 初始化WebSocket连接
      notificationStore.initWebSocket()
      onlineStore.initWebSocket()
      
      return data
    } catch (error: any) {
      if (error.response?.status === 401) {
        await logout()
      }
      console.error('Failed to get user info:', error)
      return null
    }
  }

  const refreshAccessToken = async () => {
    if (!refreshToken.value) {
      console.error('No refresh token available')
      return false
    }
    
    try {
      const response = await http.post('/auth/refresh-token', { refreshToken: refreshToken.value })
      const data = response.data
      
      // 更新访问令牌
      token.value = data.accessToken
      
      // 根据之前的存储位置保存新的token
      if (localStorage.getItem('refreshToken')) {
        localStorage.setItem('token', data.accessToken)
      } else {
        sessionStorage.setItem('token', data.accessToken)
      }
      
      console.log('Token refreshed successfully')
      return true
    } catch (error) {
      console.error('Failed to refresh token:', error)
      return false
    }
  }

  const login = async (params: LoginParams) => {
    try {
      const response = await http.post('/auth/login', params)
      const { token: newToken, refreshToken: newRefreshToken, user } = response.data
      
      // 根据记住我选项决定token存储位置
      if (params.rememberMe) {
        localStorage.setItem('token', newToken)
        localStorage.setItem('refreshToken', newRefreshToken)
      } else {
        sessionStorage.setItem('token', newToken)
        sessionStorage.setItem('refreshToken', newRefreshToken)
      }
      
      // 先设置token和用户信息
      token.value = newToken
      refreshToken.value = newRefreshToken
      userInfo.value = user
      isAuthenticated.value = true
      
      // 确保状态更新完成
      await nextTick()
      
      // 初始化WebSocket连接
      notificationStore.initWebSocket()
      onlineStore.initWebSocket()
      
      // 加载用户设置
      const settingsStore = useSettingsStore()
      await settingsStore.loadSettings()
      
      // 获取重定向地址
      const redirect = router.currentRoute.value.query.redirect
      // 登录成功后跳转
      if (redirect && typeof redirect === 'string' && !redirect.startsWith('/auth/')) {
        await router.replace(decodeURIComponent(redirect))
      } else {
        await router.replace('/')
      }

      return true
    } catch (error) {
      return false
    }
  }

  const register = async (params: RegisterParams) => {
    try {
      const response = await http.post('/auth/register', params)
      const { token: newToken, refreshToken: newRefreshToken, user } = response.data
      token.value = newToken
      refreshToken.value = newRefreshToken
      userInfo.value = user
      localStorage.setItem('token', newToken)
      localStorage.setItem('refreshToken', newRefreshToken)
      isAuthenticated.value = true
      
      // 初始化WebSocket连接
      notificationStore.initWebSocket()
      onlineStore.initWebSocket()
      
      // 注册成功后直接跳转到首页
      await router.replace('/home')
      return true
    } catch (error) {
      return false
    }
  }

  const sendVerificationCode = async (email: string) => {
    await http.post('/auth/send-verification-code', { email })
  }

  const logout = async () => {
    try {
      // 如果有token，调用后端登出接口
      if (token.value) {
        await http.post('/auth/logout')
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 关闭WebSocket连接
      notificationStore.closeWebSocket()
      onlineStore.closeWebSocket()
      
      token.value = ''
      refreshToken.value = ''
      userInfo.value = null
      isAuthenticated.value = false
      
      // 清除所有token存储
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
      sessionStorage.removeItem('token')
      sessionStorage.removeItem('refreshToken')
      
      // 登出后跳转到登录页
      const currentPath = router.currentRoute.value.fullPath
      if (!currentPath.startsWith('/auth/')) {
        await router.replace('/auth/login')
      }
    }
  }

  const forgotPassword = async (email: string) => {
    await http.post('/auth/password/reset/email', undefined, { params: { email } })
  }

  const resetPassword = async (params: { token: string; password: string }) => {
    await http.post('/auth/password/reset', params)
  }

  return {
    token,
    refreshToken,
    userInfo,
    isAuthenticated,
    login,
    register,
    refreshAccessToken,
    sendVerificationCode,
    getUserInfo,
    logout,
    forgotPassword,
    resetPassword
  }
})