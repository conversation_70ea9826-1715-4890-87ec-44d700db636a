<template>
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
      <!-- 用户基本信息卡片 -->
      <el-card shadow="never" class="mb-6">
        <div class="flex items-start space-x-6">
          <!-- 用户头像 -->
          <el-avatar 
            :size="80" 
            :src="getAvatarUrl(userInfo?.avatar)"
            @error="imgError"
          >
            {{ userInfo?.username?.charAt(0)?.toUpperCase() }}
          </el-avatar>
          
          <!-- 用户信息 -->
          <div class="flex-1">
            <div class="flex items-center justify-between">
              <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ userInfo?.username }}</h1>
                <p class="mt-1 text-sm text-gray-500">{{ userInfo?.bio || '这个人很懒，什么都没写~' }}</p>
              </div>
              <!-- 关注按钮 -->
              <follow-button
                v-if="userInfo?.id && userInfo.id !== currentUserId"
                :user-id="userInfo.id"
                :initial-is-following="isFollowing"
                @follow="handleFollow"
                @unfollow="handleUnfollow"
              />
            </div>
            
            <!-- 统计信息 -->
            <div class="flex items-center space-x-6 mt-4">
              <div class="text-center">
                <div class="text-lg font-semibold text-gray-900">{{ stats.articles || 0 }}</div>
                <div class="text-sm text-gray-500">文章</div>
              </div>
              <div class="text-center cursor-pointer" @click="handleFollowClick('following')">
                <div class="text-lg font-semibold text-gray-900">{{ stats.following || 0 }}</div>
                <div class="text-sm text-gray-500">关注</div>
              </div>
              <div class="text-center cursor-pointer" @click="handleFollowClick('followers')">
                <div class="text-lg font-semibold text-gray-900">{{ stats.followers || 0 }}</div>
                <div class="text-sm text-gray-500">粉丝</div>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 文章列表 -->
      <div v-loading="loading">
        <div v-if="articles.length === 0" class="text-center py-8">
          <el-empty description="暂无文章" />
        </div>
        <template v-else>
          <article-list
            v-model="currentPageStr"
            :articles="articles"
            :total="total"
            :current-page="currentPage"
            :page-size="pageSize"
            view-mode="list"
            @size-change="handleSizeChange"
            @page-change="handleCurrentChange"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { getAvatarUrl, useImageFallback, getRefreshedImageUrl } from '@/utils/image'
import { getUserProfile, checkIsFollowing } from '@/api/userCenter'
import { getUserPublicArticles } from '@/api/article'
import type { ArticleVO } from '@/types/article'
import ArticleList from '@/components/article/ArticleList.vue'
import FollowButton from '@/components/user/FollowButton.vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const { imgError } = useImageFallback()

// 当前登录用户ID
const currentUserId = computed(() => userStore.userInfo?.id)

// 用户信息
const userInfo = ref<{
  id: number
  username: string
  avatar: string
  bio: string
}>()

// 统计信息
const stats = ref({
  articles: 0,
  following: 0,
  followers: 0
})

// 文章列表相关
const loading = ref(false)
const articles = ref<ArticleVO[]>([])
const currentPage = ref(1)
const currentPageStr = computed({
  get: () => String(currentPage.value),
  set: (val) => {
    currentPage.value = Number(val)
  }
})
const pageSize = ref(10)
const total = ref(0)
const isFollowing = ref(false)

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const userId = Number(route.params.id)
    if (!userId) {
      ElMessage.error('用户ID无效')
      return
    }

    const { data } = await getUserProfile(userId)
    userInfo.value = {
      id: data.id,
      username: data.username,
      avatar: data.avatar,
      bio: data.bio
    }
    stats.value = data.stats

    // 如果已登录，检查是否已关注
    if (currentUserId.value && currentUserId.value !== userId) {
      try {
        const followRes = await checkIsFollowing(userId)
        isFollowing.value = followRes.data
        console.log('关注状态检查:', { 
          userId, 
          currentUserId: currentUserId.value, 
          isFollowing: isFollowing.value 
        })
      } catch (error) {
        console.error('检查关注状态失败:', error)
      }
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
    ElMessage.error('加载用户信息失败')
  }
}

// 加载文章列表
const loadArticles = async () => {
  loading.value = true
  try {
    const userId = Number(route.params.id)
    const { data } = await getUserPublicArticles({
      userId,
      page: currentPage.value,
      size: pageSize.value
    })
    articles.value = data.records
    total.value = data.total
  } catch (error) {
    console.error('加载文章列表失败:', error)
    ElMessage.error('加载文章列表失败')
  } finally {
    loading.value = false
  }
}

// 处理分页大小改变
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadArticles()
}

// 处理页码改变
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadArticles()
}

// 处理关注/取消关注
const handleFollow = () => {
  stats.value.followers++
  isFollowing.value = true // 更新关注状态
}

const handleUnfollow = () => {
  stats.value.followers--
  isFollowing.value = false // 更新关注状态
}

// 处理关注/粉丝点击
const handleFollowClick = (type: 'following' | 'followers') => {
  if (!userInfo.value?.id) return
  router.push({
    path: '/user/follows',
    query: { 
      userId: userInfo.value.id.toString(),
      type
    }
  })
}

// 监听路由参数变化
watch(() => route.params.id, (newId) => {
  if (newId) {
    console.log('路由参数变化，重新加载用户信息:', newId)
    loadUserInfo()
    loadArticles()
  }
}, { immediate: true }) // 添加 immediate: true 确保首次加载时也执行

onMounted(() => {
  const userId = route.params.id
  if (userId) {
    console.log('组件挂载，加载用户信息:', userId)
    loadUserInfo()
    loadArticles()
  }
})
</script> 