package com.modelbolt.blog.model.dto.ai;

import lombok.Data;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class AiWritingRequest {
    public enum WritingType {
        ARTICLE_GENERATION,  // 文章生成
        ARTICLE_EDITING     // 文章编辑
    }

    /**
     * 写作类型
     */
    @NotNull(message = "写作类型不能为空")
    private WritingType type;

    /**
     * 关键词列表
     */
    private List<String> keywords;

    /**
     * 目标受众
     */
    private String targetAudience;

    /**
     * 文章类型
     */
    private String articleType;

    /**
     * 当前步骤
     */
    private Integer step;

    /**
     * 选中的大纲索引
     */
    private Integer selectedOutlineIndex;

    /**
     * 文章ID（编辑模式使用）
     */
    private Long articleId;

    /**
     * 原始文章内容（编辑模式使用）
     */
    private String originalContent;
}