import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { SearchSuggestion, SearchHistory, HotSearch } from '@/types/search'
import * as searchApi from '@/api/search'
import type { SearchParams } from '@/api/search'

const HISTORY_KEY = 'search_history'
const MAX_HISTORY = 10

export const useSearchStore = defineStore('search', () => {
  const keyword = ref('')
  const history = ref<SearchHistory[]>(JSON.parse(localStorage.getItem(HISTORY_KEY) || '[]'))
  const suggestions = ref<SearchSuggestion[]>([])
  const isSearching = ref(false)
  const hotSearches = ref<HotSearch[]>([])

  // 添加搜索历史
  const addHistory = (searchKeyword: string) => {
    if (!searchKeyword || typeof searchKeyword !== 'string') return
    
    const trimmedKeyword = searchKeyword.trim()
    if (!trimmedKeyword) return
    
    const timestamp = Date.now()
    const newHistory = {
      keyword: trimmedKeyword,
      timestamp
    }
    
    // 移除重复的搜索记录
    history.value = history.value.filter(item => item.keyword !== trimmedKeyword)
    
    // 添加新记录到开头
    history.value.unshift(newHistory)
    
    // 限制历史记录数量
    if (history.value.length > MAX_HISTORY) {
      history.value = history.value.slice(0, MAX_HISTORY)
    }
    
    // 保存到本地存储
    try {
      localStorage.setItem(HISTORY_KEY, JSON.stringify(history.value))
    } catch (error) {
      console.error('保存搜索历史失败:', error)
    }
  }

  // 清除搜索历史
  const clearHistory = () => {
    history.value = []
    localStorage.removeItem(HISTORY_KEY)
  }

  // 获取搜索建议
  const getSuggestions = async (searchKeyword: string) => {
    if (!searchKeyword || !searchKeyword.trim()) {
      return { data: [] }
    }
    
    try {
      isSearching.value = true
      const response = await searchApi.getSuggestions(searchKeyword)
      return response
    } catch (error) {
      console.error('获取搜索建议失败:', error)
      return { data: [] }
    } finally {
      isSearching.value = false
    }
  }

  // 获取热门搜索
  const getHotSearches = async () => {
    try {
      const response = await searchApi.getHotSearches()
      // 确保返回的数据符合预期格式
      if (Array.isArray(response.data)) {
        hotSearches.value = response.data.map(item => ({
          keyword: item.keyword,
          count: item.count || 0
        }))
      } else {
        hotSearches.value = []
      }
    } catch (error) {
      console.error('获取热门搜索失败:', error)
      hotSearches.value = []
    }
  }

  // 加载热门搜索
  const loadHotSearches = async () => {
    try {
      const { data } = await searchApi.getHotSearches()
      if (Array.isArray(data)) {
        hotSearches.value = data
      }
    } catch (error) {
      console.error('加载热门搜索失败:', error)
      hotSearches.value = []
    }
  }

  // 搜索方法
  const search = async (params: SearchParams) => {
    if (!params.keyword || !params.keyword.trim()) {
      return {
        records: [],
        total: 0,
        size: params.size || 10,
        current: params.page || 1
      }
    }

    try {
      const response = await searchApi.search(params)
      return response.data
    } catch (error) {
      console.error('搜索失败:', error)
      return {
        records: [],
        total: 0,
        size: params.size || 10,
        current: params.page || 1
      }
    }
  }

  return {
    keyword,
    history,
    suggestions,
    isSearching,
    hotSearches,
    addHistory,
    clearHistory,
    getSuggestions,
    getHotSearches,
    loadHotSearches,
    search
  }
}) 