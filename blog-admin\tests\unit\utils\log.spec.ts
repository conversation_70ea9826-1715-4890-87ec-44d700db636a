import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { log, error, getDevMode, setDevMode } from '../../../src/utils/log'

describe('日志工具函数测试', () => {
  // 保存原始的console方法
  const originalConsoleLog = console.log
  const originalConsoleError = console.error
  // 保存原始的环境设置
  const originalDevMode = getDevMode()
  
  // 模拟console方法
  beforeEach(() => {
    console.log = vi.fn()
    console.error = vi.fn()
  })
  
  // 恢复原始console方法
  afterEach(() => {
    console.log = originalConsoleLog
    console.error = originalConsoleError
    // 恢复原始环境设置
    setDevMode(originalDevMode)
    
    // 清除所有模拟
    vi.clearAllMocks()
  })
  
  describe('log函数', () => {
    it('在开发环境下应该调用console.log', () => {
      // 设置为开发环境
      setDevMode(true)
      
      // 调用log函数
      log('test message')
      
      // 验证console.log被调用
      expect(console.log).toHaveBeenCalledWith('[WebSocket]', 'test message')
    })
    
    it('在生产环境下不应该调用console.log', () => {
      // 设置为生产环境
      setDevMode(false)
      
      // 清除之前的所有调用记录
      vi.clearAllMocks()
      
      // 调用log函数
      log('test message')
      
      // 验证console.log没有被调用
      expect(console.log).not.toHaveBeenCalled()
    })
  })
  
  describe('error函数', () => {
    it('应该调用console.error', () => {
      // 调用error函数
      error('test error')
      
      // 验证console.error被调用
      expect(console.error).toHaveBeenCalledWith('[WebSocket Error]', 'test error')
    })
    
    it('应该传递多个参数给console.error', () => {
      // 调用error函数
      error('test error', { code: 500 }, 'details')
      
      // 验证console.error被调用
      expect(console.error).toHaveBeenCalledWith(
        '[WebSocket Error]', 
        'test error', 
        { code: 500 }, 
        'details'
      )
    })
  })
}) 