package com.modelbolt.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.article.ArticleVO;
import com.modelbolt.blog.model.dto.article.CreateArticleDTO;
import com.modelbolt.blog.model.dto.article.UpdateArticleDTO;
import com.modelbolt.blog.model.dto.comment.CommentVO;
import com.modelbolt.blog.model.entity.Article;

import java.util.List;

public interface ArticleService extends IService<Article> {
    /**
     * 获取置顶文章列表
     */
    List<ArticleVO> getFeaturedArticles();

    /**
     * 分页获取文章列表
     *
     * @param categoryId 分类ID
     * @param sort 排序方式：newest（最新）, hottest（最热）, recommended（推荐）
     * @param page 页码
     * @param size 每页大小
     */
    PageResult<ArticleVO> getArticles(Long categoryId, String sort, Integer page, Integer size);

    /**
     * 获取热门文章
     *
     * @param limit 获取数量
     */
    List<ArticleVO> getHotArticles(Integer limit);

    /**
     * 获取文章详情
     *
     * @param id 文章ID
     * @return 文章详情
     */
    ArticleVO getArticleDetail(Long id);

    /**
     * 获取相关文章
     *
     * @param id 当前文章ID
     * @param limit 获取数量
     * @return 相关文章列表
     */
    List<ArticleVO> getRelatedArticles(Long id, Integer limit);

    /**
     * 获取文章评论
     *
     * @param id 文章ID
     * @param page 页码
     * @param size 每页数量
     * @return 评论分页结果
     */
    PageResult<CommentVO> getArticleComments(Long id, Integer page, Integer size);

    /**
     * 更新文章浏览量
     *
     * @param id 文章ID
     */
    void updateArticleViews(Long id);

    /**
     * 获取用户文章列表
     */
    PageResult<ArticleVO> getUserArticles(String keyword, String status, Integer page, Integer size);

    /**
     * 获取指定用户的公开文章列表
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 文章分页结果
     */
    PageResult<ArticleVO> getUserPublicArticles(Long userId, Integer page, Integer size);

    /**
     * 创建文章
     */
    Long createArticle(CreateArticleDTO dto);

    /**
     * 更新文章
     */
    void updateArticle(Long id, UpdateArticleDTO dto);

    /**
     * 删除文章
     */
    void deleteArticle(Long id);

    /**
     * 点赞文章
     *
     * @param id 文章ID
     */
    void likeArticle(Long id);

    /**
     * 取消点赞
     *
     * @param id 文章ID
     */
    void unlikeArticle(Long id);

    /**
     * 收藏文章
     *
     * @param id 文章ID
     */
    void favoriteArticle(Long id);

    /**
     * 取消收藏
     *
     * @param id 文章ID
     */
    void unfavoriteArticle(Long id);

    boolean isArticleLiked(Long articleId, Long userId);

    boolean isArticleFavorited(Long articleId, Long userId);

    /**
     * 更新文章评论数
     *
     * @param articleId 文章ID
     * @return 是否更新成功
     */
    boolean updateCommentCount(Long articleId);

    /**
     * 获取文章编辑信息
     *
     * @param id 文章ID
     * @return 文章编辑信息
     */
    ArticleVO getArticleForEdit(Long id);

    /**
     * 减少文章收藏数
     *
     * @param articleId 文章ID
     * @return 是否更新成功
     */
    boolean decrementFavoriteCount(Long articleId);

    /**
     * 获取管理后台文章列表
     *
     * @param keyword 关键词
     * @param status 状态：0-草稿，1-已发布，2-已下架
     * @param categoryId 分类ID
     * @param tagId 标签ID
     * @param current 页码
     * @param size 每页数量
     * @return 文章分页结果
     */
    PageResult<ArticleVO> getAdminArticleList(String keyword, Integer status, Long categoryId, Long tagId, Integer current, Integer size);

    /**
     * 更新文章状态
     *
     * @param id 文章ID
     * @param status 状态：0-草稿，1-已发布，2-已下架
     */
    void updateArticleStatus(Long id, Integer status);

    /**
     * 更新文章置顶状态
     *
     * @param id 文章ID
     * @param isTop 是否置顶
     */
    void updateArticleTop(Long id, Boolean isTop);
} 