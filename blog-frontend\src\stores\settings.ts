import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { UserSettings } from '@/types/user'
import { getUserSettings, updateUserSettings } from '@/api/userCenter'
import { useNotificationStore } from './notification'

export const useSettingsStore = defineStore('settings', () => {
  const settings = ref<UserSettings>({
    theme: 'light',  // 默认浅色主题
    notificationEnabled: true,  // 默认开启通知
    language: 'zh_CN'
  })

  // 应用设置
  const applySettings = (newSettings: UserSettings) => {
    // 应用主题设置
    document.documentElement.setAttribute('data-theme', newSettings.theme)
    // 添加或移除 dark 类
    document.documentElement.classList.toggle('dark', newSettings.theme === 'dark')
    localStorage.setItem('theme', newSettings.theme)
    
    // 应用通知设置
    const notificationStore = useNotificationStore()
    if (newSettings.notificationEnabled === false) {
      notificationStore.closeWebSocket()
    } else {
      notificationStore.initWebSocket()
    }
  }

  // 加载用户设置
  const loadSettings = async () => {
    try {
      const { data } = await getUserSettings()
      if (data) {
        // 更新设置
        settings.value = {
          theme: data.theme || 'light',
          notificationEnabled: data.notificationEnabled ?? true,
          language: data.language || 'zh_CN'
        }
        // 立即应用设置
        applySettings(settings.value)
      }
    } catch (error) {
      console.error('加载用户设置失败:', error)
    }
  }

  // 更新设置
  const updateSettings = async (newSettings: Partial<UserSettings>) => {
    try {
      // 更新到服务器
      const { data } = await updateUserSettings({
        ...settings.value,
        ...newSettings
      })

      // 更新本地状态并应用设置
      settings.value = {
        ...settings.value,
        ...newSettings
      }
      applySettings(settings.value)

      return true
    } catch (error) {
      console.error('更新设置失败:', error)
      return false
    }
  }

  return {
    settings,
    loadSettings,
    updateSettings
  }
})
