<!-- 暂时注释整个组件 -->
<!--
<template>
  <div class="reward-button">
    <!-- 打赏按钮 -->
    <el-button
      type="danger"
      :icon="Money"
      @click="showRewardDialog"
      :disabled="!isAuthenticated"
    >
      {{ isAuthenticated ? '打赏支持' : '登录后打赏' }}
    </el-button>

    <!-- 打赏对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="打赏支持"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="reward-dialog">
        <!-- 打赏金额选择 -->
        <div class="amount-selector">
          <el-radio-group v-model="selectedAmount" class="amount-group">
            <el-radio-button
              v-for="amount in rewardConfig?.amounts || []"
              :key="amount"
              :value="amount"
            >
              {{ amount }}元
            </el-radio-button>
          </el-radio-group>
        </div>

        <!-- 支付方式选择 -->
        <div class="payment-selector mt-4">
          <el-radio-group v-model="selectedPayment">
            <el-radio :value="'WECHAT'">
              <el-icon><ChatRound /></el-icon>
              微信支付
            </el-radio>
            <el-radio :value="'ALIPAY'">
              <el-icon><Wallet /></el-icon>
              支付宝
            </el-radio>
          </el-radio-group>
        </div>

        <!-- 打赏留言 -->
        <div class="message-input mt-4">
          <el-input
            v-model="message"
            type="textarea"
            :rows="2"
            placeholder="写一句话支持作者（选填）"
            maxlength="200"
            show-word-limit
          />
        </div>

        <!-- 支付二维码 -->
        <div v-if="showQRCode" class="qrcode-container mt-4">
          <div class="text-center">
            <img
              :src="getQRCodeUrl(selectedPayment === 'WECHAT' ? rewardConfig?.qrcodeWechat : rewardConfig?.qrcodeAlipay)"
              :alt="selectedPayment === 'WECHAT' ? '微信支付' : '支付宝'"
              class="qrcode-image"
            />
            <p class="mt-2 text-gray-600">请使用{{ selectedPayment === 'WECHAT' ? '微信' : '支付宝' }}扫码支付</p>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleReward"
            :loading="loading"
          >
            确认打赏
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Money, ChatRound, Wallet } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import type { RewardConfig } from '@/api/reward'
import {
  getRewardConfig,
  createReward,
  updatePaymentStatus
} from '@/api/reward'
import { getAvatarUrl } from '@/utils/image'

const props = defineProps<{
  articleId: number
  authorId: number
}>()

const emit = defineEmits<{
  (e: 'success'): void
}>()

const userStore = useUserStore()
const { isAuthenticated } = storeToRefs(userStore)

// 打赏配置
const rewardConfig = ref<RewardConfig | null>(null)
const dialogVisible = ref(false)
const selectedAmount = ref(0)
const selectedPayment = ref('WECHAT')
const message = ref('')
const loading = ref(false)
const showQRCode = ref(false)

// 获取打赏配置
const fetchRewardConfig = async () => {
  try {
    const { data } = await getRewardConfig(props.authorId)
    // 确保 amounts 字段有默认值
    rewardConfig.value = {
      ...data,
      amounts: data?.amounts || [5, 10, 20, 50, 100] // 如果后端返回null，使用默认值
    }
    if (rewardConfig.value?.amounts?.length > 0) {
      selectedAmount.value = rewardConfig.value.amounts[0]
    }
  } catch (error) {
    console.error('获取打赏配置失败:', error)
    ElMessage.error('获取打赏配置失败')
  }
}

// 显示打赏对话框
const showRewardDialog = async () => {
  if (!isAuthenticated.value) {
    return
  }
  await fetchRewardConfig()
  if (!rewardConfig.value?.enabled) {
    ElMessage.warning('作者未开启打赏功能')
    return
  }
  dialogVisible.value = true
}

// 处理打赏
const handleReward = async () => {
  if (!selectedAmount.value) {
    ElMessage.warning('请选择打赏金额')
    return
  }
  if (!selectedPayment.value) {
    ElMessage.warning('请选择支付方式')
    return
  }

  loading.value = true
  try {
    // 创建打赏记录
    const { data: reward } = await createReward({
      articleId: props.articleId,
      userId: userStore.userInfo?.id || 0,
      amount: selectedAmount.value,
      paymentType: selectedPayment.value,
      message: message.value
    })

    // 显示支付二维码
    showQRCode.value = true

    // 模拟支付成功
    setTimeout(async () => {
      try {
        await updatePaymentStatus(reward.id, 'SUCCESS', Date.now().toString())
        ElMessage.success('打赏成功')
        dialogVisible.value = false
        emit('success')
      } catch (error) {
        console.error('更新支付状态失败:', error)
        ElMessage.error('支付状态更新失败')
      }
    }, 3000)
  } catch (error) {
    console.error('创建打赏记录失败:', error)
    ElMessage.error('创建打赏记录失败')
  } finally {
    loading.value = false
  }
}

// 获取二维码URL
const getQRCodeUrl = (url: string | undefined | null): string => {
  if (!url) return ''
  return getAvatarUrl(url)
}

onMounted(() => {
  if (props.authorId) {
    fetchRewardConfig()
  }
})
</script>

<style lang="scss" scoped>
.reward-dialog {
  .amount-group {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
  }

  .qrcode-image {
    width: 200px;
    height: 200px;
    object-fit: cover;
  }
}
</style>
-->