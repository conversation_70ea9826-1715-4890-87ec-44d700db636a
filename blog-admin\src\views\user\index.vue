<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import type { User, UserQuery, UserForm } from '@/types/user'
import { UserRole, UserStatus } from '@/types/user'
import { userApi } from '@/api/user'
import UserSearchForm from '@/components/user/UserSearchForm.vue'
import UserEditModal from '@/components/user/UserEditModal.vue'

const loading = ref(false)
const users = ref<User[]>([])
const editModalVisible = ref(false)
const editModalLoading = ref(false)
const currentEditUser = ref<UserForm | null>(null)

const queryParams = ref({
  username: '',
  email: '',
  role: '',
  status: undefined
})

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条`
})

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80
  },
  {
    title: '用户名',
    dataIndex: 'username'
  },
  {
    title: '邮箱',
    dataIndex: 'email'
  },
  {
    title: '角色',
    dataIndex: 'role'
  },
  {
    title: '状态',
    dataIndex: 'status'
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt'
  },
  {
    title: '操作',
    key: 'action',
    width: 200
  }
]

// 获取用户列表
const fetchUsers = async () => {
  try {
    loading.value = true
    const { data } = await userApi.getUsers({
      ...queryParams.value,
      page: pagination.value.current,
      pageSize: pagination.value.pageSize
    })
    users.value = data.records
    pagination.value.total = data.total
  } catch (error) {
    console.error('获取用户列表失败:', error)
    message.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 处理表格变化
const handleTableChange = (pag) => {
  pagination.value.current = pag.current
  pagination.value.pageSize = pag.pageSize
  fetchUsers()
}

// 处理搜索
const handleSearch = () => {
  pagination.value.current = 1
  fetchUsers()
}

// 处理重置
const handleReset = () => {
  queryParams.value = {
    username: '',
    email: '',
    role: '',
    status: undefined
  }
  pagination.value.current = 1
  fetchUsers()
}

// 处理编辑
const handleEdit = (record: User) => {
  currentEditUser.value = {
    id: record.id,
    username: record.username,
    email: record.email,
    role: record.role,
    status: record.status
  }
  editModalVisible.value = true
}

// 处理状态更新
const handleStatusChange = async (id: number, status: number) => {
  try {
    await userApi.updateUserStatus(id, status)
    message.success('状态更新成功')
    fetchUsers()
  } catch (error) {
    console.error('状态更新失败:', error)
    message.error('状态更新失败')
  }
}

// 处理表单提交
const handleSubmit = async (data: UserForm) => {
  try {
    editModalLoading.value = true
    if (data.id) {
      await userApi.updateUser(data.id, data)
      message.success('更新成功')
    }
    editModalVisible.value = false
    fetchUsers()
  } catch (error) {
    console.error('操作失败:', error)
    message.error('操作失败')
  } finally {
    editModalLoading.value = false
  }
}

// 处理导出
const handleExport = async () => {
  try {
    loading.value = true
    const response = await userApi.exportUsers({
      username: queryParams.value.username,
      email: queryParams.value.email,
      role: queryParams.value.role,
      status: queryParams.value.status
    })
    
    // 解码文件名并处理编码问题
    const decodeFilename = (header: string | undefined) => {
      if (!header) return '用户列表.xlsx'
      
      try {
        const filenamePart = header.split('filename*=utf-8\'\'')[1]
        const encodedName = filenamePart.replace(/["']/g, '')
        return decodeURIComponent(encodedName)
      } catch (e) {
        console.warn('文件名解析失败:', e)
        return '用户列表.xlsx'
      }
    }

    const filename = decodeFilename(response.headers['content-disposition'])
    
    // 创建 Blob 对象，使用response.data而不是整个response
    const blob = new Blob([response.data], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(link)
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchUsers()
})
</script>

<template>
  <div class="user-list">
    <!-- 搜索表单 -->
    <UserSearchForm
      v-model:username="queryParams.username"
      v-model:email="queryParams.email"
      v-model:role="queryParams.role"
      v-model:status="queryParams.status"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 操作按钮 -->
    <div class="table-operations mb-4">
      <a-space>
        <a-button
          type="primary"
          :loading="loading"
          @click="handleExport"
        >
          导出
        </a-button>
        <a-button
          @click="fetchUsers"
        >
          刷新
        </a-button>
      </a-space>
    </div>

    <!-- 用户表格 -->
    <a-table
      :columns="columns"
      :data-source="users"
      :pagination="pagination"
      :loading="loading"
      :row-selection="null"
      :scroll="{ x: 1200 }"
      @change="handleTableChange"
      row-key="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'role'">
          <a-tag :color="record.role === 'ADMIN' ? 'blue' : ''">
            {{ record.role === 'ADMIN' ? '管理员' : '普通用户' }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'status'">
          <a-tag :color="record.status === 1 ? 'success' : 'error'">
            {{ record.status === 1 ? '启用' : '禁用' }}
          </a-tag>
        </template>
        <template v-else-if="column.key === 'action'">
          <a-space>
            <a-button type="link" @click="handleEdit(record)">
              编辑
            </a-button>
            <a-button
              type="link"
              :danger="record.status === 1"
              @click="handleStatusChange(record.id, record.status === 1 ? 0 : 1)"
            >
              {{ record.status === 1 ? '禁用' : '启用' }}
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 编辑模态框 -->
    <UserEditModal
      v-model:visible="editModalVisible"
      :loading="editModalLoading"
      :edit-data="currentEditUser"
      @submit="handleSubmit"
    />
  </div>
</template>

<style scoped>
.user-list {
  padding: 24px;
  background: #fff;
}

.table-operations {
  margin-bottom: 16px;
}
</style> 