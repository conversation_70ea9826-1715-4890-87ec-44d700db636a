<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import type { ArticleForm } from '@/types/article'
import { ArticleStatus } from '@/types/article'
import { getArticleForEdit, createArticle, updateArticle, uploadArticleCover, updateArticleTop } from '@/api/article'
import MdEditor from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'
import { PlusOutlined, RobotOutlined } from '@ant-design/icons-vue'
import { getAvatarUrl, getRefreshedImageUrl, getCoverUrl, useImageFallback } from '@/utils/image'
import type { UploadProps } from 'ant-design-vue'
import type { Article } from '@/types/article'
import { getAllCategories, getAllTags } from '@/api/category'
import type { CategoryVO, TagVO } from '@/types/category'
import AiWritingAssistant from '@/components/AiWritingAssistant.vue'

const route = useRoute()
const router = useRouter()
const id = route.params.id ? Number(route.params.id) : undefined
const { imgError } = useImageFallback()

// 文件列表
const fileList = ref<UploadProps['fileList']>([])

// 表单数据
const formData = reactive<ArticleForm>({
  title: '',
  content: '',
  summary: '',
  cover: '',
  categoryIds: [],
  tagIds: [],
  status: ArticleStatus.DRAFT,
  isTop: false
})

// 状态选项
const statusOptions = [
  { label: '草稿', value: ArticleStatus.DRAFT },
  { label: '发布', value: ArticleStatus.PUBLISHED }
]

// 加载状态
const loading = ref(false)
const submitting = ref(false)

// 分类和标签数据
const categories = ref<CategoryVO[]>([])
const tags = ref<TagVO[]>([])

// AI写作助手引用
const aiAssistant = ref()

// 获取分类和标签数据
const fetchCategoriesAndTags = async () => {
  try {
    const [categoriesRes, tagsRes] = await Promise.all([
      getAllCategories(),
      getAllTags()
    ])
    
    if (categoriesRes.code === 200 && categoriesRes.data) {
      categories.value = categoriesRes.data
    }
    
    if (tagsRes.code === 200 && tagsRes.data) {
      tags.value = tagsRes.data
    }
  } catch (error) {
    console.error('获取分类和标签数据失败:', error)
    message.error('获取分类和标签数据失败')
  }
}

// 获取文章详情
const getDetail = async () => {
  if (!id) return
  loading.value = true
  try {
    const { data } = await getArticleForEdit(id)
    // 确保所有字段都被正确赋值，特别注意布尔值的处理
    formData.title = data.title || ''
    formData.content = data.content || ''
    formData.summary = data.summary || ''
    formData.cover = data.cover || ''
    formData.categoryIds = data.categoryIds || []
    formData.tagIds = data.tagIds || []
    formData.status = data.status ?? ArticleStatus.DRAFT
    // 确保 isTop 是布尔值
    formData.isTop = Boolean(data.isTop)

    // 如果有封面，更新文件列表
    if (data.cover) {
      fileList.value = [{
        uid: '-1',
        name: 'cover',
        status: 'done',
        url: getCoverUrl(data.cover)
      }]
    }
  } catch (error) {
    console.error('获取文章详情失败:', error)
    message.error('获取文章详情失败')
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formData.title) {
    message.warning('请输入文章标题')
    return
  }
  if (!formData.content) {
    message.warning('请输入文章内容')
    return
  }

  submitting.value = true
  try {
    // 确保提交的数据中 isTop 是布尔值
    const submitData = {
      ...formData,
      isTop: Boolean(formData.isTop)
    }
    
    if (id) {
      await updateArticle(id, submitData)
      message.success('更新成功')
    } else {
      await createArticle(submitData)
      message.success('创建成功')
    }
    router.push('/article')
  } catch (error) {
    message.error(id ? '更新失败' : '创建失败')
  } finally {
    submitting.value = false
  }
}

// 上传封面
const handleCoverUpload = async (file: File) => {
  const loadingMessage = message.loading('正在上传...', 0)
  try {
    const response = await uploadArticleCover(file)
    if (response.code === 200) {
      const imageUrl = response.message
      console.log('Upload success, image URL:', imageUrl)
      formData.cover = imageUrl
      fileList.value = [{
        uid: '-1',
        name: file.name,
        status: 'done',
        url: getCoverUrl(imageUrl)
      }]
      message.success('上传成功')
    } else {
      message.error(response.message || '上传失败')
    }
    return false
  } catch (error) {
    console.error('Upload error:', error)
    message.error('上传失败，请重试')
    return false
  } finally {
    loadingMessage()
  }
}

// 预览文章
const handlePreview = () => {
  if (id) {
    router.push(`/article/preview/${id}`)
  }
}

// 返回列表
const handleBack = () => {
  router.push('/article')
}

// 处理置顶状态变更
const handleTopChange = async (checked: boolean) => {
  if (!id) return
  try {
    await updateArticleTop(id, checked)
    formData.isTop = checked
    message.success(`${checked ? '置顶' : '取消置顶'}成功`)
  } catch (error) {
    message.error(`${checked ? '置顶' : '取消置顶'}失败`)
    // 恢复原状态
    formData.isTop = !checked
  }
}

// 打开AI写作助手
const openAiAssistant = () => {
  aiAssistant.value?.showAssistant()
}

onMounted(async () => {
  // 先加载分类和标签数据
  await fetchCategoriesAndTags()
  // 如果是编辑模式，加载文章详情
  if (id) {
    await getDetail()
  }
})
</script>

<template>
  <div class="article-edit">
    <div class="header">
      <div class="title">{{ id ? '编辑文章' : '新建文章' }}</div>
      <div class="actions">
        <a-space>
          <a-button @click="handleBack">返回</a-button>
          <a-button v-if="id" type="primary" ghost @click="handlePreview">预览</a-button>
          <a-button
            type="primary"
            :loading="submitting"
            @click="handleSubmit"
          >
            保存
          </a-button>
        </a-space>
      </div>
    </div>

    <a-spin :spinning="loading">
      <div class="content">
        <div class="main">
          <!-- 标题输入 -->
          <a-input
            v-model:value="formData.title"
            placeholder="请输入文章标题"
            size="large"
            class="title-input"
          />

          <!-- AI写作助手按钮 -->
          <div class="ai-assistant-wrapper">
            <a-button type="primary" ghost @click="openAiAssistant">
              <template #icon>
                <RobotOutlined />
              </template>
              AI写作助手
            </a-button>
          </div>

          <!-- Markdown编辑器 -->
          <div class="editor-wrapper">
            <MdEditor
              v-model="formData.content"
              placeholder="请输入文章内容"
              class="md-editor"
            />
          </div>
        </div>

        <div class="side">
          <!-- 文章属性 -->
          <a-card title="文章属性" :bordered="false" class="article-properties">
            <!-- 封面上传 -->
            <a-form-item label="封面图">
              <div class="cover-upload">
                <a-upload
                  :file-list="fileList"
                  list-type="picture-card"
                  :show-upload-list="false"
                  :before-upload="handleCoverUpload"
                  :accept="'.jpg,.jpeg,.png,.gif'"
                  class="cover-uploader"
                >
                  <div class="cover-content">
                    <template v-if="formData.cover">
                      <img 
                        :src="getRefreshedImageUrl(getCoverUrl(formData.cover))" 
                        :key="formData.cover"
                        alt="封面"
                        class="cover-image"
                        @error="imgError"
                        @load="() => console.log('Image loaded:', formData.cover)"
                      />
                    </template>
                    <div v-else class="upload-placeholder">
                      <plus-outlined />
                      <div class="ant-upload-text">上传封面</div>
                      <div class="ant-upload-hint">支持 jpg、png、gif 格式</div>
                    </div>
                  </div>
                </a-upload>
              </div>
            </a-form-item>

            <!-- 文章摘要 -->
            <a-form-item label="文章摘要">
              <a-textarea
                v-model:value="formData.summary"
                placeholder="请输入文章摘要"
                :rows="4"
                :maxLength="200"
                show-count
              />
            </a-form-item>

            <!-- 文章状态 -->
            <a-form-item label="文章状态">
              <a-select
                v-model:value="formData.status"
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <!-- 是否置顶 -->
            <a-form-item label="是否置顶">
              <a-switch
                :checked="formData.isTop"
                @change="handleTopChange"
                :disabled="!id"
              >
                <template #checkedChildren>已置顶</template>
                <template #unCheckedChildren>未置顶</template>
              </a-switch>
            </a-form-item>

            <!-- 文章分类 -->
            <a-form-item label="文章分类">
              <a-select
                v-model:value="formData.categoryIds"
                mode="multiple"
                placeholder="请选择文章分类"
                style="width: 100%"
                :options="categories.map(category => ({
                  value: category.id,
                  label: category.name
                }))"
              />
            </a-form-item>

            <!-- 文章标签 -->
            <a-form-item label="文章标签">
              <a-select
                v-model:value="formData.tagIds"
                mode="multiple"
                placeholder="请选择文章标签"
                style="width: 100%"
                :options="tags.map(tag => ({
                  value: tag.id,
                  label: tag.name
                }))"
              />
            </a-form-item>
          </a-card>
        </div>
      </div>
    </a-spin>

    <!-- AI写作助手组件 -->
    <AiWritingAssistant
      ref="aiAssistant"
      :mode="id ? 'edit' : 'new'"
      :article-id="id"
      :content="formData.content"
      :title="formData.title"
      :summary="formData.summary"
      @update:content="formData.content = $event"
      @update:title="formData.title = $event"
      @update:summary="formData.summary = $event"
    />
  </div>
</template>

<style lang="scss" scoped>
.article-edit {
  padding: 24px;
  background: #fff;
  min-height: 100%;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .title {
      font-size: 20px;
      font-weight: 500;
    }
  }

  .content {
    display: flex;
    gap: 24px;

    .main {
      flex: 1;
      min-width: 0;

      .title-input {
        margin-bottom: 24px;
        font-size: 24px;
      }

      .ai-assistant-wrapper {
        margin: 16px 0;
        text-align: right;
      }

      .editor-wrapper {
        position: relative;
        height: calc(100vh - 300px);
        
        .md-editor {
          height: 100%;
        }
      }
    }

    .side {
      width: 320px;
      flex-shrink: 0;

      .article-properties {
        position: sticky;
        top: 24px;
      }

      .cover-upload {
        width: 100%;
        
        .cover-uploader {
          width: 100%;
          
          :deep(.ant-upload.ant-upload-select) {
            width: 100%;
            height: 180px;
            margin: 0;
          }

          .cover-content {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #fafafa;
            border: 1px dashed #d9d9d9;
            border-radius: 8px;
            cursor: pointer;
            transition: border-color 0.3s;

            &:hover {
              border-color: #1890ff;
            }

            .cover-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 8px;
            }

            .upload-placeholder {
              text-align: center;
              
              .anticon {
                font-size: 24px;
                color: #999;
                margin-bottom: 8px;
              }
            }
          }
        }
      }
    }
  }
}

.ai-assistant-wrapper {
  margin: 16px 0;
  text-align: right;
}
</style> 