package com.modelbolt.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.user.UserInfoDTO;
import com.modelbolt.blog.model.dto.user.AuthorInfoDTO;
import com.modelbolt.blog.model.dto.user.UserSettingsDTO;
import com.modelbolt.blog.model.dto.user.UserProfileDTO;
import com.modelbolt.blog.model.dto.user.AdminUserQuery;
import com.modelbolt.blog.model.dto.user.AdminUserVO;
import com.modelbolt.blog.model.dto.user.UpdateUserDTO;
import com.modelbolt.blog.model.dto.user.UserHomeDTO;
import com.modelbolt.blog.model.entity.User;
import com.modelbolt.blog.model.entity.UserSettings;
import com.modelbolt.blog.model.entity.UserFollow;
import com.modelbolt.blog.model.entity.Article;
import com.modelbolt.blog.model.entity.Comment;
import com.modelbolt.blog.model.entity.UserFavorite;
import com.modelbolt.blog.exception.business.AuthException;
import com.modelbolt.blog.exception.business.BusinessException;
import com.modelbolt.blog.mapper.UserFollowMapper;
import com.modelbolt.blog.mapper.UserMapper;
import com.modelbolt.blog.mapper.UserSettingsMapper;
import com.modelbolt.blog.mapper.ArticleMapper;
import com.modelbolt.blog.mapper.CommentMapper;
import com.modelbolt.blog.mapper.UserFavoriteMapper;
import com.modelbolt.blog.service.UserService;
import com.modelbolt.blog.utils.FileUtils;
import com.modelbolt.blog.utils.ExcelUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.multipart.MultipartFile;
import jakarta.servlet.http.HttpServletResponse;
import com.modelbolt.blog.enums.ArticleStatus;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private final UserMapper userMapper;
    private final UserSettingsMapper userSettingsMapper;
    private final UserFollowMapper userFollowMapper;
    private final ArticleMapper articleMapper;
    private final CommentMapper commentMapper;
    private final UserFavoriteMapper userFavoriteMapper;
    private final PasswordEncoder passwordEncoder;
    private final FileUtils fileUtils;

    @Override
    public UserInfoDTO getUserInfo(String username) {
        log.debug("Getting user info for username: {}", username);
        
        User user = userMapper.findByUsername(username);
        if (user == null) {
            log.error("User not found: {}", username);
            throw new AuthException.UserNotFound();
        }

        return UserInfoDTO.builder()
                .id(user.getId())
                .username(user.getUsername())
                .email(user.getEmail())
                .avatar(user.getAvatar())
                .bio(user.getBio())
                .role(user.getRole())
                .status(user.getStatus())
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt())
                .build();
    }

    @Override
    public AuthorInfoDTO getAuthorInfo(Long id) {
        log.debug("Getting author info for id: {}", id);
        
        User user = userMapper.selectById(id);
        if (user == null) {
            log.error("User not found with id: {}", id);
            throw new AuthException.UserNotFound();
        }

        // 获取当前登录用户ID（如果有）
        Long currentUserId = getCurrentUserId();
        
        // 获取作者统计信息
        long articlesCount = articleMapper.selectCount(new LambdaQueryWrapper<Article>()
                .eq(Article::getAuthorId, id)
                .eq(Article::getStatus, 1));
        
        long followersCount = userFollowMapper.selectCount(new LambdaQueryWrapper<UserFollow>()
                .eq(UserFollow::getFollowingId, id));
                
        long likesCount = userFavoriteMapper.selectCount(new LambdaQueryWrapper<UserFavorite>()
                .eq(UserFavorite::getUserId, id));
        
        // 检查当前用户是否关注了作者
        boolean isFollowing = false;
        if (currentUserId != null) {
            isFollowing = userFollowMapper.isFollowing(currentUserId, id) > 0;
        }

        return AuthorInfoDTO.builder()
                .id(user.getId())
                .username(user.getUsername())
                .avatar(user.getAvatar())
                .bio(user.getBio() != null ? user.getBio() : "这个作者很懒，还没有写简介")
                .stats(AuthorInfoDTO.AuthorStatsDTO.builder()
                        .articles((int) articlesCount)
                        .followers((int) followersCount)
                        .likes((int) likesCount)
                        .build())
                .build();
    }

    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() &&
                !(authentication instanceof AnonymousAuthenticationToken)) {
            try {
                String username = authentication.getName();
                // 通过用户名查询用户ID
                User user = userMapper.selectOne(
                        new LambdaQueryWrapper<User>()
                                .eq(User::getUsername, username)
                );
                if (user != null) {
                    return user.getId();
                }
            } catch (Exception e) {
                log.warn("Failed to get user id for username: {}", authentication.getName(), e);
            }
            log.warn("Unable to extract userId from authentication");
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean followUser(Long followerId, Long followingId) {
        // 检查是否已经关注
        if (userFollowMapper.isFollowing(followerId, followingId) > 0) {
            return true;
        }

        UserFollow follow = new UserFollow();
        follow.setFollowerId(followerId);
        follow.setFollowingId(followingId);
        return userFollowMapper.insert(follow) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unfollowUser(Long followerId, Long followingId) {
        LambdaQueryWrapper<UserFollow> wrapper = new LambdaQueryWrapper<UserFollow>()
                .eq(UserFollow::getFollowerId, followerId)
                .eq(UserFollow::getFollowingId, followingId);
        return userFollowMapper.delete(wrapper) > 0;
    }

    @Override
    public boolean isFollowing(Long followerId, Long followingId) {
        return userFollowMapper.isFollowing(followerId, followingId) > 0;
    }

    @Override
    public UserSettingsDTO getUserSettings(Long userId) {
        log.debug("Getting user settings for user: {}", userId);
        
        UserSettings settings = userSettingsMapper.selectByUserId(userId);
        if (settings == null) {
            // 如果没有设置记录，创建默认设置
            settings = new UserSettings();
            settings.setUserId(userId);
            settings.setTheme("light");
            settings.setNotificationEnabled(true);
            settings.setLanguage("zh_CN");
            userSettingsMapper.insert(settings);
        }

        UserSettingsDTO dto = new UserSettingsDTO();
        BeanUtils.copyProperties(settings, dto);
        return dto;
    }

    @Override
    @Transactional
    public void updateUserSettings(Long userId, UserSettingsDTO settings) {
        log.debug("Updating user settings for user: {}", userId);
        
        UserSettings userSettings = userSettingsMapper.selectByUserId(userId);
        if (userSettings == null) {
            userSettings = new UserSettings();
            userSettings.setUserId(userId);
        }

        BeanUtils.copyProperties(settings, userSettings);
        if (userSettings.getUserId() == null) {
            userSettingsMapper.insert(userSettings);
        } else {
            userSettingsMapper.updateById(userSettings);
        }
    }

    @Override
    @Transactional
    public void updateUserProfile(Long userId, UserProfileDTO profile) {
        log.debug("Updating user profile for user: {}", userId);
        
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 检查用户名是否已被使用
        if (!user.getUsername().equals(profile.getUsername())) {
            User existingUser = userMapper.findByUsername(profile.getUsername());
            if (existingUser != null) {
                throw new BusinessException("用户名已被使用");
            }
        }

        // 检查邮箱是否已被使用
        if (!user.getEmail().equals(profile.getEmail())) {
            User existingUser = userMapper.findByEmail(profile.getEmail());
            if (existingUser != null) {
                throw new BusinessException("邮箱已被使用");
            }
        }

        // 更新用户信息
        user.setUsername(profile.getUsername());
        user.setEmail(profile.getEmail());
        user.setBio(profile.getBio());
        updateById(user);

        // 更新用户设置
        UserSettings settings = userSettingsMapper.selectByUserId(userId);
        if (settings == null) {
            settings = new UserSettings();
            settings.setUserId(userId);
            settings.setTheme(profile.getTheme());
            settings.setNotificationEnabled(profile.getNotificationEnabled());
            settings.setLanguage(profile.getLanguage());
            userSettingsMapper.insertSettings(settings);
        } else {
            settings.setTheme(profile.getTheme());
            settings.setNotificationEnabled(profile.getNotificationEnabled());
            settings.setLanguage(profile.getLanguage());
            userSettingsMapper.updateSettings(settings);
        }
    }

    @Override
    @Transactional
    public String updateUserAvatar(Long userId, MultipartFile avatar) {
        log.debug("Updating avatar for user: {}", userId);
        
        // 验证文件
        fileUtils.validateFile(avatar);

        try {
            // 保存文件
            String avatarUrl = fileUtils.saveFile(avatar, "avatar");

            // 更新用户头像
            User user = getById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }
            user.setAvatar(avatarUrl);
            updateById(user);

            return avatarUrl;
        } catch (IOException e) {
            log.error("Failed to save avatar", e);
            throw new BusinessException("头像上传失败");
        }
    }

    @Override
    @Transactional
    public void updatePassword(Long userId, String oldPassword, String newPassword) {
        log.debug("Updating password for user: {}", userId);
        
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new BusinessException("原密码错误");
        }

        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        updateById(user);
    }

    @Override
    public int getArticleCount(Long userId) {
        log.debug("Getting article count for user: {}", userId);
        long count = articleMapper.selectCount(new LambdaQueryWrapper<Article>()
                .eq(Article::getAuthorId, userId)
                .eq(Article::getStatus, 1)); // 只统计已发布的文章
        return (int) count;
    }

    @Override
    public int getCommentCount(Long userId) {
        log.debug("Getting comment count for user: {}", userId);
        long count = commentMapper.selectCount(new LambdaQueryWrapper<Comment>()
                .eq(Comment::getUserId, userId)
                .eq(Comment::getStatus, 1)); // 只统计已通过审核的评论
        return (int) count;
    }

    @Override
    public int getFavoriteCount(Long userId) {
        log.debug("Getting favorite count for user: {}", userId);
        long count = userFavoriteMapper.selectCount(new LambdaQueryWrapper<UserFavorite>()
                .eq(UserFavorite::getUserId, userId));
        return (int) count;
    }

    @Override
    public PageResult<AdminUserVO> getAdminUserList(AdminUserQuery query) {
        log.debug("Getting admin user list: {}", query);
        
        // 构建查询条件
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<User>()
            .like(StringUtils.isNotBlank(query.getUsername()), User::getUsername, query.getUsername())
            .like(StringUtils.isNotBlank(query.getEmail()), User::getEmail, query.getEmail())
            .eq(StringUtils.isNotBlank(query.getRole()), User::getRole, query.getRole())
            .eq(query.getStatus() != null, User::getStatus, query.getStatus())
            .orderByDesc(User::getCreatedAt);

        // 执行分页查询
        Page<User> page = new Page<>(query.getCurrent(), query.getSize());
        Page<User> userPage = userMapper.selectPage(page, wrapper);

        // 转换为VO
        List<AdminUserVO> voList = userPage.getRecords().stream()
            .map(this::convertToAdminVO)
            .collect(Collectors.toList());

        // 构建分页结果
        PageResult<AdminUserVO> result = new PageResult<>();
        result.setRecords(voList);
        result.setTotal(userPage.getTotal());
        result.setCurrent(query.getCurrent());
        result.setSize(query.getSize());
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserStatus(Long id, Integer status) {
        log.debug("Updating user status {}: {}", id, status);
        
        User user = getById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 不允许修改超级管理员状态
        if ("ADMIN".equals(user.getRole())) {
            throw new BusinessException("不能修改超级管理员状态");
        }

        user.setStatus(status);
        updateById(user);
    }

    @Override
    public void exportUsers(AdminUserQuery query, HttpServletResponse response) {
        log.debug("Starting user export process with query: {}", query);
        
        try {
            // 构建查询条件
            LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<User>()
                .like(StringUtils.isNotBlank(query.getUsername()), User::getUsername, query.getUsername())
                .like(StringUtils.isNotBlank(query.getEmail()), User::getEmail, query.getEmail())
                .eq(StringUtils.isNotBlank(query.getRole()), User::getRole, query.getRole())
                .eq(query.getStatus() != null, User::getStatus, query.getStatus())
                .orderByDesc(User::getCreatedAt);

            log.debug("Querying users for export...");
            List<User> users = list(wrapper);
            log.debug("Found {} users to export", users.size());

            // 准备导出数据
            List<AdminUserVO> exportList = users.stream()
                .map(this::convertToAdminVO)
                .collect(Collectors.toList());
            log.debug("Converted {} users to export format", exportList.size());

            log.debug("First user data: {}", exportList.isEmpty() ? "空数据" : exportList.get(0));
            log.debug("Export data size: {}", exportList.size());
            log.debug("Excel header class: {}", AdminUserVO.class.getName());

            // 使用EasyExcel导出
            log.debug("Starting Excel export process...");
            ExcelUtils.export(response, "用户列表", AdminUserVO.class, exportList);
            log.debug("Excel export completed successfully");
            
        } catch (Exception e) {
            log.error("Failed to export users. Error: {}", e.getMessage(), e);
            throw new RuntimeException("导出用户数据失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserInfo(Long id, UpdateUserDTO dto) {
        log.debug("Updating user info {}: {}", id, dto);
        
        User user = getById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 不允许修改超级管理员角色
        if ("ADMIN".equals(user.getRole()) && !"ADMIN".equals(dto.getRole())) {
            throw new BusinessException("不能修改超级管理员角色");
        }

        // 检查用户名是否已被使用
        if (!user.getUsername().equals(dto.getUsername())) {
            User existingUser = userMapper.findByUsername(dto.getUsername());
            if (existingUser != null) {
                throw new BusinessException("用户名已被使用");
            }
        }

        // 检查邮箱是否已被使用
        if (!user.getEmail().equals(dto.getEmail())) {
            User existingUser = userMapper.findByEmail(dto.getEmail());
            if (existingUser != null) {
                throw new BusinessException("邮箱已被使用");
            }
        }

        // 更新用户信息
        user.setUsername(dto.getUsername());
        user.setEmail(dto.getEmail());
        user.setRole(dto.getRole());
        if (dto.getStatus() != null) {
            user.setStatus(dto.getStatus());
        }

        updateById(user);
        log.info("User {} updated successfully", id);
    }

    @Override
    public UserHomeDTO getUserHomeInfo(Long userId) {
        // 获取用户基本信息
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 获取文章数量
        long articleCount = articleMapper.selectCount(new LambdaQueryWrapper<Article>()
                .eq(Article::getAuthorId, userId)
                .eq(Article::getStatus, ArticleStatus.PUBLISHED.getCode()));

        // 获取关注和粉丝数量
        long followingCount = userFollowMapper.selectCount(new LambdaQueryWrapper<UserFollow>()
                .eq(UserFollow::getFollowerId, userId));
        long followerCount = userFollowMapper.selectCount(new LambdaQueryWrapper<UserFollow>()
                .eq(UserFollow::getFollowingId, userId));

        // 构建统计信息
        UserHomeDTO.Stats stats = UserHomeDTO.Stats.builder()
                .articles((int) articleCount)
                .following((int) followingCount)
                .followers((int) followerCount)
                .build();

        // 构建并返回用户主页信息
        return UserHomeDTO.builder()
                .id(user.getId())
                .username(user.getUsername())
                .avatar(user.getAvatar())
                .bio(user.getBio())
                .stats(stats)
                .build();
    }

    private AdminUserVO convertToAdminVO(User user) {
        AdminUserVO vo = new AdminUserVO();
        BeanUtils.copyProperties(user, vo);
        return vo;
    }
} 