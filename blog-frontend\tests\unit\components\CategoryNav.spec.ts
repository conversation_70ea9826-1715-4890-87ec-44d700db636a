/// <reference types="vitest" />
import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import CategoryNav from '../../../src/components/article/CategoryNav.vue'

// 测试数据
const testCategories = [
  { id: 1, name: '全部', count: 120 },
  { id: 2, name: '技术', count: 45 },
  { id: 3, name: '生活', count: 25 },
  { id: 4, name: '随想', count: 10 }
]

describe('CategoryNav组件', () => {
  // 测试组件是否正确渲染分类列表
  it('正确渲染分类列表', () => {
    const wrapper = mount(CategoryNav, {
      props: {
        categories: testCategories,
        modelValue: null
      }
    })

    // 检查渲染的分类按钮数量是否正确
    const buttons = wrapper.findAll('button')
    expect(buttons.length).toBe(testCategories.length)
    
    // 检查每个分类名称是否正确显示
    testCategories.forEach((category, index) => {
      expect(buttons[index].text()).toContain(category.name)
      expect(buttons[index].text()).toContain(category.count?.toString())
    })
  })

  // 测试点击分类时是否正确发出update:modelValue事件
  it('点击分类时正确发出update:modelValue事件', async () => {
    const wrapper = mount(CategoryNav, {
      props: {
        categories: testCategories,
        modelValue: null
      }
    })

    // 点击第二个分类
    await wrapper.findAll('button')[1].trigger('click')
    
    // 验证是否发出了正确的事件
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
    expect(wrapper.emitted('update:modelValue')![0]).toEqual([2])
  })

  // 测试当前选中的分类是否具有正确的样式
  it('当前选中的分类具有正确的样式', () => {
    const selectedCategoryId = 2
    const wrapper = mount(CategoryNav, {
      props: {
        categories: testCategories,
        modelValue: selectedCategoryId
      }
    })

    // 获取所有按钮
    const buttons = wrapper.findAll('button')
    
    // 检查选中的按钮是否有正确的类
    expect(buttons[1].classes()).toContain('bg-blue-600')
    expect(buttons[1].classes()).toContain('text-white')
    
    // 检查其他按钮是否没有选中样式
    expect(buttons[0].classes()).toContain('bg-gray-50')
    expect(buttons[2].classes()).toContain('bg-gray-50')
  })

  // 测试点击已选中的分类不会发出事件
  it('点击已选中的分类不会发出事件', async () => {
    const selectedCategoryId = 2
    const wrapper = mount(CategoryNav, {
      props: {
        categories: testCategories,
        modelValue: selectedCategoryId
      }
    })

    // 点击已选中的分类
    await wrapper.findAll('button')[1].trigger('click')
    
    // 验证没有发出事件
    expect(wrapper.emitted('update:modelValue')).toBeFalsy()
  })
}) 