package com.modelbolt.blog.model.dto.tag;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Schema(description = "创建标签请求")
public class CreateTagDTO {
    @NotBlank(message = "标签名称不能为空")
    @Size(min = 2, max = 50, message = "标签名称长度必须在2-50个字符之间")
    @Schema(description = "标签名称")
    private String name;
} 