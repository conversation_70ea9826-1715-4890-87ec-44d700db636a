<template>
  <div class="bg-white rounded-lg shadow-sm p-6">
    <!-- 筛选区域 -->
    <div class="flex justify-between items-center mb-6">
      <div class="flex items-center space-x-4">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索评论内容"
          class="w-64"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <!-- 状态筛选 -->
        <el-select 
          v-model="currentStatus" 
          placeholder="评论状态" 
          class="w-32"
          @change="handleSearch"
        >
          <el-option label="全部" value="" />
          <el-option
            v-for="[status, text] in Object.entries(USER_COMMENT_STATUS_TEXT)"
            :key="status"
            :label="text"
            :value="status"
          />
        </el-select>
      </div>
    </div>

    <!-- 评论列表 -->
    <div v-loading="loading" class="space-y-6">
      <div 
        v-for="comment in comments" 
        :key="comment.id" 
        class="border-b border-gray-100 pb-6 last:border-0"
      >
        <div class="flex justify-between items-start mb-2">
          <div class="flex items-center space-x-2">
            <router-link 
              :to="'/articles/' + comment.articleId" 
              class="text-blue-600 hover:text-blue-700"
            >
              {{ comment.articleTitle }}
            </router-link>
            <!-- 状态标签 -->
            <el-tag 
              :type="USER_COMMENT_STATUS_TAG_TYPE[comment.status]" 
              size="small"
            >
              {{ USER_COMMENT_STATUS_TEXT[comment.status] }}
            </el-tag>
          </div>
          <span class="text-sm text-gray-500">{{ formatDate(comment.createdAt) }}</span>
        </div>
        <p class="text-gray-700 mb-2">{{ comment.content }}</p>
        <div class="flex justify-end">
          <el-button 
            type="danger" 
            link 
            size="small" 
            @click="handleDelete(comment)"
          >
            删除
          </el-button>
        </div>
      </div>

      <!-- 无数据展示 -->
      <el-empty 
        v-if="!loading && comments.length === 0" 
        description="暂无评论数据" 
      />
    </div>

    <!-- 分页 -->
    <div class="flex justify-center mt-6">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 30]"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDate } from '@/utils/date'
import { getUserComments, deleteUserComment } from '@/api/userComment'
import { USER_COMMENT_STATUS, USER_COMMENT_STATUS_TEXT, USER_COMMENT_STATUS_TAG_TYPE } from '@/types/userComment'
import type { UserCommentVO } from '@/types/userComment'

// 加载状态
const loading = ref(false)

// 搜索参数
const searchKeyword = ref('')
const currentStatus = ref('')

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 评论列表
const comments = ref<UserCommentVO[]>([])

// 加载评论列表
const loadComments = async () => {
  loading.value = true
  try {
    const { data } = await getUserComments({
      keyword: searchKeyword.value,
      status: currentStatus.value,
      page: currentPage.value,
      size: pageSize.value
    })
    comments.value = data.records
    total.value = data.total
  } catch (error) {
    console.error('加载评论列表失败:', error)
    ElMessage.error('加载评论列表失败')
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  loadComments()
}

// 处理删除
const handleDelete = async (comment: UserCommentVO) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条评论吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteUserComment(comment.id)
    ElMessage.success('删除成功')
    loadComments()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除评论失败:', error)
      ElMessage.error('删除评论失败')
    }
  }
}

// 处理分页
const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadComments()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadComments()
}

// 页面加载时获取数据
onMounted(() => {
  loadComments()
})
</script> 