package com.modelbolt.blog.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 访问历史记录实体
 */
@Data
@Accessors(chain = true)
@TableName("visit_history")
@Schema(description = "访问历史记录实体")
public class VisitHistory {
    @TableId(type = IdType.AUTO)
    @Schema(description = "ID")
    private Long id;

    @Schema(description = "访问日期")
    private LocalDate visitDate;

    @Schema(description = "访问次数")
    private Integer visitCount;

    @Schema(description = "文章ID")
    private Long articleId;

    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
} 