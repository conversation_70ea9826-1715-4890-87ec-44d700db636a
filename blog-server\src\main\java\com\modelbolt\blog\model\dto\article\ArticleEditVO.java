package com.modelbolt.blog.model.dto.article;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 文章编辑信息VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ArticleEditVO extends ArticleVO {
    /**
     * 分类ID列表
     */
    private List<Long> categoryIds;

    /**
     * 标签ID列表
     */
    private List<Long> tagIds;

    /**
     * 原始分类ID字符串
     */
    private String categoryIdsStr;

    /**
     * 原始标签ID字符串
     */
    private String tagIdsStr;
} 