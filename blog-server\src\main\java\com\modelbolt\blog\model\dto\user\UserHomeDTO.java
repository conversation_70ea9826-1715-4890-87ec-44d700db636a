package com.modelbolt.blog.model.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户主页信息")
public class UserHomeDTO {
    
    @Schema(description = "用户ID")
    private Long id;
    
    @Schema(description = "用户名")
    private String username;
    
    @Schema(description = "头像")
    private String avatar;
    
    @Schema(description = "个人简介")
    private String bio;
    
    @Schema(description = "统计信息")
    private Stats stats;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "统计信息")
    public static class Stats {
        
        @Schema(description = "文章数量")
        private Integer articles;
        
        @Schema(description = "关注数量")
        private Integer following;
        
        @Schema(description = "粉丝数量")
        private Integer followers;
    }
} 