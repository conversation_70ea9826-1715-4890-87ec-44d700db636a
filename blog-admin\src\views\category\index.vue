<script setup lang="ts">
import { ref, onMounted, computed, reactive, nextTick, watch } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import type { Category, CategoryFormState } from '@/types/category'
import { categoryApi } from '@/api/category'

// 状态定义
const loading = ref(false)
const categories = ref<Category[]>([])
const modalVisible = ref(false)
const modalTitle = ref('新增分类')
const formRef = ref()

// 表单状态
const formState = reactive<CategoryFormState>({
  name: '',
  parentId: undefined,
  orderNum: 0
})

// 表单校验规则
const rules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  orderNum: [
    { required: true, message: '请输入排序号', trigger: 'blur' },
    { type: 'number', message: '必须为数字值', trigger: 'blur' },
    { type: 'number', min: 1, message: '排序号必须大于0', trigger: 'blur' }
  ]
}

// 表格列定义
const columns = [
  {
    title: '分类名称',
    dataIndex: 'name',
    key: 'name',
    width: '40%'
  },
  {
    title: '排序',
    dataIndex: 'orderNum',
    key: 'orderNum',
    width: '30%'
  },
  {
    title: '操作',
    key: 'action',
    width: '30%'
  }
]

// 转换为树形结构
const categoryTree = computed(() => {
  console.log('Original category data:', categories.value)
  return categories.value
})

// 转换为下拉选择的树形数据
const categoryTreeData = computed(() => {
  // 获取所有子孙分类ID
  const getDescendantIds = (category: Category): number[] => {
    const ids: number[] = []
    if (category.children) {
      category.children.forEach(child => {
        ids.push(child.id)
        ids.push(...getDescendantIds(child))
      })
    }
    return ids
  }

  // 当前编辑的分类ID
  const currentId = formState.id

  const convert = (items: Category[]): any[] => {
    return items.map(item => {
      // 如果是当前编辑的分类或其子孙分类，则不能作为父级选项
      if (currentId) {
        // 获取当前分类的所有子孙ID
        const descendantIds = getDescendantIds(item)
        // 如果是当前分类本身或其子孙分类，则禁用该选项
        if (item.id === currentId || descendantIds.includes(currentId)) {
          return {
            title: `${item.name} (不可选)`,
            value: item.id,
            disabled: true,
            children: item.children ? convert(item.children) : undefined
          }
        }
      }
      return {
        title: item.name,
        value: item.id,
        children: item.children ? convert(item.children) : undefined
      }
    })
  }

  console.log('生成父级分类选项:', convert(categoryTree.value))
  return convert(categoryTree.value)
})

// 展开/收起所有
const expandedKeys = ref<number[]>([])
const onExpand = (expanded: boolean, record: Category) => {
  console.log('Expand status:', expanded, 'Record:', record)
  if (expanded) {
    expandedKeys.value = [...expandedKeys.value, record.id]
  } else {
    expandedKeys.value = expandedKeys.value.filter(key => key !== record.id)
  }
  console.log('Current expanded keys:', expandedKeys.value)
}

const expandAll = () => {
  const getAllIds = (categories: Category[]): number[] => {
    return categories.reduce((ids: number[], category) => {
      ids.push(category.id)
      if (category.children && category.children.length > 0) {
        ids.push(...getAllIds(category.children))
      }
      return ids
    }, [])
  }
  console.log('Categories for expand all:', categories.value)
  const allIds = getAllIds(categories.value)
  console.log('All category IDs:', allIds)
  expandedKeys.value = allIds
}

const collapseAll = () => {
  console.log('Collapsing all categories')
  expandedKeys.value = []
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    loading.value = true
    const { data } = await categoryApi.getTreeList()
    console.log('Category data from API:', data)
    categories.value = data
    // 重新展开之前展开的节点
    if (expandedKeys.value.length > 0) {
      const currentExpanded = [...expandedKeys.value]
      expandedKeys.value = []
      nextTick(() => {
        expandedKeys.value = currentExpanded
      })
    }
  } catch (error) {
    console.error('Error fetching categories:', error)
    message.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

// 新增分类
const handleAdd = () => {
  modalTitle.value = '新增分类'
  
  // 先重置表单
  formRef.value?.resetFields()
  
  // 设置初始值
  formState.id = undefined
  formState.name = ''
  formState.parentId = undefined
  formState.orderNum = 0
  
  // 显示模态框
  modalVisible.value = true
}

// 添加子分类
const handleAddSub = (record: Category) => {
  modalTitle.value = '新增子分类'
  
  // 先重置表单
  formRef.value?.resetFields()
  
  // 直接设置表单数据
  formState.id = undefined
  formState.name = ''
  formState.parentId = record.id
  formState.orderNum = 0
  
  // 显示模态框
  modalVisible.value = true
}

// 编辑分类
const handleEdit = (record: Category) => {
    console.log('=== 开始编辑分类 ===')
    console.log('原始数据:', record)
    
    modalTitle.value = '编辑分类'
    
    // 使用一个临时对象来保存要设置的值
    const newFormState = {
        id: record.id,
        name: record.name,
        parentId: record.parentId,
        orderNum: record.orderNum
    }
    console.log('准备设置的新值:', newFormState)
    
    // 重置表单并设置新值
    formRef.value?.resetFields()
    
    // 使用 Object.assign 来一次性更新所有值
    Object.assign(formState, newFormState)
    
    console.log('设置后的表单状态:', { ...formState })
    
    // 显示模态框
    modalVisible.value = true
}

// 删除分类
const handleDelete = async (record: Category) => {
  console.log('Attempting to delete category:', record)
  try {
    const response = await categoryApi.delete(record.id)
    console.log('Delete response:', response)
    message.success('删除成功')
    await fetchCategories()
  } catch (error: any) {
    console.error('Delete category error:', error)
    message.error(error.response?.data?.message || '删除失败')
  }
}

// 取消编辑
const handleModalCancel = () => {
    console.log('=== 取消编辑 ===')
    console.log('取消前表单状态:', { ...formState })
    modalVisible.value = false
    formRef.value?.resetFields()
    console.log('取消后表单状态:', { ...formState })
}

// 添加 handleParentChange 方法
const handleParentChange = (value: number | null) => {
  formState.parentId = value;
  console.log('父级分类已更改:', value);
}

// 修改 handleModalOk 方法
const handleModalOk = async () => {
    console.log('=== 准备保存分类 ===')
    console.log('当前表单状态:', { ...formState })
    try {
        await formRef.value.validate()
        console.log('表单验证通过')
        
        // 检查是否选择了自己作为父级
        if (formState.id && formState.parentId === formState.id) {
            console.log('检测到自引用:', { id: formState.id, parentId: formState.parentId })
            message.error('不能选择自己作为父级分类')
            return
        }

        if (formState.id) {
            console.log('执行更新操作:', { ...formState })
            try {
                const updateData = {
                    name: formState.name,
                    parentId: formState.parentId === undefined ? null : formState.parentId,
                    orderNum: formState.orderNum
                }
                console.log('更新数据:', updateData)
                
                await categoryApi.update(formState.id, updateData)
                message.success('更新成功')
                modalVisible.value = false
                await fetchCategories()
            } catch (error: any) {
                console.error('更新分类失败:', error)
                console.error('错误详情:', error.response?.data)
                const errorMessage = error.response?.data?.message || '更新失败'
                message.error(errorMessage)
            }
        } else {
            console.log('执行创建操作:', { ...formState })
            try {
                const createData = {
                    name: formState.name,
                    parentId: formState.parentId === undefined ? null : formState.parentId,
                    orderNum: formState.orderNum
                }
                console.log('创建数据:', createData)
                
                await categoryApi.create(createData)
                message.success('创建成功')
                modalVisible.value = false
                await fetchCategories()
            } catch (error: any) {
                console.error('创建分类失败:', error)
                console.error('错误详情:', error.response?.data)
                const errorMessage = error.response?.data?.message || '创建失败'
                message.error(errorMessage)
            }
        }
    } catch (error) {
        console.error('表单验证失败:', error)
    }
}

// 监听表单状态变化
watch(() => ({ ...formState }), (newVal) => {
    console.log('表单状态发生变化:', newVal)
}, { deep: true })

// 监听模态框显示状态
watch(() => modalVisible.value, (newVal) => {
    console.log('模态框显示状态变化:', newVal)
})

// 初始化
onMounted(() => {
  fetchCategories()
})
</script>

<template>
  <div class="category-list">
    <div class="mb-4 flex justify-between">
      <a-button type="primary" @click="handleAdd">
        <template #icon><PlusOutlined /></template>
        新建分类
      </a-button>
      <a-space>
        <a-button @click="expandAll">展开全部</a-button>
        <a-button @click="collapseAll">收起全部</a-button>
      </a-space>
    </div>

    <a-table
      :columns="columns"
      :data-source="categoryTree"
      :loading="loading"
      :row-key="(record) => record.id"
      :expanded-row-keys="expandedKeys"
      @expand="onExpand"
      :pagination="false"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" @click="handleEdit(record)">编辑</a-button>
            <a-button type="link" @click="handleAddSub(record)">添加子分类</a-button>
            <a-popconfirm
              title="确定要删除此分类吗？"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" danger>删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <a-modal
      :open="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="分类名称" name="name">
          <a-input
            :value="formState.name"
            @update:value="val => formState.name = val"
            placeholder="请输入分类名称"
          />
        </a-form-item>
        <a-form-item label="父级分类" name="parentId">
          <a-tree-select
            :value="formState.parentId"
            @update:value="val => formState.parentId = val"
            :tree-data="categoryTreeData"
            placeholder="请选择父级分类"
            allow-clear
            tree-default-expand-all
            :disabled="modalTitle === '新增子分类'"
          />
        </a-form-item>
        <a-form-item label="排序" name="orderNum">
          <a-input-number
            :value="formState.orderNum"
            @update:value="val => formState.orderNum = val"
            :min="1"
            placeholder="请输入排序号"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<style scoped>
.category-list {
  padding: 24px;
  background: #fff;
}
</style>