server:
  port: 8080
  servlet:
    context-path: /api

# 应用配置
app:
  base-url: http://localhost:8080/api  # 应用基础URL，用于生成海报等
  system:
    # 默认系统配置
    defaults:
      site-name: ModelBolt Blog
      site-description: 集成AI能力的现代化博客系统
      site-keywords: blog,AI,knowledge sharing
      smtp-port: 587
#      ai-model: deepseek-r1  # 腾讯云大模型名称
      ai-model: deepseek-v3  # 腾讯云大模型名称
      ai-api-key: ${DEEPSEEK_API_KEY:sk-kUfzhaVo3cXgdx5D5nhdWI4bDcVu7MN47TG99mWOENvL39xf}  # 从环境变量获取，默认为你的key
      ai-api-endpoint: https://api.lkeap.cloud.tencent.com/v1/chat/completions  # 腾讯云大模型API地址
      ai-max-tokens: 2000
      ai-temperature: 0.7
      ai-request-limit: 1000
      ai-token-limit: 100000
      ai-retry-count: 3  # API调用重试次数
      ai-timeout: 30000  # API调用超时时间(毫秒)
      ai-cache-enabled: true  # 是否启用AI响应缓存
      ai-cache-ttl: 3600  # 缓存过期时间(秒)
    # 安全配置
    security:
      login-attempts: 5
      lock-duration: 30

spring:
  application:
    name: blog-server
  
  # 静态资源配置
  web:
    resources:
      static-locations:
        - classpath:/static/  # 类路径下的静态资源
        - file:${file.upload.path} # 文件系统的上传目录
      static-path-pattern: /images/**  # 统一静态资源访问前缀
      cache:
        period: 86400  # 24小时缓存
  
  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 15MB
  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************
    username: root
    password: root
  
  # Redis 配置
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      timeout: 10s
      clear-on-start: true  # 启动时清空Redis数据
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0
  
  mail:
    host: smtp.qq.com
    port: 465
    username: <EMAIL>
    password: vhtqoqqmgxrwebhb
    properties:
      mail:
        smtp:
          auth: true
          ssl:
            enable: true
            
  # SQL 初始化配置
  sql:
    init:
      mode: never
      schema-locations: classpath:db/schema.sql
      encoding: UTF-8

# JWT 配置
jwt:
  secret: a8b6f9c7e0a1d8f3b6e3c3e7f7a0e8e2b9c1d8a7c7e9f5a1e2c5f6a8d9c0e1f3a4b6c8f9e1d0c7a8b6d3c5f1a0e9f7e6d8c1a9b7e2d3f5b9c0a8e8f7d3e5c7a8b6e9f1c0d3f5a2e5d7b8c1f2e6d4a8e9b3c7f2c6a1d9e4b0d8f7c3a5e2f1b6c0a8d1e3f9b4e6c7a5f3d2e3f9b1c0a1e6d8b9c2f4a3d5e8f0d7b4c2e1a9d3f6c5b7a0e3d8f4c2a1b6e9d5f7a8d0e3c1b4f6a2e8d9c3f5e2a8b7d0e1c6f9a5b4d3e8c1a7d6e2f3f9e5b0c8a1d6b3e7c2a9d4f1b8e7f3a5c6d0e1
  expiration: 60  # 30分钟
  remember-me-expiration: 604800  # 7天


# MyBatis Plus 配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.modelbolt.blog.entity
  type-handlers-package: com.baomidou.mybatisplus.extension.handlers
  configuration:
    map-underscore-to-camel-case: true
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# Knife4j配置
knife4j:
  enable: true
  setting:
    language: zh-CN 

springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  show-actuator: false
  packages-to-scan: com.modelbolt.blog.controller 

# 日志配置
logging:
  level:
    root: info
    com.modelbolt.blog: debug
    org.springframework.security: DEBUG
  file:
    name: logs/blog-server.log 

# 自定义文件上传配置
file:
  upload:
    path: D:/cursorProject/ModelBolt/blog-server/src/main/resources/static/images/
    allowed-types: jpg,jpeg,png,gif  # 允许的文件类型
    max-size: 10485760  # 最大文件大小（10MB）
    url-prefix: /images  # 统一URL前缀
    subdirs:  # 子目录配置
      avatar: avatar  # 头像存储目录
      qrcode: qrcode  # 收款码存储目录
      article: article  # 文章图片存储目录 