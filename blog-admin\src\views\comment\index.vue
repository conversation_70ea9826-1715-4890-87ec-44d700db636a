<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { commentApi } from '@/api/comment'
import type { CommentVO, CommentQuery } from '@/types/comment'
import { CommentStatus } from '@/types/comment'

const loading = ref(false)
const comments = ref<CommentVO[]>([])
const selectedRowKeys = ref<number[]>([])
const query = ref<CommentQuery>({
  page: 1,
  size: 10,
  status: undefined,
  keyword: undefined
})

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true
})

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
  },
  {
    title: '评论内容',
    dataIndex: 'content',
    ellipsis: true,
  },
  {
    title: '文章标题',
    dataIndex: 'articleTitle',
    ellipsis: true,
  },
  {
    title: '评论用户',
    dataIndex: 'userName',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
  },
  {
    title: '点赞数',
    dataIndex: 'likeCount',
    width: 80,
  },
  {
    title: '回复数',
    dataIndex: 'replyCount',
    width: 80,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 180,
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  },
]

const fetchComments = async () => {
  try {
    loading.value = true
    const { data } = await commentApi.getList({
      ...query.value,
      page: pagination.value.current,
      size: pagination.value.pageSize
    })
    comments.value = data.records
    pagination.value.total = data.total
  } catch (error) {
    console.error('获取评论列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.value.current = pag.current
  pagination.value.pageSize = pag.pageSize
  fetchComments()
}

const handleSearch = (value: string) => {
  query.value.keyword = value
  pagination.value.current = 1
  fetchComments()
}

const handleStatusChange = (value: number | undefined) => {
  query.value.status = value
  pagination.value.current = 1
  fetchComments()
}

const handleAudit = async (record: CommentVO, status: number) => {
  try {
    await commentApi.audit(record.id, { status })
    message.success('审核成功')
    fetchComments()
  } catch (error) {
    console.error('审核评论失败:', error)
  }
}

const handleDelete = (record: CommentVO) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这条评论吗？',
    okText: '确定',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      try {
        await commentApi.delete(record.id)
        message.success('删除成功')
        fetchComments()
      } catch (error) {
        console.error('删除评论失败:', error)
      }
    },
  })
}

const handleBatchDelete = () => {
  if (!selectedRowKeys.value.length) {
    message.warning('请选择要删除的评论')
    return
  }

  Modal.confirm({
    title: '确认删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 条评论吗？`,
    okText: '确定',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      try {
        await commentApi.batchDelete(selectedRowKeys.value)
        message.success('删除成功')
        selectedRowKeys.value = []
        fetchComments()
      } catch (error) {
        console.error('批量删除评论失败:', error)
      }
    },
  })
}

const rowSelection = {
  selectedRowKeys,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys
  }
}

onMounted(() => {
  fetchComments()
})
</script>

<template>
  <div class="comment-list">
    <div class="mb-4 flex justify-between">
      <div class="flex items-center space-x-4">
        <a-input-search
          v-model="query.keyword"
          placeholder="搜索评论内容或文章标题"
          style="width: 300px"
          @search="handleSearch"
        />
        <a-select
          v-model="query.status"
          style="width: 120px"
          placeholder="评论状态"
          allowClear
          @change="handleStatusChange"
        >
          <a-select-option :value="CommentStatus.PENDING">待审核</a-select-option>
          <a-select-option :value="CommentStatus.APPROVED">已通过</a-select-option>
          <a-select-option :value="CommentStatus.REJECTED">已拒绝</a-select-option>
        </a-select>
      </div>
      <a-button
        v-if="selectedRowKeys.length"
        type="primary"
        danger
        @click="handleBatchDelete"
      >
        批量删除
      </a-button>
    </div>

    <a-table
      :columns="columns"
      :data-source="comments"
      :pagination="pagination"
      :loading="loading"
      :row-selection="rowSelection"
      @change="handleTableChange"
      row-key="id"
      :scroll="{ x: '1200px' }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="record.status === CommentStatus.APPROVED ? 'success' : record.status === CommentStatus.PENDING ? 'warning' : 'error'">
            {{ record.status === CommentStatus.APPROVED ? '已通过' : record.status === CommentStatus.PENDING ? '待审核' : '已拒绝' }}
          </a-tag>
        </template>

        <template v-else-if="column.key === 'action'">
          <a-space>
            <template v-if="record.status === CommentStatus.PENDING">
              <a-button type="link" @click="handleAudit(record, CommentStatus.APPROVED)">通过</a-button>
              <a-button type="link" danger @click="handleAudit(record, CommentStatus.REJECTED)">拒绝</a-button>
            </template>
            <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<style scoped>
.comment-list {
  padding: 24px;
  background: #fff;
  border-radius: 2px;
}
</style> 