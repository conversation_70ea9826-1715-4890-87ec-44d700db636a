package com.modelbolt.blog.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 分享记录实体
 */
@Data
@TableName("share_record")
public class ShareRecord {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long articleId;
    
    private Long userId;
    
    private String platform;
    
    private String ipAddress;
    
    private String userAgent;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
} 