package com.modelbolt.blog.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Schema(description = "操作日志导出视图对象")
public class OperationLogExportVO {
    @ExcelProperty("操作时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @Schema(description = "操作时间")
    private LocalDateTime createdAt;

    @ExcelProperty("操作用户")
    @Schema(description = "操作用户")
    private String username;

    @ExcelProperty("操作类型")
    @Schema(description = "操作类型")
    private String operation;

    @ExcelProperty("请求方法")
    @Schema(description = "请求方法")
    private String method;

    @ExcelProperty("请求参数")
    @Schema(description = "请求参数")
    private String params;

    @ExcelProperty("IP地址")
    @Schema(description = "IP地址")
    private String ip;
} 