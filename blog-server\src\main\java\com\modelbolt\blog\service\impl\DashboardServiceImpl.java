package com.modelbolt.blog.service.impl;

import com.modelbolt.blog.mapper.*;
import com.modelbolt.blog.service.DashboardService;
import com.modelbolt.blog.service.UserOnlineService;
import com.modelbolt.blog.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.logging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.jdbc.core.JdbcTemplate;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 仪表盘服务实现类，用于提供仪表盘相关统计数据
 */
@Slf4j
@Service
public class DashboardServiceImpl implements DashboardService {
    @Autowired
    private ArticleMapper articleMapper;

    @Autowired
    private CommentMapper commentMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private CategoryMapper categoryMapper;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private UserOnlineService userOnlineService;

    /**
     * 获取仪表盘统计数据，包括文章数、评论数、用户数、总浏览量、最近7天的访问统计、用户增长、内容分类统计以及实时数据
     */
    @Override
    public Map<String, Object> getDashboardStats() {
        log.info("开始获取仪表盘统计数据");
        Map<String, Object> stats = new HashMap<>();

        try {
            // 基础统计数据
            int articleCount = articleMapper.count();
            int commentCount = commentMapper.count();
            int userCount = userMapper.count();
            int viewCount = articleMapper.sumViewCount();

            log.info("基础统计数据 - 文章数: {}, 评论数: {}, 用户数: {}, 总浏览量: {}", 
                     articleCount, commentCount, userCount, viewCount);

            stats.put("articleCount", articleCount);
            stats.put("commentCount", commentCount);
            stats.put("userCount", userCount);
            stats.put("viewCount", viewCount);

            // 访问统计（最近7天）
            List<Map<String, Object>> visitStats = getVisitStats();
            log.info("访问统计数据: {}", visitStats);
            stats.put("visitStats", visitStats);

            // 用户增长（最近7天）
            List<Map<String, Object>> userGrowth = getUserGrowth();
            log.info("用户增长数据: {}", userGrowth);
            stats.put("userGrowth", userGrowth);

            // 内容分类统计
            List<Map<String, Object>> contentStats = getContentStats();
            log.info("内容分类统计: {}", contentStats);
            stats.put("contentStats", contentStats);

            // 实时数据
            int onlineUsers = userOnlineService.getOnlineUsersCount();
            int todayVisits = getTodayVisits();
            int todayComments = getTodayComments();
            String systemLoad = getSystemLoad();

            log.info("实时数据 - 在线用户: {}, 今日访问: {}, 今日评论: {}, 系统负载: {}", 
                     onlineUsers, todayVisits, todayComments, systemLoad);

            stats.put("onlineUsers", onlineUsers);
            stats.put("todayVisits", todayVisits);
            stats.put("todayComments", todayComments);
            stats.put("systemLoad", systemLoad);

            log.info("仪表盘统计数据获取完成: {}", stats);
        } catch (Exception e) {
            log.error("获取仪表盘统计数据失败", e);
            // 返回默认值而不是抛出异常
            stats.put("articleCount", 0);
            stats.put("commentCount", 0);
            stats.put("userCount", 0);
            stats.put("viewCount", 0);
            stats.put("visitStats", new ArrayList<>());
            stats.put("userGrowth", new ArrayList<>());
            stats.put("contentStats", new ArrayList<>());
            stats.put("onlineUsers", 0);
            stats.put("todayVisits", 0);
            stats.put("todayComments", 0);
            stats.put("systemLoad", "0%");
        }

        return stats;
    }

    /**
     * 获取最近7天的访问统计，包括历史访问记录和今天的实时访问量
     */
    private List<Map<String, Object>> getVisitStats() {
        log.debug("Fetching visit statistics for the last 7 days");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");
        List<Map<String, Object>> stats = new ArrayList<>();

        try {
            // 获取最近6天的历史访问记录（不包括今天）
            List<Map<String, Object>> historyStats = jdbcTemplate.queryForList(
                "SELECT visit_date, visit_count FROM visit_history " +
                "WHERE visit_date >= DATE_SUB(CURDATE(), INTERVAL 6 DAY) " +
                "AND visit_date < CURDATE() " +  // 添加这行，排除今天
                "ORDER BY visit_date DESC"
            );

            // 获取今天的实时访问量
            String todayKey = "stats:today_visits";
            Object todayCount = redisUtils.get(todayKey);
            int todayVisits = todayCount == null ? 0 : Integer.parseInt(todayCount.toString());
            log.debug("Today's real-time visits: {}", todayVisits);

            // 将数据库结果转换为所需格式
            Map<LocalDate, Integer> dateCountMap = new HashMap<>();
            historyStats.forEach(row -> {
                LocalDate date = ((java.sql.Date) row.get("visit_date")).toLocalDate();
                Integer count = ((Number) row.get("visit_count")).intValue();
                dateCountMap.put(date, count);
            });

            // 填充最近7天的数据，今天使用Redis中的实时数据
            for (int i = 0; i < 7; i++) {
                LocalDate date = LocalDate.now().minusDays(i);
                Map<String, Object> item = new HashMap<>();
                item.put("date", date.format(formatter));
                // 如果是今天，使用Redis中的实时数据
                item.put("count", i == 0 ? todayVisits : dateCountMap.getOrDefault(date, 0));
                stats.add(item);
            }

            log.debug("Final visit statistics data: {}", stats);
        } catch (Exception e) {
            log.error("Error fetching visit statistics", e);
            // 发生错误时返回空数据
            for (int i = 0; i < 7; i++) {
                LocalDate date = LocalDate.now().minusDays(i);
                Map<String, Object> item = new HashMap<>();
                item.put("date", date.format(formatter));
                item.put("count", 0);
                stats.add(item);
            }
        }

        return stats;
    }

    /**
     * 获取最近7天的用户增长统计，包括每天新注册的用户数
     */
    private List<Map<String, Object>> getUserGrowth() {
        log.debug("Fetching user growth statistics for the last 7 days");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");
        List<Map<String, Object>> stats = new ArrayList<>();

        try {
            stats = IntStream.range(0, 7)
                .mapToObj(i -> {
                    LocalDate date = LocalDate.now().minusDays(i);
                    int count = userMapper.getNewUserCountByDate(date);
                    log.debug("New users for {}: {}", date.format(formatter), count);

                    Map<String, Object> item = new HashMap<>();
                    item.put("date", date.format(formatter));
                    item.put("count", count);
                    return item;
                })
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error fetching user growth statistics", e);
        }

        return stats;
    }

    /**
     * 获取内容分类统计，包括每个分类的文章数
     */
    private List<Map<String, Object>> getContentStats() {
        log.info("开始获取内容分类统计");
        List<Map<String, Object>> stats = new ArrayList<>();

        try {
            List<Map<String, Object>> rawStats = categoryMapper.getCategoryStats();
            
            if (rawStats != null && !rawStats.isEmpty()) {
                return rawStats;
            } else {
                Map<String, Object> defaultCategory = new HashMap<>();
                defaultCategory.put("type", "未分类");
                defaultCategory.put("count", 0);
                stats.add(defaultCategory);
            }
        } catch (Exception e) {
            log.error("获取内容分类统计失败", e);
            Map<String, Object> defaultCategory = new HashMap<>();
            defaultCategory.put("type", "未分类");
            defaultCategory.put("count", 0);
            stats.add(defaultCategory);
        }

        return stats;
    }

    /**
     * 获取今日的访问量，数据来源于Redis
     */
    @Override
    public int getTodayVisits() {
        try {
            String key = "stats:today_visits";
            Object count = redisUtils.get(key);
            return count == null ? 0 : Integer.parseInt(count.toString());
        } catch (Exception e) {
            log.error("Error getting today's visit count", e);
            return 0;
        }
    }

    /**
     * 获取今日的评论数，数据来源于数据库
     */
    @Override
    public int getTodayComments() {
        try {
            int comments = commentMapper.getCommentCountByDate(LocalDate.now());
            log.debug("Today's comment count: {}", comments);
            return comments;
        } catch (Exception e) {
            log.error("Error getting today's comment count", e);
            return 0;
        }
    }

    /**
     * 获取系统负载，包括CPU和内存的使用率，并计算综合负载
     */
    @Override
    public String getSystemLoad() {
        try {
            com.sun.management.OperatingSystemMXBean osBean = 
                (com.sun.management.OperatingSystemMXBean) java.lang.management.ManagementFactory.getOperatingSystemMXBean();
            
            // 获取CPU使用率
            double cpuLoad = osBean.getCpuLoad();
            if (cpuLoad < 0) {
                cpuLoad = osBean.getSystemCpuLoad();
            }
            
            // 获取内存使用率
            double totalMemory = osBean.getTotalMemorySize();
            double freeMemory = osBean.getFreeMemorySize();
            double memoryLoad = ((totalMemory - freeMemory) / totalMemory) * 100;
            
            // 综合负载（CPU和内存的平均值）
            double systemLoad = (cpuLoad * 100 + memoryLoad) / 2;
            
            log.debug("System load - CPU: {:.1f}%, Memory: {:.1f}%, Combined: {:.1f}%", 
                     cpuLoad * 100, memoryLoad, systemLoad);
            
            return String.format("%.1f%%", systemLoad);
        } catch (Exception e) {
            log.error("Error getting system load", e);
            return "0%";
        }
    }
} 