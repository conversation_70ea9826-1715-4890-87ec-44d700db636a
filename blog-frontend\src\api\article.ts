import {http} from '@/utils/request'
import type { ArticleVO, CategoryVO, TagVO, CommentVO } from '@/types/article'
import type { PageResult, R } from '@/types/common'

/**
 * 获取置顶文章列表
 */
export function getFeaturedArticles() {
  return http<R<ArticleVO[]>>({
    url: '/articles/featured',
    method: 'get'
  })
}

/**
 * 分页获取文章列表
 */
export function getArticles(params: {
  categoryId?: number
  sort?: 'newest' | 'hottest' | 'recommended'
  page: number
  size: number
}) {
  return http<R<PageResult<ArticleVO>>>({
    url: '/articles',
    method: 'get',
    params
  })
}

/**
 * 获取热门文章
 */
export function getHotArticles(limit: number = 5) {
  return http<R<ArticleVO[]>>({
    url: '/articles/hot',
    method: 'get',
    params: { limit }
  })
}

/**
 * 获取分类列表
 */
export function getCategories() {
  return http<R<CategoryVO[]>>({
    url: '/categories',
    method: 'get'
  })
}

/**
 * 获取热门标签
 */
export function getHotTags(limit: number = 20) {
  return http<R<TagVO[]>>({
    url: '/tags/hot',
    method: 'get',
    params: { limit }
  })
}

/**
 * 获取文章详情
 * @param id 文章ID
 */
export function getArticleDetail(id: number) {
  return http<R<ArticleVO>>({
    url: `/articles/${id}`,
    method: 'get'
  })
}

/**
 * 获取相关文章
 * @param id 文章ID
 * @param limit 获取数量
 */
export function getRelatedArticles(id: number, limit: number = 3) {
  return http<R<ArticleVO[]>>({
    url: `/articles/${id}/related`,
    method: 'get',
    params: { limit }
  })
}

/**
 * 获取文章评论
 * @param id 文章ID
 * @param params 分页参数
 */
export function getArticleComments(id: number, params?: { page?: number; size?: number }) {
  return http<R<PageResult<CommentVO>>>({
    url: `/articles/${id}/comments`,
    method: 'get',
    params
  })
}

/**
 * 更新文章浏览量
 * @param id 文章ID
 */
export function updateArticleViews(id: number) {
  return http<R<void>>({
    url: `/articles/${id}/views`,
    method: 'post'
  })
}

/**
 * 点赞文章
 * @param id 文章ID
 */
export function likeArticle(id: number) {
  return http<R<void>>({
    url: `/user/articles/${id}/like`,
    method: 'post'
  })
}

/**
 * 取消点赞
 * @param id 文章ID
 */
export function unlikeArticle(id: number) {
  return http<R<void>>({
    url: `/user/articles/${id}/like`,
    method: 'delete'
  })
}

/**
 * 收藏文章
 * @param id 文章ID
 */
export function favoriteArticle(id: number) {
  return http<R<void>>({
    url: `/user/articles/${id}/favorite`,
    method: 'post'
  })
}

/**
 * 取消收藏
 * @param id 文章ID
 */
export function unfavoriteArticle(id: number) {
  return http<R<void>>({
    url: `/user/articles/${id}/favorite`,
    method: 'delete'
  })
}

/**
 * 获取用户文章列表
 */
export function getUserArticles(params: {
  keyword?: string
  status?: string
  page: number
  size: number
}) {
  return http<R<PageResult<ArticleVO>>>({
    url: '/user/articles',
    method: 'get',
    params
  })
}

/**
 * 获取用户公开文章列表
 */
export function getUserPublicArticles(params: {
  userId: number
  page: number
  size: number
}) {
  return http<R<PageResult<ArticleVO>>>({
    url: `/articles/user/${params.userId}`,
    method: 'get',
    params: {
      page: params.page,
      size: params.size
    }
  })
}

/**
 * 创建文章
 */
export function createArticle(data: {
  title: string
  content: string
  summary?: string
  cover?: string
  categoryIds?: number[]
  tagIds?: number[]
  status: 'draft' | 'published'
}) {
  return http<R<number>>({
    url: '/user/articles',
    method: 'post',
    data
  })
}

/**
 * 更新文章
 */
export function updateArticle(id: number, data: {
  title: string
  content: string
  summary?: string
  cover?: string
  categoryIds?: number[]
  tagIds?: number[]
  status: 'draft' | 'published'
}) {
  return http<R<void>>({
    url: `/user/articles/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除文章
 */
export function deleteArticle(id: number) {
  return http<R<void>>({
    url: `/user/articles/${id}`,
    method: 'delete'
  })
}

/**
 * 上传文章封面
 */
export const uploadArticleCover = (file: File) => {
  const formData = new FormData()
  formData.append('cover', file)
  return http<R<string>>({
    url: '/user/articles/cover',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取所有标签
 */
export function getAllTags() {
  return http<R<TagVO[]>>({
    url: '/tags',
    method: 'get'
  })
}

/**
 * 获取文章编辑信息
 */
export const getArticleForEdit = (id: number) => {
  return http<R<ArticleVO>>(`/user/articles/${id}/edit`)
}

