<!-- 文章轮播组件 -->
<template>
  <div class="article-carousel">
    <el-carousel 
      ref="carousel"
      :interval="5000"
      height="400px"
      :initial-index="0"
      indicator-position="none"
      :autoplay="true"
      :loop="true"
      arrow="always"
      @change="handleSlideChange"
    >
      <el-carousel-item v-for="(article, index) in articles" :key="article.id">
        <div class="carousel-item" @click="handleArticleClick(article.id)">
          <!-- 文章封面 -->
          <div class="cover-wrapper">
            <el-image 
              :src="getCoverUrl(article.cover)" 
              fit="cover"
              class="cover-image"
              loading="lazy"
            >
              <template #error>
                <div class="image-placeholder">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
            <!-- 渐变遮罩 -->
            <div class="gradient-overlay"></div>
          </div>

          <!-- 文章内容 -->
          <div class="content-wrapper">
            <h2 class="title hover:text-blue-300 transition-colors duration-200">{{ article.title }}</h2>
            <p class="summary">{{ article.summary }}</p>
            <div class="meta-info">
              <span class="meta-item">
                <el-icon><User /></el-icon>
                {{ article.authorName }}
              </span>
              <span class="meta-item">
                <el-icon><View /></el-icon>
                {{ article.viewCount }}
              </span>
              <span class="meta-item">
                <el-icon><ChatDotRound /></el-icon>
                {{ article.commentCount }}
              </span>
              <span class="meta-item">
                <el-icon><Timer /></el-icon>
                {{ formatDate(article.createdAt) }}
              </span>
            </div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>

    <!-- 自定义指示器 -->
    <div class="custom-indicators">
      <span 
        v-for="(_, index) in articles" 
        :key="index"
        class="indicator"
        :class="{ active: currentIndex === index }"
        @click="handleIndicatorClick(index)"
      ></span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { User, View, ChatDotRound, Timer, Picture } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/date'
import { getCoverUrl } from '@/utils/image'
import type { ArticleVO } from '@/types/article'

defineProps<{
  articles: ArticleVO[]
}>()

const router = useRouter()
const carousel = ref()
const currentIndex = ref(0)

const handleSlideChange = (index: number) => {
  currentIndex.value = index
}

const handleIndicatorClick = (index: number) => {
  carousel.value?.setActiveItem(index)
  currentIndex.value = index
}

const handleArticleClick = (id: number) => {
  router.push(`/articles/${id}`)
}
</script>

<style scoped>
.article-carousel {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.carousel-item {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.cover-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.cover-image {
  width: 100%;
  height: 100%;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #909399;
}

.gradient-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
}

.content-wrapper {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32px;
  color: #fff;
  z-index: 1;
}

.title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 16px;
  line-height: 1.4;
}

.summary {
  font-size: 16px;
  margin-bottom: 16px;
  opacity: 0.9;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.meta-info {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.custom-indicators {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 2;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s;
}

.indicator.active {
  width: 16px;
  border-radius: 4px;
  background-color: #fff;
}

:deep(.el-carousel__arrow) {
  background-color: rgba(0, 0, 0, 0.3);
  border: none;
  transform: scale(1.2);
}

:deep(.el-carousel__arrow:hover) {
  background-color: rgba(0, 0, 0, 0.5);
}

:deep(.el-carousel__container) {
  border-radius: 8px;
}

:deep(.el-carousel__item) {
  border-radius: 8px;
  overflow: hidden;
}
</style>