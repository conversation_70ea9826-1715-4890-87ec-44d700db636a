package com.modelbolt.blog.service;

import com.modelbolt.blog.model.entity.UserSession;
import jakarta.servlet.http.HttpServletRequest;

public interface SessionService {
    /**
     * 创建用户会话
     * @param userId 用户ID
     * @param username 用户名
     * @param request HTTP请求
     * @param token JWT token
     * @param rememberMe 是否记住我
     * @return 会话信息
     */
    UserSession createSession(Long userId, String username, HttpServletRequest request, String token, boolean rememberMe);

    /**
     * 更新用户会话
     * @param sessionId 会话ID
     * @param session 会话信息
     */
    void updateSession(String sessionId, UserSession session);
    
    /**
     * 移除用户会话
     * @param sessionId 会话ID
     */
    void removeSession(String sessionId);
    
    /**
     * 验证会话是否有效
     * @param sessionId 会话ID
     * @return 是否有效
     */
    boolean validateSession(String sessionId);
} 