package com.modelbolt.blog.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.modelbolt.blog.model.entity.User;
import com.modelbolt.blog.exception.business.AuthException;
import com.modelbolt.blog.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SecurityUtils {
    
    private static UserMapper userMapper;
    
    public SecurityUtils(UserMapper userMapper) {
        SecurityUtils.userMapper = userMapper;
    }

    /**
     * 获取当前登录用户ID
     */
    public static Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() &&
                !(authentication instanceof AnonymousAuthenticationToken)) {
            try {
                String username = authentication.getName();
                // 通过用户名查询用户ID
                User user = userMapper.selectOne(
                        new LambdaQueryWrapper<User>()
                                .eq(User::getUsername, username)
                );
                if (user != null) {
                    return user.getId();
                }
            } catch (Exception e) {
                log.warn("Failed to get user id for username: {}", authentication.getName(), e);
            }
            log.warn("Unable to extract userId from authentication");
        }
        throw new AuthException.InvalidToken();
    }

    /**
     * 获取当前登录用户名
     */
    public static String getCurrentUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated() || 
                authentication instanceof AnonymousAuthenticationToken) {
            throw new AuthException.TokenExpired();
        }
        return authentication.getName();
    }

    /**
     * 判断用户是否已登录
     */
    public static boolean isAuthenticated() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return authentication != null && authentication.isAuthenticated() && 
                !(authentication instanceof AnonymousAuthenticationToken);
    }
} 