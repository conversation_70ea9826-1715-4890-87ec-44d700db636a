import { http } from '@/utils/request'
import type { R, PageResult } from '@/types/common'
import type { CategoryVO, ArticleVO } from '@/types/article'

// 获取所有分类
export const getAllCategories = () => {
  return http<R<CategoryVO[]>>({
    url: '/categories',
    method: 'GET'
  })
}

// 获取分类详情
export const getCategoryById = (id: number) => {
  return http<R<CategoryVO>>({
    url: `/categories/${id}`,
    method: 'GET'
  })
}

// 获取分类下的文章列表
export const getArticlesByCategoryId = (categoryId: number, page: number = 1, size: number = 10) => {
  return http<R<PageResult<ArticleVO>>>({
    url: `/categories/${categoryId}/articles`,
    method: 'GET',
    params: { page, size }
  })
}

// 获取热门分类
export const getHotCategories = (limit: number = 10) => {
  return http<R<CategoryVO[]>>({
    url: '/categories/hot',
    method: 'GET',
    params: { limit }
  })
} 