import { http } from '@/utils/request'
import type { R, PageResult } from '@/types/common'
import type { CommentVO, CommentQuery, CommentForm } from '@/types/comment'

/**
 * 获取文章评论列表
 * @param articleId 文章ID
 * @param params 查询参数
 */
export const getComments = (articleId: number, params: CommentQuery) => {
  return http.get<R<PageResult<CommentVO>>>(`/articles/${articleId}/comments`, { params })
}

/**
 * 发表评论
 * @param data 评论数据
 */
export const submitComment = (data: CommentForm) => {
  return http.post<R<CommentVO>>('/comments', data)
}

/**
 * 回复评论
 * @param data 回复数据
 */
export const replyComment = (data: CommentForm) => {
  return http.post<R<CommentVO>>('/comments/reply', data)
}

/**
 * 删除评论
 * @param commentId 评论ID
 */
export const deleteComment = (commentId: number) => {
  return http.delete<R<void>>(`/comments/${commentId}`)
}

/**
 * 点赞评论
 * @param commentId 评论ID
 */
export const likeComment = (commentId: number) => {
  return http.post<R<void>>(`/comments/${commentId}/like`)
}

/**
 * 取消点赞评论
 * @param commentId 评论ID
 */
export const unlikeComment = (commentId: number) => {
  return http.delete<R<void>>(`/comments/${commentId}/like`)
} 