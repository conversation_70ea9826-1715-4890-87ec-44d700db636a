<template>
  <div class="article-content markdown-content">
    <div 
      v-html="renderedContent" 
      :class="[
        'prose prose-lg max-w-none',
        'prose-headings:font-bold prose-headings:mb-4',
        'prose-img:rounded-lg prose-img:shadow-lg',
        {
          // 浅色主题样式
          'prose-gray': !isDarkMode,
          'prose-headings:text-gray-900 prose-p:text-gray-800': !isDarkMode,
          'prose-a:text-blue-600 prose-a:hover:text-blue-800': !isDarkMode,
          'prose-code:bg-gray-100 prose-code:text-gray-800': !isDarkMode,
          'prose-pre:bg-gray-100': !isDarkMode,
          'prose-blockquote:bg-gray-50 prose-blockquote:border-gray-300': !isDarkMode,
          
          // 深色主题样式
          'prose-invert': isDarkMode,
          'prose-headings:text-white prose-p:text-white': isDarkMode,
          'prose-a:text-blue-400 prose-a:hover:text-blue-300': isDarkMode,
          'prose-code:bg-gray-800 prose-code:text-gray-50': isDarkMode,
          'prose-pre:bg-gray-900': isDarkMode,
          'prose-blockquote:bg-gray-800/50 prose-blockquote:border-blue-500': isDarkMode,
          'prose-strong:text-white prose-em:text-white': isDarkMode,
          'prose-ul:text-white prose-ol:text-white': isDarkMode,
          'prose-li:text-white prose-li:marker:text-gray-400': isDarkMode
        }
      ]"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import 'highlight.js/styles/github-dark.css'
import { escapeHtml } from '@/utils/string'
import { useSettingsStore } from '@/stores/settings'

const props = defineProps<{
  content: string
}>()

const settingsStore = useSettingsStore()
const isDarkMode = computed(() => settingsStore.settings.theme === 'dark')

const md = new MarkdownIt({
  html: false,
  linkify: true,
  typographer: true,
  highlight: (str, lang) => {
    if (lang && hljs.getLanguage(lang)) {
      try {
        const escapedStr = escapeHtml(str)
        const highlighted = hljs.highlight(escapedStr, { language: lang, ignoreIllegals: true }).value
        return `<pre class="hljs"><code class="language-${lang}">${highlighted}</code></pre>`
      } catch (e) {
        console.error('Highlight error:', e)
        return `<pre class="hljs"><code>${escapeHtml(str)}</code></pre>`
      }
    }
    return `<pre class="hljs"><code>${escapeHtml(str)}</code></pre>`
  }
})

md.renderer.rules.code_block = (tokens, idx) => {
  const content = tokens[idx].content
  return `<pre class="hljs"><code>${escapeHtml(content)}</code></pre>`
}

md.renderer.rules.fence = (tokens, idx) => {
  const token = tokens[idx]
  const lang = token.info.trim()
  const content = token.content
  
  if (lang && hljs.getLanguage(lang)) {
    try {
      const escapedContent = escapeHtml(content)
      const highlighted = hljs.highlight(escapedContent, { language: lang, ignoreIllegals: true }).value
      return `<pre class="hljs"><code class="language-${lang}">${highlighted}</code></pre>`
    } catch (e) {
      console.error('Fence highlight error:', e)
    }
  }
  return `<pre class="hljs"><code>${escapeHtml(content)}</code></pre>`
}

const renderedContent = computed(() => {
  try {
    const content = props.content || ''
    return md.render(content)
  } catch (e) {
    console.error('Markdown render error:', e)
    return ''
  }
})
</script>

<style lang="scss" scoped>
.markdown-content {
  @apply w-full;

  :deep(.prose) {
    max-width: none;
    
    /* 标题样式 */
    h1, h2, h3, h4, h5, h6 {
      @apply font-bold mt-6 mb-4 text-gray-900 dark:text-gray-50;
    }
    
    h1 { @apply text-3xl; }
    h2 { @apply text-2xl; }
    h3 { @apply text-xl; }
    h4 { @apply text-lg; }
    
    /* 段落样式 */
    p {
      @apply my-4 leading-7 text-gray-800 dark:text-gray-200;
    }
    
    /* 代码块样式 */
    pre.hljs {
      @apply rounded-lg p-4 my-4 overflow-x-auto border transition-colors duration-200;
      
      /* 浅色主题 */
      @apply bg-gray-50 border-gray-200;
      .dark & {
        @apply bg-gray-900 border-gray-700;
      }
      
      code {
        @apply p-0 text-base font-mono bg-transparent;
        
        /* 浅色主题默认文本 */
        @apply text-gray-800;
        .dark & {
          @apply text-gray-100;
        }

        /* 语法高亮 - 浅色主题 */
        .hljs-comment,
        .hljs-quote {
          @apply text-gray-500;
          .dark & {
            @apply text-gray-400;
          }
        }

        .hljs-keyword,
        .hljs-selector-tag,
        .hljs-addition {
          @apply text-purple-700;
          .dark & {
            @apply text-purple-400;
          }
        }

        .hljs-number,
        .hljs-string,
        .hljs-meta .hljs-meta-string,
        .hljs-literal,
        .hljs-doctag,
        .hljs-regexp {
          @apply text-green-700;
          .dark & {
            @apply text-green-400;
          }
        }

        .hljs-title,
        .hljs-section,
        .hljs-name,
        .hljs-selector-id,
        .hljs-selector-class {
          @apply text-blue-700;
          .dark & {
            @apply text-blue-400;
          }
        }

        .hljs-attribute,
        .hljs-attr,
        .hljs-variable,
        .hljs-template-variable,
        .hljs-class .hljs-title,
        .hljs-type {
          @apply text-orange-700;
          .dark & {
            @apply text-orange-300;
          }
        }

        .hljs-symbol,
        .hljs-bullet,
        .hljs-subst,
        .hljs-meta,
        .hljs-meta .hljs-keyword,
        .hljs-selector-attr,
        .hljs-selector-pseudo,
        .hljs-link {
          @apply text-blue-700;
          .dark & {
            @apply text-blue-300;
          }
        }

        .hljs-built_in,
        .hljs-deletion {
          @apply text-red-700;
          .dark & {
            @apply text-red-400;
          }
        }
      }
    }
    
    /* 内联代码样式 */
    code {
      @apply px-2 py-1 rounded text-sm border transition-colors duration-200;
      
      /* 浅色主题 */
      @apply bg-gray-50 text-gray-800 border-gray-200;
      .dark & {
        @apply bg-gray-800 text-gray-100 border-gray-700;
      }
    }
    
    /* 图片样式 */
    img {
      @apply rounded-lg shadow-sm max-w-full h-auto my-4 dark:brightness-90;
    }
    
    /* 列表样式 */
    ul, ol {
      @apply my-4 pl-6;
      
      li {
        @apply my-2;
        
        /* 浅色主题 */
        @apply text-gray-800;
        .dark & {
          @apply text-white;
        }
        
        &::marker {
          @apply text-gray-500;
          .dark & {
            @apply text-gray-400;
          }
        }
      }
    }
    
    /* 确保列表内的段落也继承正确的颜色 */
    ul p, ol p {
      @apply text-gray-800;
      .dark & {
        @apply text-white;
      }
    }
    
    /* 引用样式 */
    blockquote {
      @apply border-l-4 border-gray-300 dark:border-blue-500 pl-4 my-4 italic bg-gray-50 dark:bg-gray-800/50 p-4 rounded-r;
      
      p {
        @apply m-0 text-gray-700 dark:text-gray-300;
      }
    }
    
    /* 链接样式 */
    a {
      @apply text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors duration-200;
    }
    
    /* 表格样式 */
    table {
      @apply w-full my-4 border-collapse bg-white dark:bg-gray-800/50;
      
      th, td {
        @apply border border-gray-300 dark:border-gray-700 px-4 py-2;
      }
      
      th {
        @apply bg-gray-100 dark:bg-gray-700 font-bold text-gray-700 dark:text-gray-100;
      }
      
      td {
        @apply text-gray-800 dark:text-gray-200;
      }
      
      tr:nth-child(even) {
        @apply bg-gray-50 dark:bg-gray-800/50;
      }
      
      tr:hover {
        @apply bg-gray-100 dark:bg-gray-700/50;
      }
    }
  }
}

/* 适配 Windows 高对比度模式 */
@media (forced-colors: active) {
  .markdown-content {
    forced-color-adjust: auto;
  }
}
</style> 