package com.modelbolt.blog.exception.business;

import com.modelbolt.blog.exception.BusinessException;

public class FavoriteException {
    public static class FavoriteNotFound extends BusinessException {
        public FavoriteNotFound() {
            super(404, "收藏记录不存在");
        }
    }

    public static class NoPermission extends BusinessException {
        public NoPermission() {
            super(403, "没有权限操作此收藏");
        }
    }

    public static class CancelFailed extends BusinessException {
        public CancelFailed() {
            super(500, "取消收藏失败");
        }
    }
} 