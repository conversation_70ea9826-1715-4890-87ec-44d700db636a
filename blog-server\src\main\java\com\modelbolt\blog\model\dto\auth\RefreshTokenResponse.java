package com.modelbolt.blog.model.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 刷新令牌响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "刷新令牌响应")
public class RefreshTokenResponse {
    
    @Schema(description = "访问令牌")
    private String accessToken;
    
    @Schema(description = "令牌类型")
    private String tokenType;
    
    @Schema(description = "过期时间（秒）")
    private Long expiresIn;
} 