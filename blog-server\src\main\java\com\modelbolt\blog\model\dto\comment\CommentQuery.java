package com.modelbolt.blog.model.dto.comment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "评论查询参数")
public class CommentQuery {
    @Schema(description = "页码")
    private Integer page = 1;

    @Schema(description = "每页大小")
    private Integer size = 10;

    @Schema(description = "状态:0待审核,1通过,2拒绝")
    private Integer status;

    @Schema(description = "关键词")
    private String keyword;

    @Schema(description = "文章ID")
    private Long articleId;

    @Schema(description = "用户ID")
    private Long userId;
} 