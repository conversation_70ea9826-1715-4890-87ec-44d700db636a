package com.modelbolt.blog.config;

import com.modelbolt.blog.model.entity.SystemConfig;
import com.modelbolt.blog.mapper.SystemConfigMapper;
import com.modelbolt.blog.utils.RedisCache;
import lombok.RequiredArgsConstructor;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class AiConfig {
    private final SystemConfigMapper systemConfigMapper;
    private final RedisCache redisCache;

    private static final String AI_CONFIG_CACHE_KEY = "system:ai_config";

    // 从application.yml读取默认配置
    @Value("${app.system.defaults.ai-model:DeepSeek}")
    private String defaultAiModel;

    @Value("${app.system.defaults.ai-api-key:}")
    private String defaultAiApiKey;

    @Value("${app.system.defaults.ai-api-endpoint:https://api.deepseek.com/v1}")
    private String defaultAiApiEndpoint;

    @Value("${app.system.defaults.ai-max-tokens:2000}")
    private Integer defaultAiMaxTokens;

    @Value("${app.system.defaults.ai-temperature:0.7}")
    private Double defaultAiTemperature;

    @Value("${app.system.defaults.ai-request-limit:1000}")
    private Integer defaultAiRequestLimit;

    @Value("${app.system.defaults.ai-token-limit:100000}")
    private Integer defaultAiTokenLimit;

    @Getter
    private static class AiConfigData {
        private String model;
        private String apiKey;
        private String apiEndpoint;
        private Integer maxTokens;
        private Double temperature;
        private Integer requestLimit;
        private Integer tokenLimit;
    }

    public AiConfigData getConfig() {
        // 先从缓存获取
        AiConfigData config = redisCache.getCacheObject(AI_CONFIG_CACHE_KEY);
        if (config != null) {
            return config;
        }

        // 从数据库获取所有配置
        List<SystemConfig> configList = systemConfigMapper.selectList(null);
        Map<String, String> configMap = configList.stream()
                .collect(Collectors.toMap(
                    SystemConfig::getKey,
                    SystemConfig::getValue,
                    (existing, replacement) -> existing
                ));

        // 创建配置对象
        config = new AiConfigData();
        
        // 设置配置值，优先使用数据库配置，为空则使用默认配置
        config.model = getConfigOrDefault(configMap, "aiModel", defaultAiModel);
        config.apiKey = getConfigOrDefault(configMap, "aiApiKey", defaultAiApiKey);
        config.apiEndpoint = getConfigOrDefault(configMap, "aiApiEndpoint", defaultAiApiEndpoint);
        config.maxTokens = Integer.parseInt(getConfigOrDefault(configMap, "aiMaxTokens", String.valueOf(defaultAiMaxTokens)));
        config.temperature = Double.parseDouble(getConfigOrDefault(configMap, "aiTemperature", String.valueOf(defaultAiTemperature)));
        config.requestLimit = Integer.parseInt(getConfigOrDefault(configMap, "aiRequestLimit", String.valueOf(defaultAiRequestLimit)));
        config.tokenLimit = Integer.parseInt(getConfigOrDefault(configMap, "aiTokenLimit", String.valueOf(defaultAiTokenLimit)));

        // 缓存配置
        redisCache.setCacheObject(AI_CONFIG_CACHE_KEY, config);
        
        return config;
    }

    private String getConfigOrDefault(Map<String, String> configMap, String key, String defaultValue) {
        String value = configMap.get(key);
        return (value == null || value.trim().isEmpty()) ? defaultValue : value;
    }

    // Getter methods for easy access
    public String getModel() {
        return getConfig().model;
    }

    public String getApiKey() {
        return getConfig().apiKey;
    }

    public String getApiEndpoint() {
        return getConfig().apiEndpoint;
    }

    public Integer getMaxTokens() {
        return getConfig().maxTokens;
    }

    public Double getTemperature() {
        return getConfig().temperature;
    }

    public Integer getRequestLimit() {
        return getConfig().requestLimit;
    }

    public Integer getTokenLimit() {
        return getConfig().tokenLimit;
    }
} 