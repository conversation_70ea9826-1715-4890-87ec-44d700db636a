package com.modelbolt.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.modelbolt.blog.model.entity.Category;
import com.modelbolt.blog.model.entity.Article;
import com.modelbolt.blog.model.vo.SearchVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Result;

import java.util.List;
import java.util.Map;

@Mapper
public interface CategoryMapper extends BaseMapper<Category> {
    @Select("SELECT c.name as type, COUNT(DISTINCT a.id) as count " +
            "FROM category c " +
            "LEFT JOIN article_category ac ON c.id = ac.category_id " +
            "LEFT JOIN article a ON ac.article_id = a.id AND a.status = 1 " +
            "GROUP BY c.id, c.name " +
            "ORDER BY count DESC, c.order_num ASC")
    @Results({
        @Result(column = "type", property = "type", javaType = String.class),
        @Result(column = "count", property = "count", javaType = Integer.class)
    })
    List<Map<String, Object>> getCategoryStats();

    List<Category> selectList();

    @Select("SELECT COUNT(DISTINCT a.id) FROM article_category ac " +
            "LEFT JOIN article a ON ac.article_id = a.id AND a.status = 1 " +
            "WHERE ac.category_id = #{categoryId}")
    Integer countArticles(Long categoryId);

    @Select("SELECT c.id, c.name, 'category' as type, " +
            "COUNT(ac.article_id) as articleCount " +
            "FROM category c " +
            "LEFT JOIN article_category ac ON c.id = ac.category_id " +
            "WHERE c.name LIKE CONCAT('%', #{keyword}, '%') " +
            "GROUP BY c.id")
    List<SearchVO> searchCategories(@Param("keyword") String keyword);

    @Select("SELECT a.* FROM article a " +
            "INNER JOIN article_category ac ON a.id = ac.article_id " +
            "WHERE ac.category_id = #{categoryId} AND a.status = 1 " +
            "ORDER BY a.created_at DESC")
    IPage<Article> getArticlesByCategoryId(Page<Article> page, @Param("categoryId") Long categoryId);

    @Select("SELECT c.* FROM category c " +
            "LEFT JOIN article_category ac ON c.id = ac.category_id " +
            "LEFT JOIN article a ON ac.article_id = a.id AND a.status = 1 " +
            "GROUP BY c.id " +
            "ORDER BY COUNT(DISTINCT a.id) DESC " +
            "LIMIT #{limit}")
    List<Category> getHotCategories(@Param("limit") Integer limit);

    /**
     * 获取文章的分类列表
     */
    @Select("SELECT c.* FROM category c " +
            "INNER JOIN article_category ac ON c.id = ac.category_id " +
            "WHERE ac.article_id = #{articleId}")
    List<Category> selectCategoriesByArticleId(@Param("articleId") Long articleId);
}