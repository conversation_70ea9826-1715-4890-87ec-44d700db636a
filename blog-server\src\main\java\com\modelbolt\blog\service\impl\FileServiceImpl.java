package com.modelbolt.blog.service.impl;

import com.modelbolt.blog.exception.business.FileException;
import com.modelbolt.blog.service.FileService;
import com.modelbolt.blog.utils.FileUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 文件服务实现类
 * 提供文件上传、下载、缓存等功能的具体实现
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileServiceImpl implements FileService {

    /** Redis操作模板 */
    private final RedisTemplate<String, String> redisTemplate;
    
    /** 文件工具类 */
    private final FileUtils fileUtils;

    /** 文件上传根路径 */
    @Value("${file.upload.path}")
    private String uploadPath;

    /** 文件URL前缀 */
    @Value("${file.upload.url-prefix}")
    private String urlPrefix;

    /** 允许的文件类型列表，逗号分隔 */
    @Value("${file.upload.allowed-types}")
    private String allowedTypes;

    /** 图片缓存键前缀 */
    private static final String IMAGE_CACHE_KEY_PREFIX = "image:";
    
    /** 默认图片缓存过期时间（秒） */
    private static final long IMAGE_CACHE_EXPIRE_TIME = 7 * 24 * 60 * 60L; // 7天

    /**
     * 上传图片文件
     * 
     * @param file 图片文件
     * @param subDir 子目录
     * @return 图片访问URL
     */
    @Override
    public String uploadImage(MultipartFile file, String subDir) {
        // 检查文件是否为空
        if (file.isEmpty()) {
            throw new FileException.EmptyFile();
        }

        // 获取文件名和扩展名
        String originalFilename = file.getOriginalFilename();
        String extension = StringUtils.getFilenameExtension(originalFilename);

        // 检查文件类型
        if (!isAllowedFileType(extension)) {
            throw new FileException.InvalidFileType();
        }

        try {
            // 创建目标目录
            String targetDir = uploadPath + (subDir.startsWith("/") ? subDir : "/" + subDir);
            Path dirPath = Paths.get(targetDir);
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
                log.info("创建目录: {}", targetDir);
            }

            // 生成新的文件名
            String newFilename = UUID.randomUUID().toString() + "." + extension;
            Path targetPath = Paths.get(targetDir, newFilename);

            // 保存文件
            byte[] fileBytes = file.getBytes();
            Files.write(targetPath, fileBytes);
            log.info("保存文件: {}", targetPath);

            // 生成访问URL
            String fileUrl = subDir + "/" + newFilename;
            if (!fileUrl.startsWith("/")) {
                fileUrl = "/" + fileUrl;
            }

            // 缓存图片到Redis
            cacheImage(fileUrl, fileBytes);

            return urlPrefix + fileUrl;

        } catch (IOException e) {
            log.error("保存文件失败: {}", e.getMessage());
            throw new FileException.SaveFailed(e.getMessage());
        }
    }

    /**
     * 将图片缓存到Redis
     * 
     * @param fileUrl 图片URL
     * @param fileBytes 图片字节数组
     */
    @Override
    public void cacheImage(String fileUrl, byte[] fileBytes) {
        cacheImage(fileUrl, fileBytes, IMAGE_CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
    }
    
    /**
     * 将图片缓存到Redis，并设置过期时间
     * 
     * @param fileUrl 图片URL
     * @param fileBytes 图片字节数组
     * @param timeout 过期时间
     * @param unit 时间单位
     */
    @Override
    public void cacheImage(String fileUrl, byte[] fileBytes, long timeout, TimeUnit unit) {
        try {
            // 对图片进行Base64编码
            String base64Image = Base64.getEncoder().encodeToString(fileBytes);
            
            // 构建缓存键
            String cacheKey = IMAGE_CACHE_KEY_PREFIX + fileUrl;
            
            // 将编码后的图片存入Redis，并设置过期时间
            redisTemplate.opsForValue().set(cacheKey, base64Image, timeout, unit);
            log.debug("已缓存图片: {}", fileUrl);
        } catch (Exception e) {
            log.error("缓存图片失败: {}", e.getMessage());
        }
    }

    /**
     * 从缓存中获取图片
     * 
     * @param fileUrl 图片URL
     * @return 缓存的Base64编码图片数据，如果不存在则返回null
     */
    @Override
    public String getImageFromCache(String fileUrl) {
        // 构建缓存键
        String cacheKey = IMAGE_CACHE_KEY_PREFIX + fileUrl;
        
        // 从Redis获取图片
        String cachedImage = redisTemplate.opsForValue().get(cacheKey);
        
        if (cachedImage != null) {
            log.debug("从缓存获取图片: {}", fileUrl);
        }
        
        return cachedImage;
    }
    
    /**
     * 清除指定图片的缓存
     * 
     * @param fileUrl 图片URL
     */
    @Override
    public void clearImageCache(String fileUrl) {
        String cacheKey = IMAGE_CACHE_KEY_PREFIX + fileUrl;
        redisTemplate.delete(cacheKey);
        log.debug("已清除图片缓存: {}", fileUrl);
    }
    
    /**
     * 清除所有图片缓存
     */
    @Override
    public void clearAllImageCache() {
        try {
            // 获取所有图片缓存键
            Set<String> keys = redisTemplate.keys(IMAGE_CACHE_KEY_PREFIX + "*");
            if (keys != null && !keys.isEmpty()) {
                // 批量删除所有图片缓存
                redisTemplate.delete(keys);
                log.info("已清除所有图片缓存，共{}个", keys.size());
            }
        } catch (Exception e) {
            log.error("清除所有图片缓存失败: {}", e.getMessage());
        }
    }

    /**
     * 检查文件类型是否允许
     * 
     * @param extension 文件扩展名
     * @return 是否允许上传
     */
    private boolean isAllowedFileType(String extension) {
        if (extension == null) {
            return false;
        }
        
        // 解析允许的文件类型列表
        String[] allowed = allowedTypes.split(",");
        for (String type : allowed) {
            if (type.trim().equalsIgnoreCase(extension)) {
                return true;
            }
        }
        
        return false;
    }
} 