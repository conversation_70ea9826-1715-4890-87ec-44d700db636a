<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 标签列表 -->
    <div class="bg-white rounded-lg shadow-sm p-8">
      <h1 class="text-2xl font-bold mb-8">文章标签</h1>
      
      <!-- 标签搜索 -->
      <div class="mb-8">
        <el-input
          v-model="searchQuery"
          placeholder="搜索标签..."
          class="max-w-md"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 标签云 -->
      <div v-loading="loading" class="flex flex-wrap gap-4">
        <router-link
          v-for="tag in filteredTags"
          :key="tag.id"
          :to="'/tags/' + tag.id"
          class="group"
        >
          <el-tag
            :type="getRandomType()"
            class="text-base px-4 py-2 cursor-pointer transition-all duration-300 hover:scale-110"
            :effect="tag.articleCount > 10 ? 'dark' : 'light'"
          >
            {{ tag.name }}
            <span class="ml-2 text-sm opacity-75">{{ tag.articleCount }}</span>
          </el-tag>
        </router-link>

        <!-- 无数据提示 -->
        <el-empty v-if="!loading && filteredTags.length === 0" description="暂无标签数据" />
      </div>
    </div>

    <!-- 最新文章 -->
    <div class="bg-white rounded-lg shadow-sm p-8 mt-8">
      <h2 class="text-xl font-bold mb-6">最新文章</h2>
      <div v-loading="articlesLoading" class="space-y-6">
        <router-link
          v-for="article in latestArticles"
          :key="article.id"
          :to="'/articles/' + article.id"
          class="block group"
        >
          <div class="flex gap-6">
            <div class="w-48 h-32 rounded-lg overflow-hidden flex-shrink-0">
              <img
                :src="getCoverUrl(article.cover)"
                :alt="article.title"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              >
            </div>
            <div class="flex-1">
              <h3 class="text-lg font-medium group-hover:text-blue-600 mb-2">
                {{ article.title }}
              </h3>
              <p class="text-gray-600 text-sm line-clamp-2 mb-4">
                {{ article.summary }}
              </p>
              <div class="flex items-center text-sm text-gray-500 space-x-4">
                <span class="flex items-center">
                  <el-icon class="mr-1"><Timer /></el-icon>
                  {{ formatDate(article.createdAt) }}
                </span>
                <span class="flex items-center">
                  <el-icon class="mr-1"><View /></el-icon>
                  {{ article.viewCount }}
                </span>
                <span class="flex items-center">
                  <el-icon class="mr-1"><ChatDotRound /></el-icon>
                  {{ article.commentCount }}
                </span>
              </div>
            </div>
          </div>
        </router-link>

        <!-- 无数据提示 -->
        <el-empty v-if="!articlesLoading && latestArticles.length === 0" description="暂无文章数据" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Search, Timer, View, ChatDotRound } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/date'
import type { TagVO, ArticleVO } from '@/types/article'
import { getAllTags } from '@/api/tag'
import { getArticles } from '@/api/article'
import { ElMessage } from 'element-plus'
import { getCoverUrl } from '@/utils/image'

// 搜索关键词
const searchQuery = ref('')

// 加载状态
const loading = ref(false)
const articlesLoading = ref(false)

// 标签数据
const tags = ref<TagVO[]>([])

// 过滤标签
const filteredTags = computed(() => {
  if (!searchQuery.value) return tags.value
  const query = searchQuery.value.toLowerCase()
  return tags.value.filter(tag => 
    tag.name.toLowerCase().includes(query)
  )
})

// 最新文章数据
const latestArticles = ref<ArticleVO[]>([])

// 获取标签数据
const fetchTags = async () => {
  loading.value = true
  try {
    const { data } = await getAllTags()
    tags.value = data
  } catch (error) {
    ElMessage.error('获取标签数据失败')
    console.error('获取标签数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取最新文章
const fetchLatestArticles = async () => {
  articlesLoading.value = true
  try {
    const { data } = await getArticles({
      page: 1,
      size: 3,
      sort: 'newest'
    })
    latestArticles.value = data.records
  } catch (error) {
    ElMessage.error('获取最新文章失败')
    console.error('获取最新文章失败:', error)
  } finally {
    articlesLoading.value = false
  }
}

// 随机标签类型
const tagTypes = ['primary', 'success', 'warning', 'danger', 'info']
const getRandomType = () => {
  return tagTypes[Math.floor(Math.random() * tagTypes.length)]
}

// 页面加载时获取数据
onMounted(() => {
  fetchTags()
  fetchLatestArticles()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 