package com.modelbolt.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.modelbolt.blog.model.entity.RewardConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.type.JdbcType;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;

@Mapper
public interface RewardConfigMapper extends BaseMapper<RewardConfig> {
    
    @Select("SELECT * FROM reward_config WHERE user_id = #{userId}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "enabled", column = "enabled"),
        @Result(property = "amounts", column = "amounts", jdbcType = JdbcType.VARCHAR, typeHandler = JacksonTypeHandler.class),
        @Result(property = "qrcodeWechat", column = "qrcode_wechat"),
        @Result(property = "qrcodeAlipay", column = "qrcode_alipay"),
        @Result(property = "createdAt", column = "created_at"),
        @Result(property = "updatedAt", column = "updated_at")
    })
    RewardConfig selectByUserId(@Param("userId") Long userId);
} 