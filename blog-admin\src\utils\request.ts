import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { message, Modal } from 'ant-design-vue'
import router from '@/router'
import { useAdminStore } from '@/stores/admin'
import type { ApiResponse } from '@/types/api'

// 扩展AxiosRequestConfig接口，添加_isExport属性
declare module 'axios' {
  interface AxiosRequestConfig {
    _isExport?: boolean;
    _retryCount?: number;
    _skipAuthRefresh?: boolean;
  }
}

// 创建 axios 实例
const service: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json;charset=utf-8'
  }
})

// 防止多次弹出Token过期提示
let isTokenExpiredModalVisible = false
// 标记是否正在刷新令牌
let isRefreshing = false
// 等待令牌刷新的请求队列
let requestsQueue: Array<{
  resolve: (value: unknown) => void;
  reject: (reason?: any) => void;
  config: AxiosRequestConfig;
}> = []

// 获取请求超时时间
const getRequestTimeout = (url: string): number => {
  // AI相关请求使用更长的超时时间（3分钟）
  if (url.includes('/ai/')) {
    return 300000
  }
  // 其他请求使用默认超时时间
  return 15000
}

// 处理队列中的请求
const processRequestsQueue = (error: any) => {
  requestsQueue.forEach(request => {
    if (error) {
      request.reject(error)
    } else {
      request.resolve(service(request.config))
    }
  })
  requestsQueue = []
}

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers = config.headers || {}
      config.headers['Authorization'] = `Bearer ${token}`
    }
    // 根据请求URL设置超时时间
    config.timeout = getRequestTimeout(config.url || '')
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 处理认证错误的通用方法
const handleAuthError = async (response: any) => {
  const adminStore = useAdminStore()
  const currentPath = router.currentRoute.value.fullPath

  // 获取详细的错误信息
  const errorData = response.data || {}
  const errorMessage = errorData.message || '登录已过期，请重新登录'
  const errorCode = errorData.error || 'AUTHENTICATION_FAILED'
  
  // 仅在没有显示过期提示的情况下显示
  if (!isTokenExpiredModalVisible) {
    isTokenExpiredModalVisible = true
    
    try {
      await Modal.confirm({
        title: '登录提示',
        content: errorMessage,
        okText: '重新登录',
        cancelText: '取消',
        onOk: () => {
          // 清除登录状态
          adminStore.logout()
          
          // 检查当前路由是否已经是登录页，避免重复跳转
          if (!currentPath.startsWith('/login')) {
            router.replace({
              path: '/login',
              query: { redirect: currentPath }
            })
          }
        },
        onCancel: () => {
          // 仅清除登录状态但不跳转
          adminStore.logout()
        }
      })
    } finally {
      // 重置标志，允许下次显示提示
      setTimeout(() => {
        isTokenExpiredModalVisible = false
      }, 1000)
    }
  }
}

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const res = response.data

    // 如果是登录响应，检查并存储token
    if (response.config.url?.includes('/login') && res.code === 200 && res.data?.token) {
      localStorage.setItem('token', res.data.token)
      if (res.data.refreshToken) {
        localStorage.setItem('refreshToken', res.data.refreshToken)
      }
    }

    // 导出请求跳过状态码检查
    if (response.config._isExport) {
      return response
    }

    // 检查响应状态码
    if (res.code !== 200) {
      message.error(res.message || '请求失败')
      return Promise.reject(new Error(res.message || '请求失败'))
    }

    return res
  },
  async (error) => {
    const originalRequest = error.config
    
    // 防止无限重试
    const retryCount = originalRequest._retryCount || 0
    if (retryCount > 3) {
      return Promise.reject(error)
    }
    
    // 检查是否是令牌过期错误
    if (error.response?.status === 401 
        && !originalRequest._skipAuthRefresh 
        && !originalRequest.url?.includes('/refresh-token')) {
      
      const adminStore = useAdminStore()
      
      // 如果有刷新令牌且没有正在刷新，尝试刷新令牌
      if (adminStore.refreshToken && !isRefreshing) {
        isRefreshing = true
        
        try {
          // 尝试刷新令牌
          const refreshSuccess = await adminStore.tryRefreshToken()
          
          if (refreshSuccess) {
            // 刷新成功，重试原始请求
            originalRequest._retryCount = (originalRequest._retryCount || 0) + 1
            
            // 更新原始请求的认证令牌
            originalRequest.headers.Authorization = `Bearer ${adminStore.token}`
            
            // 处理队列中的请求
            processRequestsQueue(null)
            
            return service(originalRequest)
          } else {
            // 刷新失败，处理队列中的请求并显示登录模态框
            processRequestsQueue(error)
            await handleAuthError(error.response)
            return Promise.reject(error)
          }
        } catch (refreshError) {
          // 发生错误，处理队列中的请求并显示登录模态框
          processRequestsQueue(refreshError)
          await handleAuthError(error.response)
          return Promise.reject(error)
        } finally {
          isRefreshing = false
        }
      } else if (isRefreshing) {
        // 如果正在刷新，将请求加入队列
        return new Promise((resolve, reject) => {
          requestsQueue.push({
            resolve,
            reject,
            config: originalRequest
          })
        })
      } else {
        // 没有刷新令牌或已经尝试过刷新，显示登录模态框
        await handleAuthError(error.response)
      }
    } else {
      // 其他错误处理
      let msg = ''
      const status = error.response?.status
      
      switch (status) {
        case 403:
          msg = '拒绝访问'
          break
        case 404:
          msg = '请求地址错误'
          break
        case 500:
          msg = '服务器故障'
          break
        default:
          msg = error.response?.data?.message || '网络连接故障'
      }

      if (status !== 401) {
        message.error(msg)
      }
    }
    
    return Promise.reject(error)
  }
)

// token 检查函数
const checkTokenValidity = async () => {
  const token = localStorage.getItem('token')
  if (token) {
    try {
      await service.get('/auth/admin/check-token')
    } catch (error: any) {
      if (error.response?.status === 401) {
        // 使用公共方法处理认证错误
        await handleAuthError(error.response)
      }
    }
  }
}

// 启动定期检查
let tokenCheckInterval: number | null = null

export const startTokenCheck = () => {
  if (tokenCheckInterval !== null) {
    clearInterval(tokenCheckInterval)
  }
  // 每5分钟检查一次token有效性
  tokenCheckInterval = window.setInterval(checkTokenValidity, 5 * 60 * 1000)
}

export const stopTokenCheck = () => {
  if (tokenCheckInterval !== null) {
    clearInterval(tokenCheckInterval)
    tokenCheckInterval = null
  }
}

// 导出封装的请求方法
export const http = {
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return service.get(url, config)
  },

  post<T = any>(url: string, data?: object, config?: AxiosRequestConfig): Promise<T> {
    return service.post(url, data, config)
  },

  put<T = any>(url: string, data?: object, config?: AxiosRequestConfig): Promise<T> {
    return service.put(url, data, config)
  },

  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return service.delete(url, config)
  }
}

export default service 