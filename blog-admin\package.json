{"name": "blog-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "test:unit": "vitest", "test:coverage": "vitest run --coverage", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@stomp/stompjs": "^7.0.0", "@types/js-cookie": "^3.0.6", "@types/node": "^22.10.3", "@vueuse/core": "^12.3.0", "ant-design-vue": "^4.2.6", "autoprefixer": "^10.4.20", "axios": "^1.7.9", "echarts": "^5.6.0", "js-cookie": "^3.0.5", "md-editor-v3": "^2.11.3", "pinia": "^2.3.0", "postcss": "^8.4.49", "sockjs-client": "^1.6.1", "tailwindcss": "^3.4.17", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "4"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/node": "^18.18.5", "@vitejs/plugin-vue": "^4.4.0", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.4", "@vue/tsconfig": "^0.4.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "jsdom": "^24.0.0", "less": "^4.2.1", "npm-run-all2": "^6.1.1", "prettier": "^3.0.3", "sass": "^1.83.0", "typescript": "~5.2.0", "vite": "^4.4.11", "vitest": "^1.3.1"}}