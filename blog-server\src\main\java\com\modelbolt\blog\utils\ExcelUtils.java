package com.modelbolt.blog.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.text.SimpleDateFormat;
import java.util.List;

@Slf4j
public class ExcelUtils {
    private ExcelUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 导出Excel
     *
     * @param response HTTP响应
     * @param sheetName 工作表名称
     * @param clazz 数据类型
     * @param data 数据列表
     * @param <T> 泛型类型
     */
    public static <T> void export(HttpServletResponse response, String sheetName, Class<T> clazz, List<T> data) {
        try {
            log.debug("Starting Excel export. SheetName: {}, DataSize: {}", sheetName, data.size());
            
            // 添加时间戳格式化
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");
            String timestamp = sdf.format(new Date());

            // 构建带时间戳的文件名
            String fileName = String.format("%s_%s", sheetName, timestamp);
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8)
                .replaceAll("\\+", "%20"); // 替换空格编码

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", 
                String.format("attachment; filename*=utf-8''%s.xlsx", encodedFileName));
            
            log.debug("Response headers set. Starting EasyExcel write...");
            
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream(), clazz)
                    .autoCloseStream(Boolean.FALSE)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet(sheetName)
                    .doWrite(data);

            log.debug("Excel export completed successfully");
            
        } catch (Exception e) {
            log.error("Failed to export excel. Error: {}", e.getMessage(), e);
            // 重置response
            try {
                response.reset();
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("{\"message\":\"导出Excel失败：" + e.getMessage() + "\"}");
            } catch (Exception ex) {
                log.error("Failed to write error response", ex);
            }
            throw new RuntimeException("导出Excel失败：" + e.getMessage());
        }
    }
} 