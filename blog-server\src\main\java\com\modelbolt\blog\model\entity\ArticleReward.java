package com.modelbolt.blog.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("article_reward")
public class ArticleReward {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long articleId;
    private Long userId;
    private BigDecimal amount;
    private String paymentType;
    private String paymentStatus;
    private String transactionId;
    private String message;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
} 