package com.modelbolt.blog.model.vo;

import com.modelbolt.blog.model.dto.tag.TagVO;
import com.modelbolt.blog.model.dto.category.CategoryVO;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ArchiveVO {
    private String year;
    private Integer count;
    private List<ArchiveMonthVO> months;

    @Data
    public static class ArchiveMonthVO {
        private String month;
        private Integer count;
        private List<ArchiveArticleVO> articles;
    }

    @Data
    public static class ArchiveArticleVO {
        private Long id;
        private String title;
        private LocalDateTime createdAt;
        private Integer viewCount;
        private List<CategoryVO> categories;
        private List<TagVO> tags;
    }
}