package com.modelbolt.blog;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 博客系统主应用类
 */
@SpringBootApplication
@EnableTransactionManagement
@MapperScan("com.modelbolt.blog.mapper")
@EnableCaching      // 启用缓存
@EnableScheduling   // 启用定时任务
public class BlogServerApplication {
    public static void main(String[] args) {
        SpringApplication.run(BlogServerApplication.class, args);
    }
} 