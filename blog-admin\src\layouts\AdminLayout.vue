<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { h } from 'vue'
import { useRouter } from 'vue-router'
import { useAdminStore } from '@/stores/admin'
import { useAdminSettingsStore } from '@/stores/adminSettings'
import { getAvatarUrl, getRefreshedImageUrl, getAvatarText, useImageFallback } from '@/utils/image'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  FileTextOutlined,
  FolderOutlined,
  TagsOutlined,
  CommentOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

const router = useRouter()
const adminStore = useAdminStore()
const adminSettingsStore = useAdminSettingsStore()
const collapsed = ref(false)
const { imgError } = useImageFallback()

// 加载管理员设置
onMounted(async () => {
  await Promise.all([
    adminSettingsStore.loadSettings(),
    adminStore.getAdminInfo()
  ])
})

const menuItems = [
  {
    key: '/',
    icon: () => h(DashboardOutlined),
    label: '仪表盘',
  },
  {
    key: '/article',
    icon: () => h(FileTextOutlined),
    label: '文章管理',
  },
  {
    key: '/category',
    icon: () => h(FolderOutlined),
    label: '分类管理',
  },
  {
    key: '/tag',
    icon: () => h(TagsOutlined),
    label: '标签管理',
  },
  {
    key: '/comment',
    icon: () => h(CommentOutlined),
    label: '评论管理',
  },
  {
    key: '/user',
    icon: () => h(UserOutlined),
    label: '用户管理',
  },
  {
    key: '/setting',
    icon: () => h(SettingOutlined),
    label: '系统设置',
  },
]

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
  router.push(key)
}

// 处理用户菜单点击
const handleUserMenuClick = ({ key }: { key: string }) => {
  if (key === 'profile') {
    router.push('/profile')
  } else if (key === 'logout') {
    handleLogout()
  }
}

// 处理邮箱验证设置变更
const handleEmailVerificationChange = async (checked: boolean) => {
  try {
    const success = await adminSettingsStore.updateSettings({
      emailVerificationEnabled: checked
    })
    
    if (success) {
      message.success(`已${checked ? '开启' : '关闭'}邮箱验证`)
    } else {
      message.error('设置更新失败')
      // 回滚开关状态
      adminSettingsStore.settings.emailVerificationEnabled = !checked
    }
  } catch (error) {
    console.error('设置更新异常:', error)
    message.error('设置更新异常')
    adminSettingsStore.settings.emailVerificationEnabled = !checked
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await adminStore.logout()
    router.push('/login')
  } catch (error) {
    console.error('Logout failed:', error)
  }
}
</script>

<template>
  <a-layout class="min-h-screen">
    <!-- 侧边栏 -->
    <a-layout-sider v-model:collapsed="collapsed" collapsible>
      <div class="logo">
        <h1 class="text-white text-xl font-bold truncate">
          {{ collapsed ? 'MB' : 'ModelBolt' }}
        </h1>
      </div>
      <a-menu
        theme="dark"
        mode="inline"
        :items="menuItems"
        @click="handleMenuClick"
        :selectedKeys="[router.currentRoute.value.path]"
      />
    </a-layout-sider>

    <a-layout>
      <!-- 顶部栏 -->
      <a-layout-header class="bg-white px-4 flex justify-between items-center">
        <div>
          <component
            :is="collapsed ? MenuUnfoldOutlined : MenuFoldOutlined"
            class="text-lg cursor-pointer"
            @click="() => (collapsed = !collapsed)"
          />
        </div>
        <div class="flex items-center space-x-4">
          <!-- 用户信息和下拉菜单 -->
          <a-dropdown>
            <div class="user-info flex items-center space-x-2 cursor-pointer">
              <div class="avatar-wrapper w-8 h-8 rounded-full overflow-hidden">
                <img 
                  v-if="adminStore.user?.avatar"
                  :src="getRefreshedImageUrl(getAvatarUrl(adminStore.user.avatar))"
                  :alt="adminStore.user?.username"
                  @error="imgError"
                  class="w-full h-full object-cover"
                  loading="lazy"
                  decoding="async"
                  :key="adminStore.user.avatar"
                />
                <div 
                  v-else 
                  class="w-full h-full bg-blue-500 flex items-center justify-center text-white"
                >
                  {{ getAvatarText(adminStore.user?.username) }}
                </div>
              </div>
              <span class="username">
                {{ adminStore.user?.username || '管理员' }}
              </span>
            </div>
            <template #overlay>
              <a-menu @click="handleUserMenuClick">
                <a-menu-item key="profile">
                  <div class="flex items-center">
                    <UserOutlined class="mr-2" />
                    <span>个人资料</span>
                  </div>
                </a-menu-item>
                <a-menu-item key="emailVerification">
                  <div class="flex items-center justify-between w-full px-2">
                    <span class="mr-4">邮箱验证</span>
                    <a-switch
                      :checked="adminSettingsStore.settings.emailVerificationEnabled"
                      :loading="adminSettingsStore.isUpdating"
                      @change="handleEmailVerificationChange"
                    />
                  </div>
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout" @click="handleLogout">
                  <div class="flex items-center text-red-500">
                    <LogoutOutlined class="mr-2" />
                    <span>退出登录</span>
                  </div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>

      <!-- 内容区 -->
      <a-layout-content class="p-6">
        <router-view />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<style scoped>
.logo {
  height: 32px;
  margin: 16px;
  padding: 0 16px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.avatar-wrapper {
  background: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.username {
  font-size: 14px;
  color: #f7f5f5;
  font-weight: 500;
}

:deep(.ant-dropdown-menu) {
  min-width: 180px;
}

:deep(.ant-dropdown-menu-item) {
  padding: 8px 12px;
}
</style>