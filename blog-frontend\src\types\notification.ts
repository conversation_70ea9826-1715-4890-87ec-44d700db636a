// 通知类型
export type NotificationType = 'comment' | 'reply' | 'like' | 'system'

// 目标类型
export type TargetType = 'article' | 'comment' | 'system'

// 通知VO
export interface NotificationVO {
  id: number
  type: NotificationType
  senderName: string
  senderAvatar: string
  action: string
  targetType: TargetType
  targetId: number | null
  targetTitle: string
  content: string
  isRead: boolean
  createdAt: string
}

// 通知统计
export interface NotificationStats {
  total: number
  unread: number
  comment: number
  reply: number
  like: number
  system: number
} 