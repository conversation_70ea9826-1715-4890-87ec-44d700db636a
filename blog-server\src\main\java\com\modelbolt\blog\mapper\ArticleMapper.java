package com.modelbolt.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.modelbolt.blog.model.entity.Article;
import com.modelbolt.blog.model.vo.ArchiveStatsVO;
import com.modelbolt.blog.model.vo.ArchiveVO;
import com.modelbolt.blog.model.vo.SearchVO;
import com.modelbolt.blog.model.vo.HotSearchVO;
import com.modelbolt.blog.model.dto.article.ArticleEditVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Delete;

import java.util.List;
import java.util.Map;

@Mapper
public interface ArticleMapper extends BaseMapper<Article> {
    @Select("SELECT COUNT(*) FROM article")
    int count();

    @Select("SELECT COALESCE(SUM(view_count), 0) FROM article")
    int sumViewCount();

    @Select("SELECT * FROM article WHERE status = 1 ORDER BY view_count DESC LIMIT #{limit}")
    List<Article> selectHotArticles(Integer limit);

    @Select("SELECT * FROM article WHERE status = 1 AND is_top = 1 ORDER BY created_at DESC LIMIT #{limit}")
    List<Article> selectFeaturedArticles(Integer limit);

    /**
     * 分页查询指定分类下的文章
     */
    @Select({
        "<script>",
        "SELECT DISTINCT a.* FROM article a",
        "INNER JOIN article_category ac ON a.id = ac.article_id",
        "WHERE a.status = 1 AND ac.category_id = #{categoryId}",
        "<choose>",
        "  <when test='sort == \"hottest\"'>",
        "    ORDER BY a.view_count DESC",
        "  </when>",
        "  <when test='sort == \"recommended\"'>",
        "    ORDER BY a.like_count DESC",
        "  </when>",
        "  <otherwise>",
        "    ORDER BY a.created_at DESC",
        "  </otherwise>",
        "</choose>",
        "</script>"
    })
    Page<Article> selectPageWithCategory(Page<Article> page, @Param("categoryId") Long categoryId, @Param("sort") String sort);

    @Select("SELECT id, title, summary, 'article' as type, view_count as viewCount, " +
            "created_at as createTime FROM article " +
            "WHERE status = 1 AND (title LIKE CONCAT('%', #{keyword}, '%') " +
            "OR content LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY is_top DESC, created_at DESC")
    List<SearchVO> searchArticles(@Param("keyword") String keyword);

    @Select("SELECT title as keyword, view_count as count " +
            "FROM article " +
            "WHERE status = 1 " +
            "ORDER BY view_count DESC, created_at DESC " +
            "LIMIT 10")
    List<HotSearchVO> findHotKeywords();

    /**
     * 获取归档统计信息
     * 包括：总文章数、时间范围、年度统计
     */
    @Select("""
        WITH YearStats AS (
            SELECT YEAR(created_at) as year, COUNT(*) as count 
            FROM article 
            WHERE status = 1 
            GROUP BY YEAR(created_at)
            ORDER BY year DESC
        )
        SELECT 
            (SELECT COUNT(*) FROM article WHERE status = 1) as total,
            CASE 
                WHEN EXISTS (SELECT 1 FROM YearStats)
                THEN CAST(CONCAT('[', GROUP_CONCAT(
                    JSON_OBJECT(
                        'year', CAST(year AS CHAR),
                        'count', count
                    ) ORDER BY year DESC
                ), ']') AS JSON)
                ELSE CAST('[]' AS JSON)
            END as yearStats,
            JSON_OBJECT(
                'start', IFNULL(
                    (SELECT DATE_FORMAT(MIN(created_at), '%Y-%m-%d %H:%i:%s') 
                    FROM article 
                    WHERE status = 1),
                    NULL
                ),
                'end', IFNULL(
                    (SELECT DATE_FORMAT(MAX(created_at), '%Y-%m-%d %H:%i:%s') 
                    FROM article 
                    WHERE status = 1),
                    NULL
                )
            ) as timeRange
        FROM YearStats
        """)
    @Results({
        @Result(property = "total", column = "total"),
        @Result(property = "yearStats", column = "yearStats", typeHandler = com.modelbolt.blog.typehandler.JsonTypeHandler.class),
        @Result(property = "timeRange", column = "timeRange", typeHandler = com.modelbolt.blog.typehandler.JsonTypeHandler.class)
    })
    ArchiveStatsVO selectArchiveStats();

    /**
     * 获取年份统计
     */
    @Select({
            "<script>",
            "SELECT CAST(year_val AS CHAR) as year, COUNT(*) as count",
            "FROM (",
            "    SELECT YEAR(created_at) as year_val",
            "    FROM article",
            "    WHERE status = 1",
            "    <if test='year != null'>",
            "        AND YEAR(created_at) = #{year}",
            "    </if>",
            "    <if test='categoryId != null'>",
            "        AND EXISTS (SELECT 1 FROM article_category ac WHERE ac.article_id = article.id AND ac.category_id = #{categoryId})",
            "    </if>",
            "    <if test='tagId != null'>",
            "        AND EXISTS (SELECT 1 FROM article_tag at WHERE at.article_id = article.id AND at.tag_id = #{tagId})",
            "    </if>",
            ") t",
            "GROUP BY year_val",
            "ORDER BY year_val DESC",
            "</script>"
    })
    List<ArchiveVO> selectYearStats(@Param("year") Integer year,
                                   @Param("categoryId") Long categoryId,
                                   @Param("tagId") Long tagId);

    /**
     * 获取指定年份的月份统计
     */
    @Select({
            "<script>",
            "SELECT month_val as month, COUNT(*) as count",
            "FROM (",
            "    SELECT MONTH(created_at) as month_val",
            "    FROM article",
            "    WHERE status = 1",
            "    AND YEAR(created_at) = #{year}",
            "    <if test='categoryId != null'>",
            "        AND EXISTS (SELECT 1 FROM article_category ac WHERE ac.article_id = article.id AND ac.category_id = #{categoryId})",
            "    </if>",
            "    <if test='tagId != null'>",
            "        AND EXISTS (SELECT 1 FROM article_tag at WHERE at.article_id = article.id AND at.tag_id = #{tagId})",
            "    </if>",
            ") t",
            "GROUP BY month_val",
            "ORDER BY month_val DESC",
            "</script>"
    })
    List<Map<String, Object>> selectMonthStats(@Param("year") Integer year,
                                             @Param("categoryId") Long categoryId,
                                             @Param("tagId") Long tagId);

    /**
     * 获取指定年月的文章列表
     */
    @Select({
            "<script>",
            "WITH article_base AS (",
            "    SELECT DISTINCT",
            "        a.id,",
            "        a.title,",
            "        DATE_FORMAT(a.created_at, '%Y-%m-%d %H:%i:%s') as createdAt,",
            "        a.view_count as viewCount",
            "    FROM article a",
            "    WHERE a.status = 1",
            "    AND YEAR(a.created_at) = #{year}",
            "    AND MONTH(a.created_at) = #{month}",
            "    <if test='categoryId != null'>",
            "        AND EXISTS (SELECT 1 FROM article_category ac WHERE ac.article_id = a.id AND ac.category_id = #{categoryId})",
            "    </if>",
            "    <if test='tagId != null'>",
            "        AND EXISTS (SELECT 1 FROM article_tag at2 WHERE at2.article_id = a.id AND at2.tag_id = #{tagId})",
            "    </if>",
            ")",
            "SELECT",
            "    ab.*,",
            "    (",
            "        SELECT JSON_ARRAYAGG(",
            "            JSON_OBJECT(",
            "                'id', c.id,",
            "                'name', c.name",
            "            )",
            "        )",
            "        FROM article_category ac",
            "        LEFT JOIN category c ON ac.category_id = c.id",
            "        WHERE ac.article_id = ab.id",
            "    ) as categories,",
            "    IF(COUNT(t.id) > 0,",
            "       JSON_ARRAYAGG(JSON_OBJECT('id', t.id, 'name', t.name)),",
            "       '[]'",
            "    ) as tags",
            "FROM article_base ab",
            "LEFT JOIN article_tag at ON ab.id = at.article_id",
            "LEFT JOIN tag t ON at.tag_id = t.id",
            "GROUP BY ab.id, ab.title, ab.createdAt, ab.viewCount",
            "ORDER BY ab.createdAt DESC",
            "</script>"
    })
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "title", column = "title"),
        @Result(property = "createdAt", column = "createdAt"),
        @Result(property = "viewCount", column = "viewCount"),
        @Result(property = "categories", column = "categories", typeHandler = com.modelbolt.blog.typehandler.JsonTypeHandler.class),
        @Result(property = "tags", column = "tags", typeHandler = com.modelbolt.blog.typehandler.JsonTypeHandler.class)
    })
    List<Map<String, Object>> selectMonthArticles(@Param("year") Integer year,
                                                @Param("month") Integer month,
                                                @Param("categoryId") Long categoryId,
                                                @Param("tagId") Long tagId);

    /**
     * 获取文章编辑信息
     */
    @Select("SELECT a.*, " +
            "GROUP_CONCAT(DISTINCT ac.category_id) as categoryIdsStr, " +
            "GROUP_CONCAT(DISTINCT at.tag_id) as tagIdsStr, " +
            "u.username as authorName, u.avatar as authorAvatar " +
            "FROM article a " +
            "LEFT JOIN article_category ac ON a.id = ac.article_id " +
            "LEFT JOIN article_tag at ON a.id = at.article_id " +
            "LEFT JOIN user u ON a.author_id = u.id " +
            "WHERE a.id = #{id} " +
            "GROUP BY a.id")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "categoryIdsStr", column = "categoryIdsStr"),
        @Result(property = "tagIdsStr", column = "tagIdsStr"),
        @Result(property = "authorName", column = "authorName"),
        @Result(property = "authorAvatar", column = "authorAvatar")
    })
    ArticleEditVO selectArticleForEdit(@Param("id") Long id);

    /**
     * 获取文章详情
     */
    @Select("SELECT a.*, u.username as authorName, u.avatar as authorAvatar " +
            "FROM article a " +
            "LEFT JOIN user u ON a.author_id = u.id " +
            "WHERE a.id = #{id} AND a.status = 1")
    Article selectArticleDetail(@Param("id") Long id);

    /**
     * 获取相关文章
     * 基于相同分类和标签推荐
     */
    @Select("WITH article_scores AS (" +
            "  SELECT DISTINCT a.id, " +
            "    CASE " +
            "      WHEN EXISTS (SELECT 1 FROM article_tag at1 " +
            "                  INNER JOIN article_tag at2 ON at1.tag_id = at2.tag_id " +
            "                  WHERE at1.article_id = a.id AND at2.article_id = #{id}) THEN 2 " +
            "      WHEN EXISTS (SELECT 1 FROM article_category ac1 " +
            "                  INNER JOIN article_category ac2 ON ac1.category_id = ac2.category_id " +
            "                  WHERE ac1.article_id = a.id AND ac2.article_id = #{id}) THEN 1 " +
            "      ELSE 0 " +
            "    END as relevance_score " +
            "  FROM article a " +
            "  WHERE a.status = 1 " +  // 只选择已发布的文章
            "  AND a.id != #{id} " +   // 排除当前文章
            ") " +
            "SELECT a.* " +
            "FROM article_scores s " +
            "INNER JOIN article a ON s.id = a.id " +
            "WHERE s.relevance_score > 0 " +  // 只返回有关联的文章
            "ORDER BY s.relevance_score DESC, a.view_count DESC " +
            "LIMIT #{limit}")
    List<Article> selectRelatedArticles(@Param("id") Long id, @Param("limit") Integer limit);

    /**
     * 更新文章浏览量
     */
    @Update("UPDATE article SET view_count = view_count + 1 WHERE id = #{id}")
    int incrementViewCount(@Param("id") Long id);

    /**
     * 更新文章点赞数
     */
    @Update("UPDATE article SET like_count = like_count + #{delta} WHERE id = #{id}")
    int updateLikeCount(@Param("id") Long id, @Param("delta") int delta);

    /**
     * 获取用户是否点赞了文章
     */
    @Select("SELECT COUNT(*) FROM article_like WHERE article_id = #{articleId} AND user_id = #{userId}")
    int checkUserLiked(@Param("articleId") Long articleId, @Param("userId") Long userId);

    /**
     * 获取用户是否收藏了文章
     */
    @Select("SELECT COUNT(*) FROM article_favorite WHERE article_id = #{articleId} AND user_id = #{userId}")
    int checkUserFavorited(@Param("articleId") Long articleId, @Param("userId") Long userId);

    @Update("UPDATE article SET like_count = like_count + 1 WHERE id = #{id}")
    int incrementLikeCount(Long id);
    
    @Update("UPDATE article SET like_count = like_count - 1 WHERE id = #{id} AND like_count > 0")
    int decrementLikeCount(Long id);
    
    @Update("UPDATE article SET favorite_count = favorite_count + 1 WHERE id = #{id}")
    int incrementFavoriteCount(Long id);
    
    @Update("UPDATE article SET favorite_count = favorite_count - 1 WHERE id = #{id} AND favorite_count > 0")
    int decrementFavoriteCount(Long id);

    /**
     * 插入点赞记录
     *
     * @param articleId 文章ID
     * @param userId 用户ID
     * @return 影响行数
     */
    @Insert("INSERT INTO user_like (article_id, user_id) VALUES (#{articleId}, #{userId})")
    int insertLike(@Param("articleId") Long articleId, @Param("userId") Long userId);

    /**
     * 插入文章分类关联
     */
    @Insert({
        "<script>",
        "INSERT INTO article_category (article_id, category_id) VALUES",
        "<foreach collection='categoryIds' item='categoryId' separator=','>",
        "(#{articleId}, #{categoryId})",
        "</foreach>",
        "</script>"
    })
    void insertArticleCategories(@Param("articleId") Long articleId, @Param("categoryIds") List<Long> categoryIds);

    /**
     * 插入文章标签关联
     */
    @Insert({
        "<script>",
        "INSERT INTO article_tag (article_id, tag_id) VALUES",
        "<foreach collection='tagIds' item='tagId' separator=','>",
        "(#{articleId}, #{tagId})",
        "</foreach>",
        "</script>"
    })
    void insertArticleTags(@Param("articleId") Long articleId, @Param("tagIds") List<Long> tagIds);

    /**
     * 删除文章分类关联
     */
    @Delete("DELETE FROM article_category WHERE article_id = #{articleId}")
    void deleteArticleCategories(@Param("articleId") Long articleId);

    /**
     * 删除文章标签关联
     */
    @Delete("DELETE FROM article_tag WHERE article_id = #{articleId}")
    void deleteArticleTags(@Param("articleId") Long articleId);
}