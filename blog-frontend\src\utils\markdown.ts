import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import { escapeHtml } from './string'

// 创建 markdown-it 实例
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  highlight: (str: string, lang: string) => {
    if (lang && hljs.getLanguage(lang)) {
      try {
        const escapedStr = escapeHtml(str)
        return hljs.highlight(escapedStr, { language: lang, ignoreIllegals: true }).value
      } catch (e) {
        console.error('Error highlighting code block:', e)
        return escapeHtml(str) // 出错时返回转义后的原始代码
      }
    }
    return escapeHtml(str) // 没有指定语言时返回转义后的原始代码
  }
})

export { md } 