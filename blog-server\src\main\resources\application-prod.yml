server:
  port: 8080
  servlet:
    context-path: /api

# 应用配置
app:
  base-url: http://************:8080/api  # 修改为您的实际域名或IP

spring:
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************
    username: root  # 修改为您的生产数据库用户名
    password: sAFhYDKMGHykHKr5  # 修改为您的生产数据库密码
  
  # Redis 配置
  data:
    redis:
      host: ************
      port: 6379
      database: 0
      clear-on-start: false  # 生产环境不要清空Redis数据

# 日志配置
logging:
  level:
    root: warn
    com.modelbolt.blog: info
  file:
    name: /var/log/modelbolt/blog-server.log