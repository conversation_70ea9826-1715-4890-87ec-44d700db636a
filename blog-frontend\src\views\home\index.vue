<!-- 首页组件 -->
<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 文章轮播 -->
    <ArticleCarousel
      v-if="featuredArticles.length > 0"
      :articles="featuredArticles"
      class="mb-8"
    />

    <div class="flex flex-col lg:flex-row gap-8">
      <!-- 主内容区 -->
      <div class="flex-1">
        <!-- 分类导航 -->
        <CategoryNav
          :categories="categories"
          v-model="currentCategory"
          @update:modelValue="handleCategoryChange"
        />

        <!-- 文章列表 -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <!-- 排序和视图切换 -->
          <div class="flex justify-between items-center mb-6">
            <div class="flex items-center space-x-4">
              <button
                v-for="sort in ['newest', 'hottest', 'recommended']"
                :key="sort"
                class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200"
                :class="[
                  currentSort === sort
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
                ]"
                @click="handleSortChange(sort)"
              >
                {{ sort === 'newest' ? '最新' : sort === 'hottest' ? '最热' : '推荐' }}
              </button>
            </div>

            <div class="flex items-center space-x-2">
              <button
                class="p-2 rounded-lg text-gray-600 hover:bg-gray-100 transition-colors duration-200"
                :class="{ 'text-blue-600': viewMode === 'card' }"
                @click="viewMode = 'card'"
              >
                <el-icon><Grid /></el-icon>
              </button>
              <button
                class="p-2 rounded-lg text-gray-600 hover:bg-gray-100 transition-colors duration-200"
                :class="{ 'text-blue-600': viewMode === 'list' }"
                @click="viewMode = 'list'"
              >
                <el-icon><List /></el-icon>
              </button>
            </div>
          </div>

          <!-- 文章列表组件 -->
          <ArticleList
            :articles="articles"
            :total="total"
            v-model="currentSort"
            :view-mode="viewMode"
            :current-page="currentPage"
            :page-size="pageSize"
            @update:current-page="handlePageChange"
            @update:page-size="handleSizeChange"
          />
        </div>
      </div>

      <!-- 侧边栏 -->
      <div class="lg:w-80 space-y-8">
        <!-- 作者信息卡片 -->
        <AuthorCard :author="author" />

        <!-- 热门文章 -->
        <HotArticles :articles="hotArticles" />

        <!-- 标签云 -->
        <TagCloud :tags="tags" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getFeaturedArticles, getArticles, getHotArticles, getCategories, getHotTags } from '@/api/article'
import type { ArticleVO, CategoryVO, TagVO, AuthorVO } from '@/types/article'
import type { R, PageResult } from '@/types/common'
import ArticleCarousel from '@/components/article/ArticleCarousel.vue'
import CategoryNav from '@/components/article/CategoryNav.vue'
import ArticleList from '@/components/article/ArticleList.vue'
import AuthorCard from '@/components/sidebar/AuthorCard.vue'
import HotArticles from '@/components/sidebar/HotArticles.vue'
import TagCloud from '@/components/sidebar/TagCloud.vue'
import defaultAvatar from '@/assets/default-avatar.png'

// 状态管理
const currentCategory = ref<number | null>(null)
const currentSort = ref<'newest' | 'hottest' | 'recommended'>('newest')
const viewMode = ref<'card' | 'list'>('card')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 数据状态
const loading = ref(false)
const featuredArticles = ref<ArticleVO[]>([])
const articles = ref<ArticleVO[]>([])
const categories = ref<CategoryVO[]>([])
const hotArticles = ref<ArticleVO[]>([])
const tags = ref<TagVO[]>([])

// 加载文章列表
const loadArticles = async () => {
  try {
    loading.value = true
    const response = await getArticles({
      categoryId: currentCategory.value || undefined,
      sort: currentSort.value,
      page: currentPage.value,
      size: pageSize.value
    })
    if (response.code === 200 && response.data) {
      articles.value = response.data.records
      total.value = response.data.total
    }
  } catch (error) {
    console.error('加载文章列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载置顶文章
const loadFeaturedArticles = async () => {
  try {
    const response = await getFeaturedArticles()
    if (response.code === 200) {
      featuredArticles.value = response.data
    }
  } catch (error) {
    console.error('加载置顶文章失败:', error)
  }
}

// 加载分类列表
const loadCategories = async () => {
  try {
    const response = await getCategories()
    if (response.code === 200) {
      categories.value = response.data
    }
  } catch (error) {
    console.error('加载分类列表失败:', error)
  }
}

// 加载热门文章
const loadHotArticles = async () => {
  try {
    const response = await getHotArticles(5)
    if (response.code === 200) {
      hotArticles.value = response.data
    }
  } catch (error) {
    console.error('加载热门文章失败:', error)
  }
}

// 加载热门标签
const loadHotTags = async () => {
  try {
    const response = await getHotTags(20)
    if (response.code === 200) {
      tags.value = response.data
    }
  } catch (error) {
    console.error('加载热门标签失败:', error)
  }
}

// 处理分类变更
const handleCategoryChange = (categoryId: number | null) => {
  currentCategory.value = categoryId
  currentPage.value = 1
  loadArticles()
}

// 处理排序方式变更
const handleSortChange = (sort: 'newest' | 'hottest' | 'recommended') => {
  currentSort.value = sort
  currentPage.value = 1
  loadArticles()
}

// 处理页码变更
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadArticles()
}

// 处理每页条数变更
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadArticles()
}

// 博主信息（这个可能需要从用户系统获取）
const author = ref<AuthorVO>({
  name: '大东喔',
  avatar: defaultAvatar,
  bio: '分享技术，记录生活',
  socials: [
    { 
      type: 'github', 
      url: 'https://github.com/dadongwo',
      icon: 'github'
    },
    { 
      type: 'qq', 
      url: 'tencent://message/?uin=2480097631',
      icon: 'qq'
    }
  ],
  stats: {
    articles: 100,
    comments: 500,
    favorites: 1000
  }
})

// 初始化数据
onMounted(async () => {
  await Promise.all([
    loadFeaturedArticles(),
    loadArticles(),
    loadCategories(),
    loadHotArticles(),
    loadHotTags()
  ])
})
</script>