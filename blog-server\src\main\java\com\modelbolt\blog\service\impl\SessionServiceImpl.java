package com.modelbolt.blog.service.impl;

import com.modelbolt.blog.model.entity.UserSession;
import com.modelbolt.blog.service.SessionService;
import com.modelbolt.blog.service.UserOnlineService;
import com.modelbolt.blog.utils.RedisUtils;
import com.modelbolt.blog.utils.JwtUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;
import java.util.Set;

@Slf4j
@Service
public class SessionServiceImpl implements SessionService {
    
    private static final String SESSION_PREFIX = "session:";
    // JWT过期时间配置
    @Value("${jwt.expiration}")
    private Long expiration;

    // JWT记住我模式下的过期时间配置
    @Value("${jwt.remember-me-expiration}")
    private Long rememberMeExpiration;
    
    private final RedisUtils redisUtils;
    private final UserOnlineService userOnlineService;
    private final JwtUtils jwtUtils;
    
    public SessionServiceImpl(RedisUtils redisUtils, UserOnlineService userOnlineService, JwtUtils jwtUtils) {
        this.redisUtils = redisUtils;
        this.userOnlineService = userOnlineService;
        this.jwtUtils = jwtUtils;
    }
    
    @Override
    public UserSession createSession(Long userId, String username, HttpServletRequest request, String token, boolean rememberMe) {
        try {
            String tokenId = jwtUtils.extractTokenId(token);
            if (tokenId == null) {
                log.warn("Failed to extract tokenId from token, using token as sessionId");
                tokenId = token;
            }
            
            UserSession session = new UserSession();
            session.setId(tokenId);
            session.setUserId(userId);
            session.setUsername(username);
            session.setIp(request.getRemoteAddr());
            session.setUserAgent(request.getHeader("User-Agent"));
            session.setLoginTime(LocalDateTime.now());
            session.setLastAccessTime(LocalDateTime.now());
            session.setRememberMe(rememberMe);
            
            // 设置过期时间与token保持一致
            long expirationSeconds = rememberMe ? rememberMeExpiration : expiration;
            session.setExpirationTime(LocalDateTime.now().plusSeconds(expirationSeconds));

            // 标记用户为在线状态
            userOnlineService.markUserOnline(userId);
            log.debug("Created session for user: {}", userId);

            // 保存session到Redis
            String key = SESSION_PREFIX + tokenId;
            redisUtils.set(key, session, expirationSeconds, TimeUnit.SECONDS);
            
            return session;
        } catch (Exception e) {
            log.error("Failed to create session for user: {}", userId, e);
            throw e;
        }
    }

    @Override
    public void updateSession(String sessionId, UserSession session) {
        try {
            String key = SESSION_PREFIX + sessionId;
            
            // 获取key的剩余过期时间
            Long ttl = redisUtils.getExpire(key);
            if (ttl == null || ttl <= 0) {
                log.warn("Session {} has expired or does not exist", sessionId);
                return;
            }
            
            // 只更新session对象,保持原有的过期时间
            redisUtils.set(key, session, ttl, TimeUnit.SECONDS);
            
            // 更新用户在线状态
            userOnlineService.updateOnlineStatus(session.getUserId());
            log.debug("Updated session for user: {}, remaining TTL: {}s", session.getUserId(), ttl);
        } catch (Exception e) {
            log.error("Failed to update session for user: {}", session.getUserId(), e);
            throw e;
        }
    }
    
    @Override
    public void removeSession(String sessionId) {
        try {
            String key = SESSION_PREFIX + sessionId;
            UserSession session = (UserSession) redisUtils.get(key);
            
            if (session != null) {
                // 检查用户是否还有其他活动会话
                String userSessionPattern = SESSION_PREFIX + "*";
                Set<String> userSessions = redisUtils.keys(userSessionPattern);
                boolean hasOtherActiveSessions = false;
                
                for (String otherSessionKey : userSessions) {
                    if (!otherSessionKey.equals(key)) {
                        UserSession otherSession = (UserSession) redisUtils.get(otherSessionKey);
                        if (otherSession != null && 
                            otherSession.getUserId().equals(session.getUserId()) &&
                            otherSession.getExpirationTime().isAfter(LocalDateTime.now())) {
                            hasOtherActiveSessions = true;
                            break;
                        }
                    }
                }
                
                // 只有在用户没有其他活动会话时才标记为离线
                if (!hasOtherActiveSessions) {
                    userOnlineService.markUserOffline(session.getUserId());
                }
                
                // 删除当前会话
                redisUtils.delete(key);
                log.debug("Removed session for user: {}, hasOtherActiveSessions: {}", session.getUserId(), hasOtherActiveSessions);
            }
        } catch (Exception e) {
            log.error("Failed to remove session: {}", sessionId, e);
            throw e;
        }
    }
    
    @Override
    public boolean validateSession(String sessionId) {
        try {
            // 1. 构造Redis中session的key
            String key = SESSION_PREFIX + sessionId;
            
            // 2. 从Redis获取session信息
            UserSession session = (UserSession) redisUtils.get(key);
            
            // 3. 检查session是否存在
            if (session == null) {
                // session不存在，说明可能已过期被Redis自动删除
                // 或者已经被手动清除(比如用户登出)
                String tokenId = jwtUtils.extractTokenId(sessionId);
                if (tokenId != null) {
                    removeSession(tokenId);
                }
                return false;
            }
            
            // 4. 检查session是否过期
            if (session.getExpirationTime() != null && 
                session.getExpirationTime().isBefore(LocalDateTime.now())) {
                // session已过期，清除session
                removeSession(sessionId);
                return false;
            }
            
            // 5. 更新session的最后访问时间
            session.setLastAccessTime(LocalDateTime.now());
            updateSession(sessionId, session);
            
            // 6. session有效
            return true;
        } catch (Exception e) {
            log.error("Failed to validate session: {}", sessionId, e);
            return false;
        }
    }
} 