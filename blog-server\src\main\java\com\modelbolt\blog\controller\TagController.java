package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.article.ArticleVO;
import com.modelbolt.blog.model.dto.tag.TagVO;
import com.modelbolt.blog.service.TagService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "标签接口", description = "标签相关接口")
@RestController
@RequestMapping("/tags")
@RequiredArgsConstructor
public class TagController {

    private final TagService tagService;

    @Operation(summary = "获取热门标签")
    @GetMapping("/hot")
    public ApiResponse<List<TagVO>> getHotTags(
            @RequestParam(defaultValue = "20") Integer limit) {
        return ApiResponse.success(tagService.getHotTags(limit));
    }

    @Operation(summary = "获取所有标签")
    @GetMapping
    public ApiResponse<List<TagVO>> getAllTags() {
        return ApiResponse.success(tagService.getAllTags());
    }

    @Operation(summary = "获取标签详情")
    @GetMapping("/{id}")
    public ApiResponse<TagVO> getTagById(
        @Parameter(description = "标签ID") @PathVariable Long id
    ) {
        return ApiResponse.success(tagService.getTagById(id));
    }

    @Operation(summary = "获取标签下的文章列表")
    @GetMapping("/{id}/articles")
    public ApiResponse<PageResult<ArticleVO>> getArticlesByTagId(
        @Parameter(description = "标签ID") @PathVariable Long id,
        @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
        @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size
    ) {
        return ApiResponse.success(tagService.getArticlesByTagId(id, page, size));
    }
} 