<template>
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
      <!-- 页面标题 -->
      <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-900">关注与粉丝</h2>
        <p class="mt-1 text-sm text-gray-500">管理你的关注列表和粉丝</p>
      </div>

      <!-- 统计信息 -->
      <div class="grid grid-cols-2 gap-4 mb-6">
        <el-card shadow="hover">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ followStats.followingCount }}</div>
            <div class="text-sm text-gray-500">关注</div>
          </div>
        </el-card>
        <el-card shadow="hover">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ followStats.followerCount }}</div>
            <div class="text-sm text-gray-500">粉丝</div>
          </div>
        </el-card>
      </div>

      <!-- 关注列表 -->
      <el-card shadow="never">
        <follow-list
          :user-id="userStore.userInfo?.id"
          @follow="handleFollow"
          @unfollow="handleUnfollow"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { getFollowStats } from '@/api/userCenter'
import type { FollowStats } from '@/types/user'
import FollowList from '@/components/user/FollowList.vue'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()

const followStats = ref<FollowStats>({
  followingCount: 0,
  followerCount: 0
})

// 加载关注统计
const loadFollowStats = async () => {
  try {
    const currentUserId = userStore.userInfo?.id
    if (!currentUserId) {
      ElMessage.warning('请先登录')
      return
    }
    const { data } = await getFollowStats(currentUserId)
    followStats.value = data
  } catch (error) {
    console.error('加载关注统计失败:', error)
    ElMessage.error('加载统计信息失败')
  }
}

// 处理关注事件
const handleFollow = () => {
  const currentUserId = userStore.userInfo?.id
  if (!currentUserId) return
  followStats.value.followingCount++
}

// 处理取消关注事件
const handleUnfollow = () => {
  const currentUserId = userStore.userInfo?.id
  if (!currentUserId) return
  followStats.value.followingCount--
}

onMounted(() => {
  const currentUserId = userStore.userInfo?.id
  if (currentUserId) {
    loadFollowStats()
  }
})
</script>

<style lang="scss" scoped>
.container {
  min-height: calc(100vh - 64px - 64px); // 减去头部和底部的高度
}
</style> 