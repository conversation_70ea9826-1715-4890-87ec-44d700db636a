package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.model.dto.share.PosterConfigDTO;
import com.modelbolt.blog.model.dto.share.ShareRecordDTO;
import com.modelbolt.blog.service.ShareService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 分享控制器
 */
@RestController
@RequestMapping("share")
@RequiredArgsConstructor
public class ShareController {

    private final ShareService shareService;
    /**
     * 生成分享海报
     */
    @PostMapping("/poster")
    public ApiResponse<String> generatePoster(@Validated @RequestBody PosterConfigDTO config) {
        return ApiResponse.success(shareService.generatePoster(config));
    }

    /**
     * 记录分享事件
     */
    @PostMapping("/record")
    public ApiResponse<Void> recordShare(@Validated @RequestBody ShareRecordDTO record) {
        shareService.recordShare(record);
        return ApiResponse.success();
    }
}