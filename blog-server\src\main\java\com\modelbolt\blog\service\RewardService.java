package com.modelbolt.blog.service;

import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.reward.RewardConfigDTO;
import com.modelbolt.blog.model.dto.reward.RewardRecordDTO;
import com.modelbolt.blog.model.entity.ArticleReward;

import java.math.BigDecimal;

public interface RewardService {
    /**
     * 获取文章打赏配置
     */
    RewardConfigDTO getRewardConfig(Long userId);

    /**
     * 更新打赏配置
     */
    void updateRewardConfig(RewardConfigDTO config);

    /**
     * 创建打赏记录
     */
    ArticleReward createReward(Long articleId, Long userId, BigDecimal amount, String paymentType, String message);

    /**
     * 获取文章打赏记录
     */
    PageResult<RewardRecordDTO> getRewardRecords(Long articleId, Integer page, Integer size);

    /**
     * 获取文章总打赏金额
     */
    BigDecimal getTotalRewardAmount(Long articleId);

    /**
     * 更新支付状态
     */
    void updatePaymentStatus(Long rewardId, String status, String transactionId);
} 