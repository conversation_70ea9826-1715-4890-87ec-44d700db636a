package com.modelbolt.blog.service;

import com.modelbolt.blog.model.dto.system.SystemConfigDTO;
import com.modelbolt.blog.model.vo.MonitoringData;
import jakarta.servlet.http.HttpServletResponse;

public interface SystemConfigService {
    /**
     * 获取系统配置
     */
    SystemConfigDTO getConfig();

    /**
     * 保存系统配置
     */
    void saveConfig(SystemConfigDTO config);

    /**
     * 测试邮件配置
     */
    void testEmailConfig();

    /**
     * 测试AI配置
     */
    void testAiConfig();

    /**
     * 获取监控数据
     */
    MonitoringData getMonitoringData();

    /**
     * 导出操作日志
     * @param response HTTP响应
     */
    void exportOperationLogs(HttpServletResponse response);
} 