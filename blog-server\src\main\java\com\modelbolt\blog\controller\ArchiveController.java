package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.model.vo.ArchiveVO;
import com.modelbolt.blog.model.vo.ArchiveStatsVO;
import com.modelbolt.blog.service.ArchiveService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "归档管理", description = "归档相关接口")
@RestController
@RequestMapping("/archives")
@RequiredArgsConstructor
public class ArchiveController {
    private final ArchiveService archiveService;

    @Operation(summary = "获取归档列表", description = "支持按年份、分类、标签筛选")
    @GetMapping
    public ApiResponse<List<ArchiveVO>> getArchives(
            @Parameter(description = "年份") @RequestParam(required = false) Integer year,
            @Parameter(description = "分类ID") @RequestParam(required = false) Long categoryId,
            @Parameter(description = "标签ID") @RequestParam(required = false) Long tagId
    ) {
        return ApiResponse.success(archiveService.getArchives(year, categoryId, tagId));
    }

    @Operation(summary = "获取归档统计信息", description = "获取文章总数、年份统计、时间范围等信息")
    @GetMapping("/stats")
    public ApiResponse<ArchiveStatsVO> getArchiveStats() {
        return ApiResponse.success(archiveService.getArchiveStats());
    }
}