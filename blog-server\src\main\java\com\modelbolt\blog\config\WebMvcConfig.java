package com.modelbolt.blog.config;

import com.modelbolt.blog.interceptor.WebsiteVisitInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Spring MVC 配置类
 * 用于配置 Spring MVC 的各种功能，如静态资源映射、拦截器、跨域等
 */
@Configuration
@RequiredArgsConstructor
public class WebMvcConfig implements WebMvcConfigurer {

    // 从配置文件中注入上传文件的存储路径
    @Value("${file.upload.path}")
    private String uploadPath;

    @Autowired
    private WebsiteVisitInterceptor websiteVisitInterceptor;

    /**
     * 配置静态资源映射
     * 这个方法用来设置静态资源的访问路径和实际存储路径的对应关系
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // API访问路径 - 直接可以通过/api/images/访问
        registry.addResourceHandler("/images/**")
                .addResourceLocations("file:" + uploadPath, "classpath:/static/images/")
                .setCachePeriod(86400);  // 缓存一天
                
        // 静态资源访问路径 - 可以通过/static/images/访问
        registry.addResourceHandler("/static/images/**")
                .addResourceLocations("file:" + uploadPath, "classpath:/static/images/")
                .setCachePeriod(86400);  // 缓存一天
        
        // 配置 Swagger UI 的资源路径，用于API文档展示
        registry.addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/springfox-swagger-ui/");
    }

    /**
     * 添加网站访问统计拦截器
     * 用于统计网站的访问量
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 保持原有的拦截器配置（如果有的话）
        WebMvcConfigurer.super.addInterceptors(registry);
        
        // 添加网站访问统计拦截器
        registry.addInterceptor(websiteVisitInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(
                    "/error",
                    "/api/**",
                    "/static/**",
                    "/swagger-ui/**",
                    "/images/**",
                    "/webjars/**"
                );
    }
} 