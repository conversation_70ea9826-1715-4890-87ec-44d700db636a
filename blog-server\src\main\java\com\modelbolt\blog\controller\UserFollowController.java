package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.user.UserFollowDTO;
import com.modelbolt.blog.service.UserFollowService;
import com.modelbolt.blog.utils.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
@Tag(name = "用户关注管理")
public class UserFollowController {

    private final UserFollowService userFollowService;

    @GetMapping("/follows")
    @Operation(summary = "获取关注/粉丝列表")
    public ApiResponse<PageResult<UserFollowDTO>> getFollowList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "12") Integer size,
            @RequestParam(defaultValue = "following") String type,
            @RequestParam(required = false) Long userId) {
        
        // 如果没有指定userId，使用当前登录用户的ID
        if (userId == null) {
            userId = SecurityUtils.getCurrentUserId();
        }
        
        log.debug("Getting {} list for user: {}, page: {}, size: {}", type, userId, page, size);
        
        PageResult<UserFollowDTO> result;
        if ("following".equals(type)) {
            result = userFollowService.getFollowingList(userId, page, size);
        } else {
            result = userFollowService.getFollowersList(userId, page, size);
        }
        
        return ApiResponse.success(result);
    }

    @GetMapping("/follow/stats/{userId}")
    @Operation(summary = "获取关注统计")
    public ApiResponse<UserFollowDTO.FollowStats> getFollowStats(@PathVariable Long userId) {
        log.debug("Getting follow stats for user: {}", userId);
        UserFollowDTO.FollowStats stats = userFollowService.getFollowStats(userId);
        return ApiResponse.success(stats);
    }

    @PostMapping("/follow/{userId}")
    @Operation(summary = "关注用户")
    public ApiResponse<Void> followUser(@PathVariable Long userId) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        log.debug("User {} following user {}", currentUserId, userId);
        boolean success = userFollowService.followUser(currentUserId, userId);
        return success ? ApiResponse.success() : ApiResponse.error("关注失败");
    }

    @DeleteMapping("/follow/{userId}")
    @Operation(summary = "取消关注")
    public ApiResponse<Void> unfollowUser(@PathVariable Long userId) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        log.debug("User {} unfollowing user {}", currentUserId, userId);
        boolean success = userFollowService.unfollowUser(currentUserId, userId);
        return success ? ApiResponse.success() : ApiResponse.error("取消关注失败");
    }

    @GetMapping("/follow/check/{userId}")
    @Operation(summary = "检查是否已关注")
    public ApiResponse<Boolean> checkIsFollowing(@PathVariable Long userId) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        log.debug("Checking if user {} is following user {}", currentUserId, userId);
        boolean isFollowing = userFollowService.isFollowing(currentUserId, userId);
        return ApiResponse.success(isFollowing);
    }
} 