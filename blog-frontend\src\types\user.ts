export interface UserInfo {
  id: number
  username: string
  email: string
  avatar: string
  role: string
  status: number
  createdAt: string
  updatedAt: string
}

export interface AuthorInfo {
  id: number
  username: string
  avatar: string
  bio: string
  stats: {
    articles: number
    followers: number
    likes: number
  }
}

export interface LoginParams {
  username: string
  password: string
  rememberMe: boolean
}

export interface RegisterParams {
  username: string
  email: string
  password: string
  verificationCode: string
  agreeTerms: boolean
}

export interface LoginResponse {
  token: string
  refreshToken: string
  user: UserInfo
}

export interface RegisterResponse {
  token: string
  refreshToken: string
  user: UserInfo
  message: string
}

/**
 * 用户设置
 */
export interface UserSettings {
  /** 主题设置 */
  theme: 'light' | 'dark'
  /** 通知开关 */
  notificationEnabled: boolean
  /** 语言设置 */
  language: string
}

/**
 * 用户资料表单
 */
export interface UserProfileForm {
  /** 用户名 */
  username: string
  /** 邮箱 */
  email: string
  /** 个人简介 */
  bio?: string
  /** 主题设置 */
  theme: 'light' | 'dark'
  /** 通知开关 */
  notificationEnabled: boolean
  /** 语言设置 */
  language: string
}

/**
 * 通用查询参数
 */
export interface QueryParams {
  page: number
  size: number
  keyword?: string
}

/**
 * 关注用户信息
 */
export interface FollowUserInfo {
  id: number
  username: string
  avatar: string
  bio: string
  isFollowing: boolean
  followTime?: string
}

/**
 * 关注列表查询参数
 */
export interface FollowQueryParams extends QueryParams {
  type: 'followers' | 'following'
}

/**
 * 关注统计
 */
export interface FollowStats {
  followingCount: number
  followerCount: number
} 