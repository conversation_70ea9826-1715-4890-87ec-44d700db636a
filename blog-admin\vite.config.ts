import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueJsx()
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    port: 3002,
    proxy: {
      // API请求代理
      '/api': {
        target: 'http://113.44.89.44:8080',
        changeOrigin: true
      },
      // 静态资源代理
      '/images': {
        target: 'http://113.44.89.44:8080/api',
        changeOrigin: true,
        rewrite: (path) => path
      },
      // 兼容/static/images路径
      '/static/images': {
        target: 'http://113.44.89.44:8080/api', 
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/static\/images/, '/images')
      }
    }
  }
})
