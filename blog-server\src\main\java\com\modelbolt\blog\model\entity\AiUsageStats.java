package com.modelbolt.blog.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.time.LocalDate;

/**
 * AI使用统计实体
 */
@Data
@Accessors(chain = true)
@TableName("ai_usage_stats")
@Schema(description = "AI使用统计实体")
public class AiUsageStats {
    @TableId(type = IdType.AUTO)
    @Schema(description = "ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "统计日期")
    private LocalDate date;

    @Schema(description = "总token数量")
    private Integer totalTokens;

    @Schema(description = "总请求次数")
    private Integer totalRequests;

    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
} 