import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { ArchiveYear, ArchiveStats, ArchiveQuery } from '@/types/archive'
import { getArchives, getArchiveStats } from '@/api/archive'

export const useArchiveStore = defineStore('archive', () => {
  // 状态
  const archives = ref<ArchiveYear[]>([])
  const stats = ref<ArchiveStats | null>(null)
  const loading = ref(false)
  const currentQuery = ref<ArchiveQuery>({
    page: 1,
    size: 10
  })

  // 筛选条件状态
  const filterState = ref<{
    year: number | undefined
    categoryId: number | undefined
    tagId: number | undefined
  }>({
    year: undefined,
    categoryId: undefined,
    tagId: undefined
  })

  // 获取归档列表
  const fetchArchives = async (query?: Partial<ArchiveQuery>) => {
    loading.value = true
    try {
      // 合并筛选条件和分页条件
      const newQuery: ArchiveQuery = { 
        ...currentQuery.value, 
        ...filterState.value,
        ...query 
      }
      currentQuery.value = newQuery
      const { data } = await getArchives(newQuery)
      archives.value = data
    } catch (error) {
      console.error('获取归档列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 获取归档统计
  const fetchStats = async () => {
    try {
      const { data } = await getArchiveStats()
      stats.value = data
    } catch (error) {
      console.error('获取归档统计失败:', error)
    }
  }

  // 更新筛选条件
  const updateFilter = (filter: Partial<typeof filterState.value>) => {
    filterState.value = {
      ...filterState.value,
      ...filter
    }
    // 更新筛选条件时重置分页
    currentQuery.value.page = 1
    return fetchArchives()
  }

  // 重置查询条件
  const resetQuery = () => {
    currentQuery.value = {
      page: 1,
      size: 10
    }
    filterState.value = {
      year: undefined,
      categoryId: undefined,
      tagId: undefined
    }
  }

  return {
    archives,
    stats,
    loading,
    currentQuery,
    filterState,
    fetchArchives,
    fetchStats,
    updateFilter,
    resetQuery
  }
}, {
  persist: {
    paths: ['filterState'] // 持久化筛选条件
  }
}) 