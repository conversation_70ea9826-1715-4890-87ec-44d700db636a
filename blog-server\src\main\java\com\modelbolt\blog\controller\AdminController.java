package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.model.dto.auth.AdminLoginResponse;
import com.modelbolt.blog.service.AdminAuthService;
import com.modelbolt.blog.service.AdminService;
import com.modelbolt.blog.utils.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/admin")
@RequiredArgsConstructor
@Tag(name = "管理员", description = "管理员相关接口")
public class AdminController {

    private final AdminAuthService adminAuthService;
    private final AdminService adminService;

    @Operation(summary = "获取管理员信息")
    @GetMapping("/info")
    public ApiResponse<AdminLoginResponse> getAdminInfo(Authentication authentication) {
        return adminAuthService.getAdminInfo(authentication.getName());
    }

    @PostMapping("/avatar")
    @Operation(summary = "更新管理员头像")
    public ApiResponse<String> updateAdminAvatar(@RequestParam("avatar") MultipartFile avatar) {
        Long adminId = SecurityUtils.getCurrentUserId();
        log.debug("Updating avatar for admin: {}", adminId);
        String avatarUrl = adminService.updateAdminAvatar(adminId, avatar);
        return ApiResponse.success(avatarUrl);
    }

    @PutMapping("/password")
    @Operation(summary = "修改管理员密码")
    public ApiResponse<Void> updateAdminPassword(@RequestBody Map<String, String> passwordData) {
        Long adminId = SecurityUtils.getCurrentUserId();
        log.debug("Updating password for admin: {}", adminId);
        
        String oldPassword = passwordData.get("oldPassword");
        String newPassword = passwordData.get("newPassword");
        
        if (oldPassword == null || newPassword == null) {
            return ApiResponse.error("密码参数不能为空");
        }
        
        adminService.updateAdminPassword(adminId, oldPassword, newPassword);
        return ApiResponse.success();
    }
}
