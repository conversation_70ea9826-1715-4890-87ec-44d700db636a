package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.model.entity.AdminSettings;
import com.modelbolt.blog.service.AdminSettingsService;
import com.modelbolt.blog.exception.UnauthorizedException;
import com.modelbolt.blog.utils.JwtUtils;
import com.modelbolt.blog.annotation.OperationLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 管理员设置控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/settings")
@RequiredArgsConstructor
@Tag(name = "管理员设置", description = "管理员设置相关接口")
public class AdminSettingsController {

    private final AdminSettingsService adminSettingsService;
    private final JwtUtils jwtUtils;

    @GetMapping
    @Operation(summary = "获取管理员设置")
    public ApiResponse<AdminSettings> getSettings(@RequestHeader("Authorization") String token) {
        if (token == null || token.isEmpty()) {
            throw new UnauthorizedException("未提供认证信息");
        }

        // 去掉 Bearer 前缀
        String actualToken = token.startsWith("Bearer ") ? token.substring(7) : token;
        
        try {
            // 从token中直接获取用户ID
            Long userId = jwtUtils.getUserIdFromToken(actualToken);
            if (userId == null) {
                throw new UnauthorizedException("无效的用户信息");
            }
            
            return adminSettingsService.getSettings(userId);
        } catch (Exception e) {
            log.error("获取管理员设置失败", e);
            throw new UnauthorizedException("获取设置失败：" + e.getMessage());
        }
    }

    @PostMapping
    @Operation(summary = "更新管理员设置")
    @OperationLog("更新管理员设置")
    public ApiResponse<AdminSettings> updateSettings(
            @RequestHeader("Authorization") String token,
            @RequestBody AdminSettings settings) {
        if (token == null || token.isEmpty()) {
            throw new UnauthorizedException("未提供认证信息");
        }

        // 去掉 Bearer 前缀
        String actualToken = token.startsWith("Bearer ") ? token.substring(7) : token;
        
        try {
            // 从token中直接获取用户ID
            Long userId = jwtUtils.getUserIdFromToken(actualToken);
            if (userId == null) {
                throw new UnauthorizedException("无效的用户信息");
            }

            if (settings == null) {
                return ApiResponse.error("设置内容不能为空");
            }

            return adminSettingsService.updateSettings(userId, settings);
        } catch (Exception e) {
            log.error("更新管理员设置失败", e);
            throw new UnauthorizedException("更新设置失败：" + e.getMessage());
        }
    }
} 