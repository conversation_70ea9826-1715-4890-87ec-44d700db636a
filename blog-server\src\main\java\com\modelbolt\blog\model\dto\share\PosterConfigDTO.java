package com.modelbolt.blog.model.dto.share;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 海报配置DTO
 */
@Data
@Schema(description = "海报配置")
public class PosterConfigDTO {
    
    @Schema(description = "文章标题")
    private String title;
    
    @Schema(description = "文章摘要")
    private String summary;
    
    @Schema(description = "文章封面图")
    private String coverImage;
    
    @Schema(description = "文章封面图")
    private String cover;
    
    @Schema(description = "作者名称")
    private String authorName;
    
    @Schema(description = "作者头像")
    private String authorAvatar;
    
    @Schema(description = "二维码内容")
    private String qrCodeContent;
} 