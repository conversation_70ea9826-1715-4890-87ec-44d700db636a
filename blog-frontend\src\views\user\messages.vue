<template>
  <div class="bg-white rounded-lg shadow-sm p-6">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-lg font-medium text-gray-900">消息通知</h2>
      <div class="space-x-4">
        <el-radio-group v-model="currentType" size="small">
          <el-radio-button value="all">全部</el-radio-button>
          <el-radio-button value="comment">评论</el-radio-button>
          <el-radio-button value="reply">回复</el-radio-button>
          <el-radio-button value="like">点赞</el-radio-button>
          <el-radio-button value="system">系统</el-radio-button>
        </el-radio-group>
        <el-button type="primary" @click="handleReadAll" class="ml-4">
          全部已读
        </el-button>
      </div>
    </div>

    <!-- 消息列表 -->
    <div class="space-y-4">
      <div 
        v-for="message in messages" 
        :key="message.id" 
        class="message-card"
        :class="[message.isRead ? 'bg-white' : 'bg-blue-50']"
      >
        <div class="flex items-start justify-between">
          <div class="flex items-start space-x-3">
            <el-avatar 
              :size="40" 
              :src="getAvatarUrl(message.senderAvatar)"
              @error="imgError"
            >
              {{ message.senderName?.[0]?.toUpperCase() }}
            </el-avatar>
            <div class="flex-1">
              <div class="flex items-center space-x-2">
                <span class="font-medium text-gray-900">{{ message.senderName }}</span>
                <span class="text-gray-600">{{ message.action }}</span>
                <router-link 
                  v-if="message.targetType === 'article' || message.targetType === 'comment'"
                  :to="'/articles/' + message.targetId"
                  class="article-link"
                >
                  {{ message.targetTitle }}
                </router-link>
              </div>
              <p class="text-gray-600 mt-1">{{ message.content }}</p>
              <div class="flex items-center space-x-4 mt-2">
                <span class="text-sm text-gray-500">{{ formatDate(message.createdAt) }}</span>
                <div class="message-actions">
                  <template v-if="!message.isRead">
                    <el-button 
                      type="primary" 
                      size="small" 
                      class="read-button"
                      @click="handleRead(message)"
                    >
                      标记已读
                    </el-button>
                  </template>
                  <el-button 
                    type="danger" 
                    size="small"
                    class="delete-button" 
                    @click="handleDelete(message)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          <el-tag 
            :type="getTagType(message.type)"
            size="small"
            class="message-tag"
          >
            {{ getTagText(message.type) }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="flex justify-center mt-6">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 30]"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDate } from '@/utils/date'
import { getAvatarUrl, useImageFallback } from '@/utils/image'
import { useNotificationStore } from '@/stores/notification'
import { 
  getNotifications, 
  markNotificationRead, 
  markAllNotificationsRead,
  deleteNotification 
} from '@/api/userCenter'
import type { NotificationVO } from '@/types/notification'

// 获取通知store
const notificationStore = useNotificationStore()

// 图片加载错误处理
const { imgError } = useImageFallback()

// 获取标签类型
const getTagType = (type: string) => {
  switch (type) {
    case 'LIKE':
      return 'success'
    case 'COMMENT':
    case 'REPLY':
      return 'primary'
    case 'SYSTEM':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取标签文本
const getTagText = (type: string) => {
  switch (type) {
    case 'LIKE':
      return '点赞'
    case 'COMMENT':
      return '评论'
    case 'REPLY':
      return '回复'
    case 'SYSTEM':
      return '系统'
    default:
      return '其他'
  }
}

// 当前消息类型
const currentType = ref<'all' | 'comment' | 'reply' | 'like' | 'system'>('all')

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 消息列表
const messages = ref<NotificationVO[]>([])
const loading = ref(false)

// 加载消息列表
const loadMessages = async () => {
  try {
    loading.value = true
    const { data } = await getNotifications({
      page: currentPage.value,
      size: pageSize.value,
      type: currentType.value,
    })
    
    if (data) {
      messages.value = data.records
      total.value = data.total
    }
  } catch (error) {
    console.error('加载消息列表失败:', error)
    ElMessage.error('加载消息列表失败')
  } finally {
    loading.value = false
  }
}

// 标记消息已读
const handleRead = async (message: NotificationVO) => {
  try {
    const { code } = await markNotificationRead(message.id)
    if (code === 200) {
      message.isRead = true
      // 更新全局通知状态
      notificationStore.refreshUnreadCount()
      ElMessage.success('已标记为已读')
    }
  } catch (error) {
    console.error('标记已读失败:', error)
    ElMessage.error('标记已读失败')
  }
}

// 全部标记已读
const handleReadAll = async () => {
  try {
    const { code } = await markAllNotificationsRead()
    if (code === 200) {
      messages.value.forEach(message => message.isRead = true)
      // 更新全局通知状态
      notificationStore.refreshUnreadCount()
      ElMessage.success('已全部标记为已读')
    }
  } catch (error) {
    console.error('标记全部已读失败:', error)
    ElMessage.error('标记全部已读失败')
  }
}

// 删除消息
const handleDelete = async (message: NotificationVO) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条消息吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const { code } = await deleteNotification(message.id)
    if (code === 200) {
      messages.value = messages.value.filter(item => item.id !== message.id)
      ElMessage.success('删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除消息失败:', error)
      ElMessage.error('删除消息失败')
    }
  }
}

// 处理分页
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  loadMessages()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadMessages()
}

// 监听消息类型变化
watch(currentType, () => {
  currentPage.value = 1
  loadMessages()
})

// 初始化数据
onMounted(() => {
  loadMessages()
})
</script>

<style scoped>
.el-radio-group {
  --el-radio-button-checked-bg-color: var(--el-color-primary);
  --el-radio-button-checked-text-color: white;
}

/* 消息卡片样式 */
.message-card {
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid var(--el-border-color-lighter);
  transition: all 0.3s ease;
  margin-bottom: 1rem;
}

.message-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 未读消息样式 */
.bg-blue-50 {
  background-color: var(--el-color-primary-light-9);
  border-left: 4px solid var(--el-color-primary);
}

/* 链接样式 */
.article-link {
  color: var(--el-color-primary);
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s ease;
}

.article-link:hover {
  color: var(--el-color-primary-dark-2);
  text-decoration: underline;
}

/* 按钮样式 */
.message-actions {
  display: flex;
  gap: 0.5rem;
}

.read-button {
  font-weight: 500;
  background-color: var(--el-color-primary);
  border: none;
  color: white;
  padding: 4px 12px;
}

.read-button:hover {
  background-color: var(--el-color-primary-dark-2);
}

.delete-button {
  font-weight: 500;
}

/* 标签样式 */
.message-tag {
  font-weight: 500;
  padding: 4px 8px;
}
</style> 