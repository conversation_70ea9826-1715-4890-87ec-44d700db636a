package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.model.dto.auth.*;
import com.modelbolt.blog.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Tag(name = "认证接口", description = "用户认证相关接口")
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;

    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public ApiResponse<LoginResponse> login(@RequestBody @Valid LoginRequest request, HttpServletRequest servletRequest) {
        return authService.login(request, servletRequest);
    }

    @Operation(summary = "用户注册")
    @PostMapping("/register")
    public ApiResponse<RegisterResponse> register(@RequestBody @Valid RegisterRequest request) {
        return authService.register(request);
    }

    @Operation(summary = "发送重置密码邮件")
    @PostMapping("/password/reset/email")
    public ApiResponse<Void> sendResetPasswordEmail(@RequestParam String email) {
        return authService.sendResetPasswordEmail(email);
    }

    @Operation(summary = "重置密码")
    @PostMapping("/password/reset")
    public ApiResponse<Void> resetPassword(@RequestBody @Valid ResetPasswordRequest request) {
        return authService.resetPassword(request);
    }

    @Operation(summary = "发送验证码")
    @PostMapping("/send-verification-code")
    public ApiResponse<Void> sendVerificationCode(@RequestBody @Valid SendVerificationCodeRequest request) {
        return authService.sendVerificationCode(request);
    }

    @Operation(summary = "检查token有效性")
    @GetMapping("/check-token")
    public ApiResponse<Void> checkToken() {
        // 如果能通过认证过滤器，说明token有效
        return ApiResponse.success();
    }

    @Operation(summary = "用户登出")
    @PostMapping("/logout")
    public ApiResponse<Void> logout(@RequestHeader(value = "Authorization", required = false) String token) {
        if (token != null && token.startsWith("Bearer ")) {
            // 移除 Bearer 前缀
            token = token.substring(7);
        }
        return authService.logout(token);
    }

    @Operation(summary = "刷新令牌")
    @PostMapping("/refresh-token")
    public ApiResponse<RefreshTokenResponse> refreshToken(@RequestBody @Valid RefreshTokenRequest request) {
        return authService.refreshToken(request);
    }
}