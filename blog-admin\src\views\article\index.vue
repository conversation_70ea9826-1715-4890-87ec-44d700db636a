<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal, Skeleton } from 'ant-design-vue'
import type { TableColumnsType } from 'ant-design-vue'
import type { Article, ArticleQuery } from '@/types/article'
import { ArticleStatus } from '@/types/article'
import { getArticleList, deleteArticle, updateArticleStatus, updateArticleTop } from '@/api/article'
import type { PageResult } from '@/types/common'
import { getAvatarUrl, getRefreshedImageUrl, getCoverUrl, useImageFallback } from '@/utils/image'

const router = useRouter()
const { imgError } = useImageFallback()

// 查询参数
const queryParams = reactive<ArticleQuery>({
  current: 1,
  size: 10,
  keyword: '',
  status: undefined
})

// 加载状态
const loading = ref(false)
// 文章列表数据
const articleData = ref<PageResult<Article>>()

// 状态选项
const statusOptions = [
  { label: '草稿', value: ArticleStatus.DRAFT },
  { label: '已发布', value: ArticleStatus.PUBLISHED },
  { label: '已下架', value: ArticleStatus.DISABLED }
]

// 图片加载状态
const imageLoadingStates = ref<Record<string, boolean>>({})

// 处理图片加载完成
const handleImageLoad = (url: string) => {
  imageLoadingStates.value[url] = false
}

// 处理图片开始加载
const handleImageLoadStart = (url: string) => {
  imageLoadingStates.value[url] = true
}

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '标题',
    dataIndex: 'title',
    width: 300,
    ellipsis: true
  },
  {
    title: '封面',
    dataIndex: 'cover',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100
  },
  {
    title: '置顶',
    dataIndex: 'isTop',
    width: 80
  },
  {
    title: '浏览量',
    dataIndex: 'viewCount',
    width: 100
  },
  {
    title: '评论数',
    dataIndex: 'commentCount',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 180
  },
  {
    title: '更新时间',
    dataIndex: 'updatedAt',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 获取文章列表
const getList = async () => {
  loading.value = true
  try {
    const { data } = await getArticleList(queryParams)
    articleData.value = data
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  queryParams.current = 1
  getList()
}

// 重置搜索
const handleReset = () => {
  queryParams.current = 1
  queryParams.keyword = ''
  queryParams.status = undefined
  getList()
}

// 分页变化
const handleTableChange = (pagination: any) => {
  queryParams.current = pagination.current
  queryParams.size = pagination.pageSize
  getList()
}

// 编辑文章
const handleEdit = (id: number) => {
  router.push(`/article/edit/${id}`)
}

// 预览文章
const handlePreview = (id: number) => {
  router.push(`/article/preview/${id}`)
}

// 删除文章
const handleDelete = (id: number) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这篇文章吗？',
    async onOk() {
      try {
        await deleteArticle(id)
        message.success('删除成功')
        getList()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 更新文章状态
const handleStatusChange = async (id: number, status: number) => {
  try {
    await updateArticleStatus(id, status)
    message.success('状态更新成功')
    getList()
  } catch (error) {
    message.error('状态更新失败')
  }
}

// 更新置顶状态
const handleTopChange = async (id: number, isTop: boolean) => {
  const targetStatus = isTop
  try {
    await updateArticleTop(id, targetStatus)
    message.success(`${targetStatus ? '置顶' : '取消置顶'}成功`)
    // 重新加载列表以获取最新状态
    await getList()
  } catch (error) {
    message.error(`${targetStatus ? '置顶' : '取消置顶'}失败`)
    console.error('更新置顶状态失败:', error)
    // 重新加载列表以恢复正确状态
    await getList()
  }
}

// 创建文章
const handleCreate = () => {
  router.push('/article/edit')
}

// 初始化加载
getList()
</script>

<template>
  <div class="article-list">
    <!-- 搜索区域 -->
    <div class="search-wrapper">
      <a-form layout="inline">
        <a-form-item label="关键词">
          <a-input
            v-model:value="queryParams.keyword"
            placeholder="请输入标题关键词"
            allow-clear
            @press-enter="handleSearch"
          />
        </a-form-item>
        <a-form-item label="状态">
          <a-select
            v-model:value="queryParams.status"
            placeholder="请选择状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option
              v-for="item in statusOptions"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">搜索</a-button>
            <a-button @click="handleReset">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="operation-wrapper">
      <a-button type="primary" @click="handleCreate">新建文章</a-button>
    </div>

    <!-- 表格区域 -->
    <a-table
      :columns="columns"
      :data-source="articleData?.records"
      :loading="loading"
      :pagination="{
        total: articleData?.total,
        current: queryParams.current,
        pageSize: queryParams.size,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => `共 ${total} 条`
      }"
      row-key="id"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'cover'">
          <div class="relative w-20 h-12">
            <!-- 加载占位 -->
            <div 
              v-if="imageLoadingStates[text]" 
              class="absolute inset-0 bg-gray-100 animate-pulse rounded"
            >
              <a-skeleton-image class="w-full h-full" />
            </div>
            
            <!-- 图片 -->
            <img
              v-if="text"
              :src="getRefreshedImageUrl(getCoverUrl(text))"
              :alt="text"
              loading="lazy"
              decoding="async"
              style="width: 80px; height: 45px; object-fit: cover;"
              @error="imgError"
              @load="handleImageLoad(text)"
              @loadstart="handleImageLoadStart(text)"
              class="rounded shadow-sm hover:shadow-md transition-shadow duration-200"
            />
            <span v-else class="text-gray-400">无封面</span>
          </div>
        </template>

        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="text === ArticleStatus.PUBLISHED ? 'green' : text === ArticleStatus.DRAFT ? 'orange' : 'red'">
            {{ statusOptions.find(item => item.value === text)?.label || text }}
          </a-tag>
        </template>

        <template v-if="column.dataIndex === 'isTop'">
          <a-switch
            :checked="Boolean(text)"
            @change="(checked) => handleTopChange(record.id, checked)"
            :loading="loading"
          >
            <template #checkedChildren>已置顶</template>
            <template #unCheckedChildren>未置顶</template>
          </a-switch>
        </template>

        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" @click="handleEdit(record.id)">编辑</a-button>
            <a-button type="link" @click="handlePreview(record.id)">预览</a-button>
            <a-button type="link" danger @click="handleDelete(record.id)">删除</a-button>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<style lang="scss" scoped>
.article-list {
  padding: 24px;

  .search-wrapper {
    margin-bottom: 24px;
    padding: 24px;
    background: #fff;
    border-radius: 2px;
  }

  .operation-wrapper {
    margin-bottom: 16px;
  }

  :deep(.ant-table-cell img) {
    border-radius: 4px;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  :deep(.ant-skeleton-image) {
    width: 100%;
    height: 100%;
  }
}
</style>