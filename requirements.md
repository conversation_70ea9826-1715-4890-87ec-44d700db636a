# 现代化个人博客系统需求文档

## 1. 项目概述

### 1.1 项目背景
开发一个集成AI能力的现代化个人博客系统，通过整合DeepSeek API实现智能化的内容创作、管理和交互功能。该系统将传统博客功能与AI能力相结合，提供更智能、更高效的博客写作和管理体验。

### 1.2 项目目标
- 构建一个美观、现代化的博客系统
- 集成AI能力，提升内容创作和管理效率
- 提供良好的用户交互体验
- 实现博客内容的智能化管理和分析

## 2. 系统架构

### 2.1 系统模块
1. 博客前台（面向读者）
2. 管理后台（面向博主）
3. 后端服务（包含AI服务集成）

### 2.2 技术栈选型
#### 博客前台（面向读者）
- 核心框架：Vue 3.0+
  - 状态管理：Pinia
  - 路由管理：Vue Router 4
  - UI组件库：Element Plus
  - 样式解决方案：TailwindCSS + SCSS
  - 构建工具：Vite 4.0+
  - 包管理器：pnpm
  - 测试框架：Vitest + Vue Test Utils
  - 代码规范：ESLint + Prettier
  - 国际化：vue-i18n
  - HTTP请求：Axios + 请求拦截器
  
#### 管理后台（面向博主）
- 核心框架：Vue 3.0+
  - 状态管理：Pinia
  - 路由管理：Vue Router 4
  - UI组件库：Ant Design Vue
  - 样式解决方案：TailwindCSS + SCSS
  - 构建工具：Vite 4.0+
  - 包管理器：pnpm
  - 富文本编辑器：TinyMCE/WangEditor
  - Markdown编辑器：MD Editor V3
  - 图表库：ECharts 5
  - 拖拽排序：Vue.Draggable
  
#### 后端服务
- 核心框架：Spring Boot 3.0+
  - 数据库：MySQL 8.0+
  - ORM框架：MyBatis Plus
  - 缓存：Redis 6.0+
  - 搜索引擎：Elasticsearch 8.0+
  - API文档：OpenAPI 3.0 + Knife4j
  - 身份认证：JWT + Redis
  - AI服务集成：DeepSeek API
  - 消息队列：Redis Stream（复用已有Redis服务）
  - 日志框架：Logback + ELK
  - 监控：Spring Boot Admin + Prometheus + Grafana
  - 单元测试：JUnit 5 + Mockito
  
#### 存储服务
- 对象存储：腾讯云 COS
  - SDK版本：cos-java-sdk-v5
  - 存储桶配置：标准存储
  - 图片处理：图片压缩、水印、裁剪

### 2.3 详细技术架构

#### 2.3.1 前端架构（博客前台和管理后台）
1. 应用架构
   - 基于Vue 3 Composition API
   - 采用组件化开发
   - 使用Vue Router实现路由管理
   - 使用Pinia进行状态管理
   
2. 状态管理
   - Pinia进行全局状态管理
   - Composables处理组件状态
   - 持久化存储用户配置
   
3. 性能优化
   - 路由级别代码分割
   - 组件懒加载
   - 图片懒加载和预加载
   - Service Worker缓存

#### 2.3.2 后端架构
1. 应用层
   - Spring Boot控制器
   - 模块化服务设计
   - 统一响应处理
   - 全局异常处理
   
2. 数据层
   - MyBatis Plus数据访问
   - Redis缓存层
   - 读写分离设计
   - 数据库连接池
   
3. 安全架构
   - JWT + Redis实现分布式Session
   - CORS跨域处理
   - Spring Security安全框架
   - Rate Limiting限流

#### 2.3.3 数据库设计
1. 用户相关表
   ```sql
   -- 用户表
   CREATE TABLE `user` (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `username` varchar(50) NOT NULL COMMENT '用户名',
     `password` varchar(100) NOT NULL COMMENT '密码',
     `email` varchar(100) NOT NULL COMMENT '邮箱',
     `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
     `role` varchar(20) NOT NULL DEFAULT 'USER' COMMENT '角色:USER,ADMIN',
     `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
     `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
     `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
     PRIMARY KEY (`id`),
     UNIQUE KEY `uk_username` (`username`),
     UNIQUE KEY `uk_email` (`email`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

   -- 用户配置表
   CREATE TABLE `user_settings` (
     `user_id` bigint NOT NULL,
     `theme` varchar(20) DEFAULT 'light' COMMENT '主题设置',
     `notification_enabled` tinyint(1) DEFAULT '1' COMMENT '通知开关',
     `language` varchar(10) DEFAULT 'zh_CN' COMMENT '语言设置',
     PRIMARY KEY (`user_id`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户配置表';
   ```

2. 内容相关表
   ```sql
   -- 文章表
   CREATE TABLE `article` (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `title` varchar(200) NOT NULL COMMENT '标题',
     `content` longtext NOT NULL COMMENT '内容',
     `author_id` bigint NOT NULL COMMENT '作者ID',
     `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态:0草稿,1发布,2下架',
     `view_count` int DEFAULT '0' COMMENT '浏览量',
     `like_count` int DEFAULT '0' COMMENT '点赞数',
     `comment_count` int DEFAULT '0' COMMENT '评论数',
     `is_top` tinyint(1) DEFAULT '0' COMMENT '是否置顶',
     `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
     `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
     PRIMARY KEY (`id`),
     KEY `idx_author` (`author_id`),
     FULLTEXT KEY `idx_title_content` (`title`,`content`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章表';

   -- 分类表
   CREATE TABLE `category` (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `name` varchar(50) NOT NULL COMMENT '分类名称',
     `parent_id` bigint DEFAULT NULL COMMENT '父分类ID',
     `order_num` int DEFAULT '0' COMMENT '排序',
     PRIMARY KEY (`id`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类表';

   -- 标签表
   CREATE TABLE `tag` (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `name` varchar(50) NOT NULL COMMENT '标签名称',
     PRIMARY KEY (`id`),
     UNIQUE KEY `uk_name` (`name`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';

   -- 文章标签关联表
   CREATE TABLE `article_tag` (
     `article_id` bigint NOT NULL,
     `tag_id` bigint NOT NULL,
     PRIMARY KEY (`article_id`,`tag_id`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章标签关联表';
   ```

3. 交互相关表
   ```sql
   -- 评论表
   CREATE TABLE `comment` (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `content` text NOT NULL COMMENT '评论内容',
     `article_id` bigint NOT NULL COMMENT '文章ID',
     `user_id` bigint NOT NULL COMMENT '评论用户ID',
     `parent_id` bigint DEFAULT NULL COMMENT '父评论ID',
     `root_id` bigint DEFAULT NULL COMMENT '根评论ID',
     `status` tinyint DEFAULT '0' COMMENT '状态:0待审核,1通过,2拒绝',
     `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
     PRIMARY KEY (`id`),
     KEY `idx_article` (`article_id`),
     KEY `idx_user` (`user_id`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

   -- 用户收藏表
   CREATE TABLE `user_favorite` (
     `user_id` bigint NOT NULL,
     `article_id` bigint NOT NULL,
     `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
     PRIMARY KEY (`user_id`,`article_id`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏表';

   -- 用户关注表
   CREATE TABLE `user_follow` (
     `follower_id` bigint NOT NULL COMMENT '关注者ID',
     `following_id` bigint NOT NULL COMMENT '被关注者ID',
     `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
     PRIMARY KEY (`follower_id`,`following_id`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户关注表';
   ```

4. 系统相关表
   ```sql
   -- 系统配置表
   CREATE TABLE `system_config` (
     `key` varchar(50) NOT NULL COMMENT '配置键',
     `value` text COMMENT '配置值',
     `description` varchar(200) DEFAULT NULL COMMENT '配置描述',
     PRIMARY KEY (`key`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

   -- 操作日志表
   CREATE TABLE `operation_log` (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `user_id` bigint DEFAULT NULL COMMENT '操作用户ID',
     `operation` varchar(50) NOT NULL COMMENT '操作类型',
     `method` varchar(200) NOT NULL COMMENT '请求方法',
     `params` text COMMENT '请求参数',
     `ip` varchar(64) DEFAULT NULL COMMENT '操作IP',
     `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
     PRIMARY KEY (`id`),
     KEY `idx_user` (`user_id`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';
   ```

5. AI 对话历史表
   ```sql
   -- AI 对话历史表
   CREATE TABLE `ai_chat_history` (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `user_id` bigint NOT NULL COMMENT '用户ID',
     `session_id` varchar(50) NOT NULL COMMENT '会话ID',
     `role` varchar(20) NOT NULL COMMENT '角色:user,assistant',
     `content` text NOT NULL COMMENT '对话内容',
     `tokens` int DEFAULT '0' COMMENT 'token数量',
     `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
     PRIMARY KEY (`id`),
     KEY `idx_user_session` (`user_id`,`session_id`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI对话历史表';

   -- AI 服务用量统计表
   CREATE TABLE `ai_usage_stats` (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `user_id` bigint NOT NULL COMMENT '用户ID',
     `service_type` varchar(50) NOT NULL COMMENT '服务类型',
     `tokens_used` int NOT NULL COMMENT '使用的token数',
     `cost` decimal(10,2) NOT NULL COMMENT '费用(元)',
     `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
     PRIMARY KEY (`id`),
     KEY `idx_user_type` (`user_id`,`service_type`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI服务用量统计表';
   ```

### 2.3.4 AI 服务集成架构

#### 2.3.4.1 DeepSeek API 集成
1. API 配置
   - Base URL: https://api.deepseek.com
   - 认证方式: Bearer Token
   - 模型选型: deepseek-chat (DeepSeek-V2.5)
   
2. 错误处理机制
   - 400: 请求格式错误处理
   - 401: API 认证失败处理 
   - 402: 余额不足提醒
   - 429: 请求速率限制处理
   - 500/503: 服务器错误重试机制

3. 性能优化
   - 使用硬盘缓存服务(自动开启)
   - 缓存命中计费: 0.1元/百万tokens
   - 非缓存计费: 1元/百万tokens
   - 请求合并与批处理
   - 长文本分段处理

4. 高可用设计
   - 失败重试策略
   - 负载均衡
   - 熔断降级机制
   - 实时监控告警

#### 2.3.4.2 AI 能力调用设计
1. 对话能力
   - 多轮对话上下文管理
   - 对话历史缓存优化
   - Stream 模式支持
   - 角色预设管理

2. 内容生成
   - JSON 结构化输出
   - Function Calling 支持
   - 前缀续写能力(Beta)
   - FIM 补全能力(Beta)

3. 安全控制
   - 输入内容过滤
   - 输出内容审核
   - 敏感信息处理
   - 用量控制

4. 监控分析
   - API 调用统计
   - 响应时间监控
   - 错误率统计
   - 费用分析

## 3. 功能需求

### 3.1 博客前台
#### 基础功能
1. 文章展示
   - 文章列表（分页、分类、标签筛选）
   - 文章详情页
   - 相关文章推荐
   
2. 互动功能
   - 文章评论
   - 点赞功能
   - 分享功能
   
3. 搜索功能
   - 全文搜索
   - 标签搜索
   - 分类搜索

#### AI增强功能
1. AI问答助手
   - 基于博客内容的智能问答
   - 上下文理解和知识关联
   
2. 智能评论
   - AI评论审核
   - 垃圾评论过滤
   - 评论情感分析

#### 用户系统
1. 登录注册
   - 账号密码登录
   - 邮箱验证
   - 找回密码功能
   
2. 个人中心
   - 个人资料管理
   - 修改密码
   - 我的评论历史
   - 我的收藏文章
   - 消息通知

3. 用户互动
   - 关注作者
   - 收藏文章
   - 评论点赞
   - @用户功能
   - 消息提醒

#### 评论系统升级
1. 评论功能
   - 支持登录用户评论
   - 评论嵌套回复
   - 评论支持 Markdown
   - 评论支持图片上传
   - 评论通知
   
2. 评论管理
   - 用户删除自己的评论
   - 评论举报功能
   - AI 智能过滤敏感内容

#### 文章阅读体验
1. 深色模式支持
2. 文章目录导航
3. 代码高亮
4. 图片预览
5. 字体大小调节
6. 阅读进度条
7. 移动端适配

#### SEO优化
1. Meta信息管理
2. Sitemap自动生成
3. 规范的URL结构
4. 页面性能优化
5. 结构化数据支持

#### 增强功能
1. 社交功能
   - 文章分享到社交媒体
   - 一键分享到微信/QQ/微博
   - 复制文章链接
   - 生成分享海报

2. 订阅功能
   - 邮件订阅
   - RSS订阅
   - 新文章提醒
   - 自定义订阅内容

3. 多语言支持
   - 中英文切换
   - 其他语言扩展接口
   - 用户行为分析
   - 首页布局
   - 自动化测试

4. 辅助功能
   - 文章朗读功能
   - 无障碍访问支持
   - 快捷键支持
   - 屏幕阅读器优化

5. 互动增强
   - 文章打赏功能
   - 粉丝徽章系统
   - 用户积分系统
   - 活跃度排行榜

### 3.2 管理后台
#### 基础功能
1. 内容管理
   - 文章管理（CRUD）
   - 分类管理
   - 标签管理
   - 评论管理
   
2. 系统管理
   - 用户管理
   // - 权限管理
   - 系统设置

#### AI增强功能
1. AI写作助手
   - 文章创意生成
   - 文章大纲生成
   - 内容优化建议
   - SEO建议生成
   
2. 内容分析
   - 文章质量评估
   - 阅读体验分析
   - 关键词提取
   - 热点话题推荐

3. 数据分析
   - 访问统计
   - 系统监控面板
   - Token 刷新机制
   - 多端登录控制
   - 导出功能

#### 登录认证
1. 管理员登录
   - 多因素认证
   - 登录日志记录
   - 异地登录提醒
   - IP 限制

2. 角色权限
   // - 超级管理员
   // - 内容管理员
   // - 评论管理员
   
3. 安全设置
   - 密码策略设置
   - 登录失败处理
   - 会话管理

#### 用户管理
1. 用户列表
   - 用户信息管理
   - 用户状态管理
   - 用户封禁/解封
   
2. 用户分析
   - 用户活跃度分析
   - 用户增长趋势
   - 用户行为分析

#### 仪表盘功能
1. 数据概览
   - 今日访问量
   - 新增用户数
   - 文章发布数
   - 评论数量
   
2. 实时监控
   - 在线用户数
   - 服务器状态
   - API调用统计
   - AI服务用量

#### 内容编辑器
1. Markdown编辑器
   - 所见即所得模式
   - 实时预览
   - 快捷键支持
   - 图片拖拽上传
   
2. AI辅助功能
   - 智能纠错
   - 标题优化建议
   - 内容扩写
   - 关键词建议

#### 扩展功能
1. 内容运营
   - 文章定时发布
   - 文章版本管理
   - 草稿箱功能
   - 文章模板管理
   
2. SEO工具
   - 关键词分析
   - 外链管理
   - 死链检测
   - 网站地图管理

3. 数据分析增强
   - 用户画像分析
   - 内容热度分析
   - 转化率分析
   - 自定义报表

4. 运营工具
   - 邮件群发
   - 消息推送
   - 活动管理
   - 用户标签管理

5. 系统工具
   - 数据库备份还原
   - 缓存管理
   - 系统日志查看
   - 性能监控面板

### 3.3 后端服务
1. 接口服务
   - RESTful API设计
   - 接口权限控制
   - 数据验证
   
2. 数据服务
   - 数据库操作
   - 缓存管理
   - 文件存储
   
3. AI服务集成
   - DeepSeek API对接
   - AI模型调用管理
   - 响应优化

#### 认证服务
1. JWT 认证
   - Token 生成和验证
   - 动态语言包加载
   - 区域化配置

2. 权限控制
   // - 基于角色的权限控制（RBAC）
   // - API 权限管理
   // - 资源访问控制

#### 文件服务
1. 腾讯云 COS 集成
   - 图片上传
   - 文件管理
   - 访问控制
   - 防盗链设置
   
2. 文件处理
   - 图片压缩
   - 图片水印
   - 文件格式验证
   - 文件大小限制

#### 缓存服务
1. Redis 缓存
   - 用户会话管理
   - 热点数据缓存
   - 接口限流
   - 验证码存储

### 3.4 AI功能细节
1. 文章创作助手
   - 主题发散
   - 大纲生成
   - 内容润色
   - SEO建议

2. 智能审核系统
   - 评论内容审核
   - 敏感信息检测
   - 垃圾内容过滤
   - 用户行为分析

3. 智能问答系统
   - 基于文章的问答
   - 上下文理解
   - 多轮对话支持
   - 知识关联推荐

4. 智能推荐系统
   - 个性化文章推荐
   - 相关文章智能匹配
   - 用户兴趣分析
   - 阅读时间预估

5. AI运营助手
   - 热点话题发现
   - 内容创作建议
   - 最佳发布时间预测
   - 用户增长建议

6. 智能客服
   - 智能问答机器人
   - 常见问题自动回复
   - 用户意图识别
   - 人工客服协助

7. 内容安全
   - 实时内容审核
   - 敏感信息识别
   - 垃圾内容过滤
   - 用户行为风险预警

### 3.5 页面布局与设计

#### 3.5.1 前台页面
1. 首页布局
   - 头部导航栏
     - Logo
     - 主导航菜单
     - 搜索框
     - 用户入口
   - 文章展示区
     - 置顶文章轮播
     - 最新文章列表
     - 热门文章推荐
   - 侧边栏
     - 作者信息
     - 分类导航
     - 标签云
     - 热门排行
   - 底部信息
     - 友情链接
     - 网站信息
     - 联系方式

2. 文章详情页
   - 文章头图
   - 文章元信息
   - 目录导航
   - 阅读进度条
   - 文章内容
   - 代码块样式
   - 评论区
   - 相关推荐

3. 个人中心
   - 用户信息卡片
   - 数据统计面板
   - 内容管理标签页
   - 消息通知中心
   - 设置面板

#### 3.5.2 后台页面
1. 管理控制台
   - 顶部导航
     - 系统Logo
     - 全局搜索
     - 消息通知
     - 用户信息
   - 侧边菜单
     - 仪表盘
     - 内容管理
     - 用户管理
     - 系统设置
   - 主内容区
     - 数据概览
     - 快捷操作
     - 待办事项

2. 内容管理页面
   - 文章管理
     - 文章列表
       - 批量操作工具栏
       - 筛选器
       - 列表展示
       - 分页控制
     - 文章编辑
       - 编辑器工具栏
       - Markdown编辑区
       - 预览区
       - 发布设置面板
   - 分类管理
     - 分类树形展示
     - 拖拽排序
     - 批量操作
   - 标签管理
     - 标签云展示
     - 使用频率统计
     - 批量操作

3. 数据分析页面
   - 数据总览
     - 关键指标卡片
     - 趋势图表
     - 实时数据
   - 用户分析
     - 用户画像
     - 行为轨迹
     - 留存分析
   - 内容分析
     - 热门文章
     - 阅读时长
     - 转化漏斗
   - 自定义报表
     - 指标选择器
     - 图表配置
     - 数据导出

4. 系统设置页面
   - 基础设置
     - 站点信息
     - SEO配置
     - 主题设置
   - 功能配置
     - 评论设置
     - 会员设置
     - 积分规则
   - 安全设置
     - 密码策略
     - 登录控制
     - IP黑名单
   - 第三方集成
     - 存储服务
     - 消息推送

### 3.6 错误处理机制

#### 3.6.1 前端错误处理
1. 全局错误捕获
   - Vue 全局错误处理器配置
   - Promise 异常统一处理
   - Console 错误监控
   - 路由错误处理
   - 资源加载错误处理

2. 用户界面反馈
   - 统一的错误提示组件
   - 错误状态页面（404、403、500等）
   - Loading 状态管理
   - 表单验证错误提示
   - Toast/Message 提示规范

3. 网络请求处理
   - Axios 请求/响应拦截器
   - 请求超时处理
   - 断网检测和提示
   - 请求重试机制
     - 重试次数配置
     - 重试间隔策略
     - 特定状态码重试
   - 并发请求控制

4. 数据容错处理
   - 数据格式校验
   - 默认值处理
   - 空值展示策略
   - 数据类型转换

#### 3.6.2 后端错误处理
1. 统一响应格式
   ```json
   {
     "code": 200,           // 状态码
     "message": "success",  // 提示信息
     "data": {},           // 业务数据
     "timestamp": "",      // 时间戳
     "traceId": ""        // 请求追踪ID
   }
   ```

2. 异常体系设计
   - 基础异常类
     - BusinessException（业务异常）
     - ValidationException（参数验证异常）
     - AuthenticationException（认证异常）
     - AuthorizationException（授权异常）
     - SystemException（系统异常）
   
   - 错误码规范
     - A0001: 用户端错误
     - B0001: 系统执行错误
     - C0001: 调用第三方服务错误

3. 日志记录规范
   - 错误日志分级
     - ERROR: 系统错误
     - WARN: 业务警告
     - INFO: 重要信息
     - DEBUG: 调试信息
   
   - 日志内容要求
     - 时间戳
     - 请求 ID
     - 用户信息
     - 错误堆栈
     - 请求参数
     - 环境信息

4. 监控告警机制
   - 实时告警
     - 关键业务异常
     - 系统性能异常
     - 安全风险告警
   
   - 告警方式
     - 邮件通知
     - 短信通知
     - 企业微信/钉钉通知
   
   - 告警级别
     - P0: 紧急（影响系统核心功能）
     - P1: 重要（影响部分功能）
     - P2: 普通（影响用户体验）
     - P3: 提示（需要关注）

5. 降级策略
   - 功能降级方案
   - 服务熔断机制
   - 限流措施
   - 兜底数据处理

#### 3.6.3 错误处理最佳实践
1. 安全性考虑
   - 敏感信息过滤
   - 错误信息脱敏
   - 生产环境堆栈信息处理
   - 错误日志访问权限控制

2. 用户体验优化
   - 友好的错误提示文案
   - 引导性的解决方案
   - 错误状态自动恢复
   - 离线模式支持

3. 开发规范
   - 错误边界处理
   - 异常捕获规范
   - 错误码使用规范
   - 日志记录规范

4. 错误分析与复盘
   - 错误统计分析
   - 定期错误回顾
   - 优化改进机制
   - 错误处理文档维护

#### 3.6.4 AI 服务错误处理
1. API 调用错误
   - 模型调用超时处理
   - Token 额度不足预警
   - 并发限制处理
   - 服务降级方案

2. 内容处理错误
   - 输入内容长度限制
   - 敏感内容过滤失败处理
   - 生成内容审核失败处理
   - 格式校验失败处理

3. 业务降级方案
   - AI 服务不可用时的降级策略
   - 本地缓存备用方案
   - 基础功能保障机制
   - 用户友好提示
