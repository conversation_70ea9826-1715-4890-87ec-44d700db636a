/**
 * 分类信息
 */
export interface CategoryVO {
  /** ID */
  id: number
  /** 名称 */
  name: string
  /** 父分类ID */
  parentId?: number | null
  /** 排序号 */
  orderNum: number
  /** 文章数量 */
  articleCount: number
  /** 子分类列表 */
  children?: CategoryVO[]
}

/**
 * 分类查询参数
 */
export interface CategoryQuery {
  /** 关键词 */
  keyword?: string
  /** 父分类ID */
  parentId?: number | null
  /** 页码 */
  pageNum?: number
  /** 每页大小 */
  pageSize?: number
}

/**
 * 分类树形结构
 */
export interface Category extends CategoryVO {
  children?: Category[]
  isEditing?: boolean
}

/**
 * 分类表单状态
 */
export interface CategoryFormState {
  id?: number
  name: string
  parentId?: number | null
  orderNum: number
} 