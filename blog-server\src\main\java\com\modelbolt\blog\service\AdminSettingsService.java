package com.modelbolt.blog.service;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.model.entity.AdminSettings;

/**
 * 管理员设置服务接口
 */
public interface AdminSettingsService {
    /**
     * 获取管理员设置
     *
     * @param adminId 管理员ID
     * @return 设置信息
     */
    ApiResponse<AdminSettings> getSettings(Long adminId);

    /**
     * 更新管理员设置
     *
     * @param adminId 管理员ID
     * @param settings 设置信息
     * @return 更新结果
     */
    ApiResponse<AdminSettings> updateSettings(Long adminId, AdminSettings settings);
} 