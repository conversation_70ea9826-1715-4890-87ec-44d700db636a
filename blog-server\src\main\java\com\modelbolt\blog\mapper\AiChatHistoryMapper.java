package com.modelbolt.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.modelbolt.blog.model.entity.AiChatHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface AiChatHistoryMapper extends BaseMapper<AiChatHistory> {
    
    @Select("SELECT * FROM ai_chat_history " +
            "WHERE user_id = #{userId} AND created_at >= #{startTime} " +
            "ORDER BY created_at DESC")
    List<AiChatHistory> findByUserIdAndTimeRange(@Param("userId") Long userId, 
                                                @Param("startTime") LocalDateTime startTime);
    
    @Select("SELECT COALESCE(SUM(tokens), 0) FROM ai_chat_history " +
            "WHERE user_id = #{userId} AND created_at >= #{startTime}")
    Long sumTokensByUserIdAndTimeRange(@Param("userId") Long userId, 
                                     @Param("startTime") LocalDateTime startTime);
    
    @Select("SELECT COUNT(*) FROM ai_chat_history " +
            "WHERE user_id = #{userId} AND created_at >= #{startTime}")
    Long countByUserIdAndTimeRange(@Param("userId") Long userId, 
                                 @Param("startTime") LocalDateTime startTime);
} 