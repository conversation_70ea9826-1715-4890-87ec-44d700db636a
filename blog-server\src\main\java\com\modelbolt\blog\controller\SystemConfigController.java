package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.model.dto.system.SystemConfigDTO;
import com.modelbolt.blog.model.vo.MonitoringData;
import com.modelbolt.blog.service.SystemConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.*;
import com.modelbolt.blog.annotation.OperationLog;

@Slf4j
@Tag(name = "系统配置接口", description = "系统配置管理相关接口")
@RestController
@RequestMapping("/system")
@RequiredArgsConstructor
public class SystemConfigController {

    private final SystemConfigService systemConfigService;

    @Operation(summary = "获取系统配置", description = "获取系统所有配置信息")
    @GetMapping("/config")
    public ApiResponse<SystemConfigDTO> getConfig() {
        return ApiResponse.success(systemConfigService.getConfig());
    }

    @Operation(summary = "保存系统配置", description = "保存或更新系统配置信息")
    @PostMapping("/config")
    @OperationLog("保存系统配置")
    public ApiResponse<Void> saveConfig(@RequestBody SystemConfigDTO config) {
        systemConfigService.saveConfig(config);
        return ApiResponse.success();
    }

    @Operation(summary = "测试邮件配置", description = "发送测试邮件验证邮件配置是否正确")
    @PostMapping("/config/test-email")
    @OperationLog("测试邮件配置")
    public ApiResponse<Void> testEmailConfig() {
        systemConfigService.testEmailConfig();
        return ApiResponse.success();
    }

    @Operation(summary = "测试AI配置", description = "测试AI服务配置是否正确")
    @PostMapping("/config/test-ai")
    @OperationLog("测试AI配置")
    public ApiResponse<Void> testAiConfig() {
        systemConfigService.testAiConfig();
        return ApiResponse.success();
    }

    @Operation(summary = "获取监控数据", description = "获取系统监控和统计数据")
    @GetMapping("/monitoring")
    public ApiResponse<MonitoringData> getMonitoringData() {
        return ApiResponse.success(systemConfigService.getMonitoringData());
    }

    @GetMapping("/operation-logs/export")
    @Operation(summary = "导出操作日志", description = "导出系统操作日志为Excel文件")
    @OperationLog("导出操作日志")
    public void exportOperationLogs(HttpServletResponse response) {
        log.debug("Starting operation logs export...");
        try {
            systemConfigService.exportOperationLogs(response);
            log.debug("Operation logs export completed successfully");
        } catch (Exception e) {
            log.error("Failed to export operation logs. Error: {}", e.getMessage(), e);
            throw e;
        }
    }
} 