package com.modelbolt.blog.model.dto.category;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "分类视图对象")
public class CategoryVO {
    @Schema(description = "分类ID")
    private Long id;

    @Schema(description = "分类名称")
    private String name;

    @Schema(description = "文章数量")
    private Integer articleCount;

    @Schema(description = "父分类ID")
    private Long parentId;

    @Schema(description = "排序号")
    private Integer orderNum;

    @Schema(description = "子分类列表")
    private List<CategoryVO> children;
} 