package com.modelbolt.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.modelbolt.blog.model.entity.AiUsageStats;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface AiUsageStatsMapper extends BaseMapper<AiUsageStats> {
    
    @Select("SELECT COALESCE(SUM(total_tokens), 0) FROM ai_usage_stats " +
            "WHERE user_id = #{userId} AND date >= #{startDate} AND date <= #{endDate}")
    Long sumTokensByDateRange(@Param("userId") Long userId, 
                            @Param("startDate") LocalDate startDate, 
                            @Param("endDate") LocalDate endDate);
    
    @Select("SELECT COALESCE(SUM(total_requests), 0) FROM ai_usage_stats " +
            "WHERE user_id = #{userId} AND date >= #{startDate} AND date <= #{endDate}")
    Long sumRequestsByDateRange(@Param("userId") Long userId, 
                              @Param("startDate") LocalDate startDate, 
                              @Param("endDate") LocalDate endDate);
    
    @Select("SELECT * FROM ai_usage_stats " +
            "WHERE user_id = #{userId} AND date >= #{startDate} AND date <= #{endDate} " +
            "ORDER BY date DESC")
    List<AiUsageStats> findByDateRange(@Param("userId") Long userId, 
                                      @Param("startDate") LocalDate startDate, 
                                      @Param("endDate") LocalDate endDate);
    
    @Select("SELECT * FROM ai_usage_stats WHERE user_id = #{userId} AND date = #{date}")
    AiUsageStats findByUserIdAndDate(@Param("userId") Long userId, @Param("date") LocalDate date);
} 