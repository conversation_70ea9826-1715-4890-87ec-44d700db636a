package com.modelbolt.blog.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.modelbolt.blog.model.dto.share.PosterConfigDTO;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 海报生成工具类
 */
@Slf4j
@Component
public class PosterUtil {

    @Value("${app.base-url}")
    private String baseUrl;

    @Value("${file.upload.path}")
    private String uploadPath;

    private static final int POSTER_WIDTH = 750;
    private static final int POSTER_HEIGHT = 1334;
    private static final int MARGIN = 40;
    private static final int AVATAR_SIZE = 80;
    private static final int QRCODE_SIZE = 200;
    private static final String FONT_NAME = "Microsoft YaHei";
    private static final int CONNECTION_TIMEOUT = 5000; // 5秒
    private static final int READ_TIMEOUT = 5000; // 5秒

    private BufferedImage loadImageFromUrl(String imageUrl) throws IOException {
        log.debug("Attempting to load image from URL: {}", imageUrl);
        URL url = new URL(imageUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setConnectTimeout(CONNECTION_TIMEOUT);
        connection.setReadTimeout(READ_TIMEOUT);
        try {
            return ImageIO.read(connection.getInputStream());
        } finally {
            connection.disconnect();
        }
    }

    private BufferedImage loadImage(String imagePath) throws IOException {
        if (imagePath == null) {
            throw new IOException("Image path is null");
        }

        if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
            return loadImageFromUrl(imagePath);
        }

        // 处理相对路径
        if (imagePath.startsWith("/")) {
            // 首先尝试本地文件系统
            File localFile = new File(uploadPath + imagePath.substring("/images/".length()));
            if (localFile.exists()) {
                log.debug("Loading image from local file: {}", localFile.getAbsolutePath());
                return ImageIO.read(localFile);
            }
            // 如果本地文件不存在，尝试通过URL
            String fullUrl = baseUrl + "/api" + imagePath;
            log.debug("Local file not found, trying URL: {}", fullUrl);
            return loadImageFromUrl(fullUrl);
        }

        throw new IOException("Unsupported image path format: " + imagePath);
    }

    public String generatePoster(PosterConfigDTO config) {
        try {
            // 创建海报画布
            BufferedImage poster = new BufferedImage(POSTER_WIDTH, POSTER_HEIGHT, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = poster.createGraphics();

            // 设置背景颜色
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, POSTER_WIDTH, POSTER_HEIGHT);

            // 绘制封面图片
            if (config.getCover() != null) {
                try {
                    BufferedImage cover = loadImage(config.getCover());
                    if (cover != null) {
                        int coverHeight = (int) (POSTER_WIDTH * 0.6);
                        g2d.drawImage(cover, 0, 0, POSTER_WIDTH, coverHeight, null);
                    } else {
                        log.error("Failed to load cover image: null image returned");
                        drawDefaultCover(g2d);
                    }
                } catch (Exception e) {
                    log.error("Failed to load cover image: {}", config.getCover(), e);
                    drawDefaultCover(g2d);
                }
            }

            // 绘制标题
            g2d.setColor(Color.BLACK);
            g2d.setFont(new Font(FONT_NAME, Font.BOLD, 32));
            drawMultiLineText(g2d, config.getTitle(), MARGIN, 300, POSTER_WIDTH - MARGIN * 2);

            // 绘制摘要
            if (config.getSummary() != null) {
                g2d.setFont(new Font(FONT_NAME, Font.PLAIN, 24));
                g2d.setColor(new Color(102, 102, 102));
                drawMultiLineText(g2d, config.getSummary(), MARGIN, 400, POSTER_WIDTH - MARGIN * 2);
            }

            // 绘制作者头像
            if (config.getAuthorAvatar() != null) {
                try {
                    BufferedImage avatar = loadImage(config.getAuthorAvatar());
                    if (avatar != null) {
                        avatar = Thumbnails.of(avatar)
                            .size(AVATAR_SIZE, AVATAR_SIZE)
                            .asBufferedImage();
                        g2d.drawImage(makeRoundedCorner(avatar, AVATAR_SIZE), MARGIN, POSTER_HEIGHT - 200, null);
                    } else {
                        log.error("Failed to load avatar image: {}", config.getAuthorAvatar());
                        g2d.setColor(Color.GRAY);
                        g2d.fillOval(MARGIN, POSTER_HEIGHT - 200, AVATAR_SIZE, AVATAR_SIZE);
                    }
                } catch (Exception e) {
                    log.error("Failed to load avatar image: {}", config.getAuthorAvatar(), e);
                    g2d.setColor(Color.GRAY);
                    g2d.fillOval(MARGIN, POSTER_HEIGHT - 200, AVATAR_SIZE, AVATAR_SIZE);
                }
            }

            // 绘制作者名称
            g2d.setFont(new Font(FONT_NAME, Font.BOLD, 24));
            g2d.setColor(Color.BLACK);
            g2d.drawString(config.getAuthorName(), MARGIN + AVATAR_SIZE + 20, POSTER_HEIGHT - 160);

            // 绘制二维码
            BufferedImage qrCode = generateQRCode(config.getQrCodeContent(), QRCODE_SIZE);
            g2d.drawImage(qrCode, POSTER_WIDTH - QRCODE_SIZE - MARGIN, POSTER_HEIGHT - QRCODE_SIZE - MARGIN, null);

            g2d.dispose();

            // 转换为字节数组
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ImageIO.write(poster, "png", os);

            // 添加Base64编码
            return "data:image/png;base64," + Base64.getEncoder().encodeToString(os.toByteArray());
        } catch (IOException | WriterException e) {
            log.error("生成海报失败", e);
            throw new RuntimeException("生成海报失败", e);
        }
    }

    private static void drawMultiLineText(Graphics2D g2d, String text, int x, int y, int maxWidth) {
        FontMetrics metrics = g2d.getFontMetrics();
        int lineHeight = metrics.getHeight();
        String[] words = text.split(" ");
        StringBuilder line = new StringBuilder();

        for (String word : words) {
            if (metrics.stringWidth(line + " " + word) < maxWidth) {
                line.append(word).append(" ");
            } else {
                g2d.drawString(line.toString(), x, y);
                y += lineHeight;
                line = new StringBuilder(word + " ");
            }
        }
        g2d.drawString(line.toString(), x, y);
    }

    private static BufferedImage generateQRCode(String content, int size) throws WriterException {
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.MARGIN, 1);

        QRCodeWriter writer = new QRCodeWriter();
        return MatrixToImageWriter.toBufferedImage(
            writer.encode(content, BarcodeFormat.QR_CODE, size, size, hints)
        );
    }

    private static BufferedImage makeRoundedCorner(BufferedImage image, int cornerRadius) {
        int w = image.getWidth();
        int h = image.getHeight();
        BufferedImage output = new BufferedImage(w, h, BufferedImage.TYPE_INT_ARGB);

        Graphics2D g2 = output.createGraphics();
        g2.setComposite(AlphaComposite.Src);
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2.setColor(Color.WHITE);
        g2.fill(new RoundRectangle2D.Float(0, 0, w, h, cornerRadius, cornerRadius));
        g2.setComposite(AlphaComposite.SrcAtop);
        g2.drawImage(image, 0, 0, null);
        g2.dispose();

        return output;
    }

    private void drawDefaultCover(Graphics2D g2d) {
        // 绘制渐变背景代替纯灰
        GradientPaint gradient = new GradientPaint(
            0, 0, new Color(200, 200, 200),
            POSTER_WIDTH, (int) (POSTER_WIDTH * 0.6), new Color(150, 150, 150)
        );
        g2d.setPaint(gradient);
        g2d.fillRect(0, 0, POSTER_WIDTH, (int) (POSTER_WIDTH * 0.6));
    }
}
