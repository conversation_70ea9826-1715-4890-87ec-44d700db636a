package com.modelbolt.blog.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户关注实体
 */
@Data
@TableName("user_follow")
@Schema(description = "用户关注实体")
public class UserFollow {
    
    @Schema(description = "关注者ID")
    @TableId(value = "follower_id")
    private Long followerId;

    @Schema(description = "被关注者ID")
    @TableField("following_id")
    private Long followingId;

    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
} 