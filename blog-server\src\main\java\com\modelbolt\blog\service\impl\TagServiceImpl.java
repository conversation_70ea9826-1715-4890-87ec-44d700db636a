package com.modelbolt.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.article.ArticleVO;
import com.modelbolt.blog.model.dto.tag.TagVO;
import com.modelbolt.blog.model.dto.tag.CreateTagDTO;
import com.modelbolt.blog.model.entity.Article;
import com.modelbolt.blog.model.entity.Tag;
import com.modelbolt.blog.exception.business.TagException;
import com.modelbolt.blog.mapper.TagMapper;
import com.modelbolt.blog.service.TagService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class TagServiceImpl implements TagService {

    private final TagMapper tagMapper;

    @Override
    public List<TagVO> getHotTags(Integer limit) {
        return tagMapper.selectHotTags(limit);
    }

    @Override
    public List<TagVO> getAllTags() {
        // 获取所有标签并按文章数量降序排序
        return tagMapper.selectAllTags();
    }

    @Override
    public TagVO getTagById(Long id) {
        Tag tag = tagMapper.selectById(id);
        if (tag == null) {
            throw new TagException.TagNotFound();
        }
        return tagMapper.selectTagById(id);
    }

    @Override
    public PageResult<ArticleVO> getArticlesByTagId(Long tagId, Integer page, Integer size) {
        // 先检查标签是否存在
        Tag tag = tagMapper.selectById(tagId);
        if (tag == null) {
            throw new TagException.TagNotFound();
        }

        // 获取标签下的文章列表
        Page<Article> pageParam = new Page<>(page, size);
        IPage<Article> articlePage = tagMapper.getArticlesByTagId(pageParam, tagId);

        // 转换为VO
        List<ArticleVO> records = articlePage.getRecords().stream()
                .map(article -> {
                    ArticleVO vo = new ArticleVO();
                    BeanUtils.copyProperties(article, vo);
                    return vo;
                })
                .collect(Collectors.toList());

        return new PageResult<>(records, articlePage.getTotal(), page, size);
    }

    @Override
    public List<TagVO> getTagsByArticleId(Long articleId) {
        return tagMapper.selectTagsByArticleId(articleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TagVO createTag(CreateTagDTO dto) {
        // 检查标签名称是否重复
        if (isTagNameExists(dto.getName())) {
            throw new TagException.DuplicateName();
        }

        // 创建标签
        Tag tag = new Tag();
        tag.setName(dto.getName());
        tagMapper.insert(tag);

        return convertToVO(tag);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TagVO updateTag(Long id, CreateTagDTO dto) {
        // 检查标签是否存在
        Tag tag = tagMapper.selectById(id);
        if (tag == null) {
            throw new TagException.TagNotFound();
        }

        // 如果名称有变化，检查是否重复
        if (!tag.getName().equals(dto.getName()) && isTagNameExists(dto.getName())) {
            throw new TagException.DuplicateName();
        }

        // 更新标签
        tag.setName(dto.getName());
        tagMapper.updateById(tag);

        return convertToVO(tag);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTag(Long id) {
        // 检查标签是否存在
        Tag tag = tagMapper.selectById(id);
        if (tag == null) {
            throw new TagException.TagNotFound();
        }

        // 检查是否有关联的文章
        Integer articleCount = tagMapper.countArticles(id);
        if (articleCount > 0) {
            throw new TagException.HasArticles();
        }

        // 删除标签
        tagMapper.deleteById(id);
    }

    private TagVO convertToVO(Tag tag) {
        TagVO vo = new TagVO();
        BeanUtils.copyProperties(tag, vo);
        
        // 获取标签下的文章数量
        Integer articleCount = tagMapper.countArticles(tag.getId());
        vo.setArticleCount(articleCount);
        
        return vo;
    }

    private boolean isTagNameExists(String name) {
        LambdaQueryWrapper<Tag> wrapper = new LambdaQueryWrapper<Tag>()
            .eq(Tag::getName, name);
        return tagMapper.selectCount(wrapper) > 0;
    }
} 