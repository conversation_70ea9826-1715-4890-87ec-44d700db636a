package com.modelbolt.blog.service;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.model.dto.auth.AdminLoginRequest;
import com.modelbolt.blog.model.dto.auth.AdminLoginResponse;
import com.modelbolt.blog.model.dto.auth.RefreshTokenRequest;
import com.modelbolt.blog.model.dto.auth.RefreshTokenResponse;
import jakarta.servlet.http.HttpServletRequest;

public interface AdminAuthService {
    /**
     * 管理员登录
     */
    ApiResponse<AdminLoginResponse> login(AdminLoginRequest request, HttpServletRequest servletRequest);

    /**
     * 发送验证码
     */
    ApiResponse<Void> sendVerificationCode(String email);

    /**
     * 管理员登出
     */
    ApiResponse<Void> logout(String token);

    /**
     * 获取管理员信息
     */
    ApiResponse<AdminLoginResponse> getAdminInfo(String username);

    /**
     * 刷新令牌
     */
    ApiResponse<RefreshTokenResponse> refreshToken(RefreshTokenRequest request);
} 