package com.modelbolt.blog.service.impl;

import com.modelbolt.blog.model.entity.UserSession;
import com.modelbolt.blog.service.UserOnlineService;
import com.modelbolt.blog.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserOnlineServiceImpl implements UserOnlineService {
    
    private static final String SESSION_PREFIX = "session:";
    private static final String ONLINE_USERS_KEY = "online:users:count";
    private static final String ONLINE_USERS_SET_KEY = "online:users:set";
    
    // 本地缓存，减少Redis访问
    private final ConcurrentHashMap<Long, Boolean> localOnlineCache = new ConcurrentHashMap<>();
    
    private final RedisUtils redisUtils;
    
    public UserOnlineServiceImpl(RedisUtils redisUtils) {
        this.redisUtils = redisUtils;
    }
    
    @Override
    public void updateOnlineStatus(Long userId) {
        if (userId == null) return;
        
        // 仅更新用户活动状态，不影响计数
        log.debug("用户 {} 活动状态已更新", userId);
    }
    
    @Override
    public void markUserOnline(Long userId) {
        if (userId == null) return;
        
        try {
            // 使用Redis Set记录在线用户ID，自动去重
            boolean added = redisUtils.setAdd(ONLINE_USERS_SET_KEY, userId);
            
            // 只有新用户才增加计数
            if (added) {
                Long count = redisUtils.increment(ONLINE_USERS_KEY, 1L);
                log.debug("用户 {} 上线，当前在线用户数: {}", userId, count);
                
                // 更新本地缓存
                localOnlineCache.put(userId, true);
            }
        } catch (Exception e) {
            log.error("标记用户 {} 在线状态失败", userId, e);
        }
    }
    
    @Override
    public void markUserOffline(Long userId) {
        if (userId == null) return;
        
        try {
            // 检查用户是否真的没有任何活跃会话了
            if (!hasActiveSession(userId)) {
                // 从在线用户集合中移除
                boolean removed = redisUtils.setRemove(ONLINE_USERS_SET_KEY, userId);
                
                // 只有当用户确实在线时才减少计数
                if (removed) {
                    Long count = redisUtils.decrement(ONLINE_USERS_KEY, 1L);
                    if (count < 0) {
                        log.warn("在线用户数为负数 ({}), 重置为0", count);
                        redisUtils.set(ONLINE_USERS_KEY, 0);
                    }
                    log.debug("用户 {} 离线，当前在线用户数: {}", userId, Math.max(count, 0));
                    
                    // 更新本地缓存
                    localOnlineCache.remove(userId);
                }
            }
        } catch (Exception e) {
            log.error("标记用户 {} 离线状态失败", userId, e);
        }
    }
    
    /**
     * 检查用户是否有活跃会话
     * 优化的会话检查方法，通过筛选会话名称缩小查询范围
     */
    private boolean hasActiveSession(Long userId) {
        if (userId == null) return false;
        
        try {
            // 使用用户特定的会话模式提高效率
            String userSessionPattern = SESSION_PREFIX + "*";
            Set<String> allSessionKeys = redisUtils.keys(userSessionPattern);
            
            if (allSessionKeys == null || allSessionKeys.isEmpty()) {
                return false;
            }
            
            for (String sessionKey : allSessionKeys) {
                UserSession session = (UserSession) redisUtils.get(sessionKey);
                if (session != null && 
                    userId.equals(session.getUserId()) && 
                    session.getExpirationTime().isAfter(LocalDateTime.now())) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("检查用户 {} 会话状态失败", userId, e);
            return false;
        }
    }
    
    @Override
    public boolean isUserOnline(Long userId) {
        if (userId == null) return false;
        
        // 先检查本地缓存，减少Redis访问
        Boolean cachedStatus = localOnlineCache.get(userId);
        if (cachedStatus != null) {
            return cachedStatus;
        }
        
        // 从Redis中获取在线状态
        boolean isOnline = redisUtils.setContains(ONLINE_USERS_SET_KEY, userId);
        
        // 如果Redis中显示在线，再确认是否有活跃会话
        if (isOnline && !hasActiveSession(userId)) {
            // 会话已过期但状态未更新，执行修正
            markUserOffline(userId);
            return false;
        }
        
        // 更新本地缓存
        localOnlineCache.put(userId, isOnline);
        return isOnline;
    }
    
    @Override
    public int getOnlineUsersCount() {
        try {
            // 优先从在线用户集合大小获取准确数据
            Long setSize = redisUtils.setSize(ONLINE_USERS_SET_KEY);
            if (setSize != null) {
                // 更新计数值，确保同步
                redisUtils.set(ONLINE_USERS_KEY, setSize);
                return setSize.intValue();
            }
            
            // 回退到计数值
            Object count = redisUtils.get(ONLINE_USERS_KEY);
            return count == null ? 0 : Integer.parseInt(count.toString());
        } catch (Exception e) {
            log.error("获取在线用户数失败", e);
            return 0;
        }
    }
    
    /**
     * 定期校准在线用户计数
     * 每10分钟执行一次
     */
    @Scheduled(fixedRate = 600000)
    public void recalibrateOnlineCount() {
        try {
            log.debug("开始校准在线用户计数...");
            
            // 获取当前所有用户会话
            Set<String> sessionKeys = redisUtils.keys(SESSION_PREFIX + "*");
            Set<Long> activeUserIds = new HashSet<>();
            
            if (sessionKeys != null && !sessionKeys.isEmpty()) {
                // 收集所有有效会话的用户ID
                for (String sessionKey : sessionKeys) {
                    UserSession session = (UserSession) redisUtils.get(sessionKey);
                    if (session != null && 
                        session.getUserId() != null && 
                        session.getExpirationTime().isAfter(LocalDateTime.now())) {
                        activeUserIds.add(session.getUserId());
                    }
                }
            }
            
            // 获取当前记录的在线用户
            Set<Object> onlineUserSet = redisUtils.setMembers(ONLINE_USERS_SET_KEY);
            Set<Long> onlineUserIds = onlineUserSet == null ? new HashSet<>() : 
                                      onlineUserSet.stream()
                                          .map(obj -> Long.parseLong(obj.toString()))
                                          .collect(Collectors.toSet());
            
            // 找出需要添加和移除的用户
            Set<Long> usersToAdd = new HashSet<>(activeUserIds);
            usersToAdd.removeAll(onlineUserIds);
            
            Set<Long> usersToRemove = new HashSet<>(onlineUserIds);
            usersToRemove.removeAll(activeUserIds);
            
            // 更新在线用户集合
            for (Long userId : usersToAdd) {
                redisUtils.setAdd(ONLINE_USERS_SET_KEY, userId);
            }
            
            for (Long userId : usersToRemove) {
                redisUtils.setRemove(ONLINE_USERS_SET_KEY, userId);
            }
            
            // 更新在线用户计数
            int actualOnlineCount = activeUserIds.size();
            redisUtils.set(ONLINE_USERS_KEY, actualOnlineCount);
            
            // 更新本地缓存
            localOnlineCache.clear();
            for (Long userId : activeUserIds) {
                localOnlineCache.put(userId, true);
            }
            
            log.debug("在线用户计数校准完成，当前在线用户数: {}，添加: {}，移除: {}", 
                    actualOnlineCount, usersToAdd.size(), usersToRemove.size());
        } catch (Exception e) {
            log.error("校准在线用户计数失败", e);
        }
    }
    
    @Override
    public Set<Long> getOnlineUserIds() {
        try {
            Set<Object> onlineSet = redisUtils.setMembers(ONLINE_USERS_SET_KEY);
            if (onlineSet == null || onlineSet.isEmpty()) {
                return new HashSet<>();
            }
            
            return onlineSet.stream()
                .map(obj -> Long.parseLong(obj.toString()))
                .collect(Collectors.toSet());
        } catch (Exception e) {
            log.error("获取在线用户ID列表失败", e);
            return new HashSet<>();
        }
    }
} 