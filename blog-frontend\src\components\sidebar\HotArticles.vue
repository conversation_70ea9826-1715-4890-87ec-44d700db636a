<!-- 热门文章组件 -->
<template>
  <div class="bg-white rounded-lg shadow-sm p-6">
    <h3 class="text-lg font-bold mb-4 flex items-center">
      <el-icon class="mr-2 text-blue-600"><Star /></el-icon>
      热门文章
    </h3>
    <div class="space-y-4">
      <router-link 
        v-for="(article, index) in articles" 
        :key="article.id"
        :to="'/articles/' + article.id" 
        class="group block"
      >
        <div class="flex items-start">
          <span 
            class="flex-shrink-0 w-6 h-6 rounded bg-gray-100 text-gray-600 flex items-center justify-center text-sm group-hover:bg-blue-100 group-hover:text-blue-600 transition-colors duration-200"
            :class="index < 3 ? getTopRankClass(index) : ''"
          >
            {{ index + 1 }}
          </span>
          <div class="ml-3 flex-1">
            <h4 class="text-gray-600 group-hover:text-blue-600 line-clamp-2 text-sm transition-colors duration-200">
              {{ article.title }}
            </h4>
            <div class="mt-1 flex items-center text-xs text-gray-400 space-x-2">
              <span class="flex items-center">
                <el-icon class="mr-1"><View /></el-icon>
                {{ article.viewCount }}
              </span>
              <span class="flex items-center">
                <el-icon class="mr-1"><ChatDotRound /></el-icon>
                {{ article.commentCount }}
              </span>
            </div>
          </div>
        </div>
      </router-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Star, View, ChatDotRound } from '@element-plus/icons-vue'
import type { ArticleVO } from '@/types/article'

defineProps<{
  articles: ArticleVO[]
}>()

const getTopRankClass = (index: number) => {
  const classes = [
    'bg-red-100 text-red-600 group-hover:bg-red-200',
    'bg-orange-100 text-orange-600 group-hover:bg-orange-200',
    'bg-yellow-100 text-yellow-600 group-hover:bg-yellow-200'
  ]
  return classes[index]
}
</script>