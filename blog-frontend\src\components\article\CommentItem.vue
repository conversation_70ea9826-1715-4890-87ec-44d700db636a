<!-- 评论项组件 -->
<template>
  <div class="comment-item">
    <div class="flex gap-4">
      <!-- 用户头像 -->
      <el-avatar 
        :size="size === 'small' ? 32 : 40" 
        :src="getAvatarUrl(comment.userAvatar)"
        @error="imgError"
      >
        {{ comment.userName?.charAt(0) }}
      </el-avatar>

      <div class="flex-1">
        <!-- 评论头部 -->
        <div class="flex items-center justify-between mb-2">
          <span class="font-medium text-gray-900 dark:text-gray-100">{{ comment.userName }}</span>
          <span class="text-sm text-gray-500 dark:text-gray-400">{{ formatDate(comment.createdAt) }}</span>
        </div>

        <!-- 评论内容 -->
        <p class="text-gray-700 dark:text-gray-300">{{ comment.content }}</p>

        <!-- 评论操作 -->
        <div class="flex items-center gap-4 mt-2">
          <!-- 点赞按钮 -->
          <button 
            class="text-sm text-gray-500 hover:text-blue-600 transition-colors flex items-center gap-1"
            @click="handleLike"
          >
            <el-icon :class="{ 'text-blue-600': comment.liked }">
              <Star :filled="comment.liked" />
            </el-icon>
            <span>{{ comment.likeCount || 0 }}</span>
          </button>

          <!-- 回复按钮 -->
          <button 
            class="text-sm text-gray-500 hover:text-blue-600 transition-colors"
            @click="showReplyForm"
          >
            回复
          </button>

          <!-- 删除按钮 -->
          <button 
            v-if="canDelete"
            class="text-sm text-red-500 hover:text-red-600 transition-colors"
            @click="handleDelete"
          >
            删除
          </button>
        </div>

        <!-- 回复表单 -->
        <div v-if="replyFormVisible" class="mt-4">
          <el-input
            v-model="replyContent"
            type="textarea"
            :rows="2"
            placeholder="写下你的回复..."
            maxlength="500"
            show-word-limit
          />
          <div class="flex justify-end gap-2 mt-2">
            <el-button @click="replyFormVisible = false">取消</el-button>
            <el-button type="primary" @click="handleReplySubmit" :loading="submitting">
              回复
            </el-button>
          </div>
        </div>
        
        <!-- 子评论 -->
        <div v-if="comment.children?.length" class="mt-4 space-y-4 pl-4 border-l-2 border-gray-100 dark:border-gray-700">
          <comment-item
            v-for="reply in comment.children"
            :key="reply.id"
            :comment="reply"
            :article-id="articleId"
            size="small"
            @refresh="$emit('refresh')"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Star } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { formatDate } from '@/utils/date'
import { getAvatarUrl, useImageFallback } from '@/utils/image'
import type { CommentVO } from '@/types/comment'
import { 
  likeComment,
  unlikeComment,
  deleteComment,
  replyComment 
} from '@/api/comment'

const props = defineProps<{
  comment: CommentVO
  articleId: number
  size?: 'normal' | 'small'
}>()

const emit = defineEmits<{
  (e: 'refresh'): void
}>()

const userStore = useUserStore()
const { imgError } = useImageFallback()

// 回复表单状态
const replyFormVisible = ref(false)
const replyContent = ref('')
const submitting = ref(false)

// 判断是否可以删除评论
const canDelete = computed(() => {
  return userStore.userInfo?.id === props.comment.userId
})

// 显示回复表单
const showReplyForm = () => {
  if (!userStore.isAuthenticated) {
    ElMessage.warning('请先登录后再回复')
    return
  }
  replyFormVisible.value = true
}

// 处理点赞
const handleLike = async () => {
  if (!userStore.isAuthenticated) {
    ElMessage.warning('请先登录后再点赞')
    return
  }

  try {
    if (props.comment.liked) {
      await unlikeComment(props.comment.id)
      props.comment.likeCount--
    } else {
      await likeComment(props.comment.id)
      props.comment.likeCount++
    }
    props.comment.liked = !props.comment.liked
  } catch (error) {
    console.error('点赞失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 处理删除
const handleDelete = async () => {
  try {
    await ElMessageBox.confirm('确定要删除这条评论吗？', '提示', {
      type: 'warning'
    })
    await deleteComment(props.comment.id)
    ElMessage.success('删除成功')
    emit('refresh')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除评论失败:', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  }
}

// 提交回复
const handleReplySubmit = async () => {
  if (!replyContent.value.trim()) {
    ElMessage.warning('回复内容不能为空')
    return
  }

  submitting.value = true
  try {
    await replyComment({
      articleId: props.articleId,
      parentId: props.comment.id,
      rootId: props.comment.rootId || props.comment.id,
      content: replyContent.value.trim()
    })
    ElMessage.success('回复成功')
    replyContent.value = ''
    replyFormVisible.value = false
    emit('refresh')
  } catch (error) {
    console.error('回复失败:', error)
    ElMessage.error('回复失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}
</script> 