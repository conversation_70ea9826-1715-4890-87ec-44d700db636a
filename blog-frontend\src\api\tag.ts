import { http } from '@/utils/request'
import type { R, PageResult } from '@/types/common'
import type { TagVO, ArticleVO } from '@/types/article'

// 获取所有标签
export const getAllTags = () => {
  return http<R<TagVO[]>>({
    url: '/tags',
    method: 'GET'
  })
}

// 获取标签详情
export const getTagById = (id: number) => {
  return http<R<TagVO>>({
    url: `/tags/${id}`,
    method: 'GET'
  })
}

// 获取标签下的文章列表
export const getArticlesByTagId = (tagId: number, page: number = 1, size: number = 10) => {
  return http<R<PageResult<ArticleVO>>>({
    url: `/tags/${tagId}/articles`,
    method: 'GET',
    params: { page, size }
  })
}

// 获取热门标签
export const getHotTags = (limit: number = 20) => {
  return http<R<TagVO[]>>({
    url: '/tags/hot',
    method: 'GET',
    params: { limit }
  })
}
