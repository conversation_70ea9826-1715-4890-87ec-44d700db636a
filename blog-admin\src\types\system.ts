// 系统配置接口
export interface SystemConfig {
  // 基础配置
  siteName: string
  siteDescription: string
  siteKeywords: string
  siteUrl: string
  
  // 邮件配置
  smtpHost: string
  smtpPort: number
  smtpUsername: string
  smtpPassword: string
  smtpFrom: string
  
  // AI配置
  aiModel: string
  aiApiKey: string
  aiApiEndpoint: string
  aiMaxTokens: number
  aiTemperature: number
  aiRequestLimit: number
  aiTokenLimit: number
  
  // 安全配置
  securityLoginAttempts: number
  securityLockDuration: number
}

// 监控数据接口
export interface MonitoringData {
  aiUsage: {
    totalCalls: number
    totalTokens: number
    successRate: number
    averageLatency: number
    dailyStats: Array<{
      date: string
      calls: number
      tokens: number
    }>
  }
  performance: {
    cpuUsage: number
    memoryUsage: number
    responseTime: number
    concurrentUsers: number
    errorRate: number
  }
  userBehavior: {
    activeUsers: number
    newUsers: number
    retention: number
    averageSessionTime: number
    topPages: Array<{
      page: string
      views: number
    }>
  }
  contentQuality: {
    averageScore: number
    totalArticles: number
    qualityDistribution: Array<{
      score: string
      count: number
    }>
    topAuthors: <AUTHORS>
      author: string
      score: number
    }>
  }
} 