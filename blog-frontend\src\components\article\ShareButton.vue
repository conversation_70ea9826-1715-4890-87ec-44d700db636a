<!-- 分享按钮组件 -->
<template>
  <div class="share-button">
    <el-dropdown trigger="click" @command="handleShare" :disabled="!isAuthenticated">
      <el-button type="success" :disabled="!isAuthenticated">
        <el-icon><Share /></el-icon>
        {{ isAuthenticated ? '分享' : '登录后分享' }}
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="wechat">
            <el-icon><ChatDotRound /></el-icon>
            分享到微信
          </el-dropdown-item>
          <el-dropdown-item command="weibo">
            <el-icon><Platform /></el-icon>
            分享到微博
          </el-dropdown-item>
          <el-dropdown-item command="link">
            <el-icon><Link /></el-icon>
            复制链接
          </el-dropdown-item>
          <el-dropdown-item command="poster">
            <el-icon><Picture /></el-icon>
            生成海报
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 微信分享二维码弹窗 -->
    <el-dialog
      v-model="qrCodeVisible"
      title="微信分享"
      width="300px"
      align-center
      :close-on-click-modal="false"
    >
      <div class="text-center">
        <qrcode-vue :value="shareUrl" :size="200" level="H" />
        <p class="mt-4 text-gray-600">请使用微信扫码分享</p>
      </div>
    </el-dialog>

    <!-- 分享海报弹窗 -->
    <el-dialog
      v-model="posterVisible"
      title="分享海报"
      width="375px"
      align-center
      :close-on-click-modal="false"
    >
      <div v-loading="posterLoading">
        <div v-if="posterUrl" class="share-poster">
          <img 
            :src="posterUrl" 
            alt="分享海报" 
            class="w-full rounded-lg shadow-md"
            @error="handlePosterError"
          />
        </div>
        <div v-else class="h-[667px] bg-gray-100 rounded-lg flex items-center justify-center">
          <el-icon :size="32" class="text-gray-400"><Picture /></el-icon>
        </div>
      </div>
      <template #footer>
        <el-button @click="posterVisible = false">关闭</el-button>
        <el-button 
          type="primary" 
          :disabled="!posterUrl" 
          @click="downloadPoster"
        >
          保存海报
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import QrcodeVue from 'qrcode.vue'
import { Share, ChatDotRound, Platform, Link, Picture } from '@element-plus/icons-vue'
import type { SharePlatform } from '@/types/share'
import { generatePoster, recordShare } from '@/api/share'

const props = defineProps<{
  title: string
  summary?: string
  cover?: string
  articleId: number
  authorName: string
  authorAvatar?: string
}>()

const router = useRouter()
const userStore = useUserStore()
const isAuthenticated = computed(() => userStore.isAuthenticated)

// 分享链接
const shareUrl = computed(() => {
  return `${window.location.origin}/articles/${props.articleId}`
})

// 弹窗状态
const qrCodeVisible = ref(false)
const posterVisible = ref(false)
const posterLoading = ref(false)
const posterUrl = ref('')

// 处理未登录状态
const handleUnauthorized = () => {
  router.push({
    path: '/auth/login',
    query: { redirect: window.location.pathname }
  })
}

// 处理分享
const handleShare = async (platform: SharePlatform) => {
  if (!isAuthenticated.value) {
    handleUnauthorized()
    return
  }

  try {
    switch (platform) {
      case 'wechat':
        qrCodeVisible.value = true
        break
      case 'weibo':
        shareToWeibo()
        break
      case 'link':
        await copyLink()
        break
      case 'poster':
        await showPoster()
        break
    }
    // 记录分享事件
    await recordShare(props.articleId, platform)
  } catch (error) {
    console.error('分享失败:', error)
    ElMessage.error('分享失败，请稍后重试')
  }
}

// 分享到微博
const shareToWeibo = () => {
  const params = new URLSearchParams({
    url: shareUrl.value,
    title: props.title,
    pic: props.cover || ''
  })
  window.open(`http://service.weibo.com/share/share.php?${params.toString()}`, '_blank')
}

// 复制链接
const copyLink = async () => {
  try {
    await navigator.clipboard.writeText(shareUrl.value)
    ElMessage.success('链接已复制')
  } catch (err) {
    console.error('Failed to copy:', err)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 显示海报
const showPoster = async () => {
  posterVisible.value = true
  posterLoading.value = true
  try {
    const { data } = await generatePoster({
      title: props.title,
      summary: props.summary,
      cover: props.cover,
      authorName: props.authorName,
      authorAvatar: props.authorAvatar,
      qrCodeContent: shareUrl.value
    })
    posterUrl.value = data
  } catch (error) {
    console.error('生成海报失败:', error)
    ElMessage.error('生成海报失败，请稍后重试')
  } finally {
    posterLoading.value = false
  }
}

// 下载海报
const downloadPoster = async () => {
  if (!posterUrl.value) return
  
  try {
    const link = document.createElement('a')
    link.download = `share-${props.articleId}.png`
    link.href = posterUrl.value
    link.click()
  } catch (err) {
    console.error('下载海报失败:', err)
    ElMessage.error('下载失败，请右键保存图片')
  }
}

// 添加错误处理方法
const handlePosterError = (e: Event) => {
  const img = e.target as HTMLImageElement
  img.src = '/default-poster.png'
  ElMessage.warning('海报加载失败，已显示默认图片')
}
</script>

<style lang="scss" scoped>
.share-poster {
  img {
    width: 100%;
    height: auto;
    max-height: 667px;
    object-fit: contain;
  }
}
</style> 