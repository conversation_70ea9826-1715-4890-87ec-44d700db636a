<template>
  <div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-sm p-6">
      <!-- 个人资料卡片 -->
      <div class="flex items-start space-x-6 mb-8">
        <div class="flex-shrink-0">
          <div class="relative group">
            <img 
              :src="getAvatarUrl(userStore.userInfo?.avatar)" 
              class="w-24 h-24 rounded-lg object-cover" 
              alt="用户头像"
              @error="imgError"
            >
            <div 
              class="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
              @click="handleAvatarClick"
            >
              <el-icon class="text-white text-xl"><Camera /></el-icon>
            </div>
          </div>
          <input
            ref="fileInputRef"
            type="file"
            accept="image/*"
            class="hidden"
            @change="handleFileChange"
          />
        </div>
        <div class="flex-grow">
          <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ userStore.userInfo?.username }}</h2>
          <p class="text-gray-600 mb-4">{{ form.bio || '这个人很懒，什么都没有留下...' }}</p>
          <div class="flex items-center space-x-4 text-sm text-gray-500">
            <span>注册时间: {{ formatDate(userStore.userInfo?.createdAt) }}</span>
            <span>·</span>
            <span>文章: {{ userStats.articleCount }}</span>
            <span>·</span>
            <span>评论: {{ userStats.commentCount }}</span>
          </div>
        </div>
      </div>

      <!-- 基本信息表单 -->
      <div class="max-w-2xl">
        <h3 class="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
        <el-form 
          ref="formRef"
          :model="form" 
          :rules="rules"
          label-width="100px"
        >
          <el-form-item label="用户名" prop="username">
            <el-input 
              v-model="form.username" 
              placeholder="请输入用户名" 
              :disabled="true"
              class="readonly-input"
            />
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input 
              v-model="form.email" 
              placeholder="请输入邮箱" 
              :disabled="true"
              class="readonly-input"
            />
          </el-form-item>
          <el-form-item label="个人简介" prop="bio">
            <el-input 
              v-model="form.bio" 
              type="textarea" 
              :rows="3"
              placeholder="介绍一下自己吧" 
            />
          </el-form-item>
          <el-form-item label="主题设置" prop="theme">
            <el-radio-group v-model="form.theme">
              <el-radio-button value="light">浅色</el-radio-button>
              <el-radio-button value="dark">深色</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="通知设置" prop="notificationEnabled">
            <el-switch
              v-model="form.notificationEnabled"
              active-text="开启通知"
              inactive-text="关闭通知"
            />
          </el-form-item>
          <el-form-item label="语言设置" prop="language">
            <el-radio-group v-model="form.language">
              <el-radio-button value="zh_CN">简体中文</el-radio-button>
              <el-radio-button value="en_US">English</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <el-button 
              type="primary" 
              :loading="submitting"
              @click="handleSubmit"
            >
              保存修改
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 修改密码表单 -->
      <div class="max-w-2xl mt-8 pt-8 border-t">
        <h3 class="text-lg font-medium text-gray-900 mb-4">修改密码</h3>
        <el-form 
          ref="passwordFormRef"
          :model="passwordForm" 
          :rules="passwordRules"
          label-width="100px"
        >
          <el-form-item label="当前密码" prop="oldPassword">
            <el-input 
              v-model="passwordForm.oldPassword" 
              type="password" 
              placeholder="请输入当前密码" 
              show-password
            />
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input 
              v-model="passwordForm.newPassword" 
              type="password" 
              placeholder="请输入新密码" 
              show-password
            />
          </el-form-item>
          <el-form-item label="确认新密码" prop="confirmPassword">
            <el-input 
              v-model="passwordForm.confirmPassword" 
              type="password" 
              placeholder="请再次输入新密码" 
              show-password
            />
          </el-form-item>
          <el-form-item>
            <el-button 
              type="primary" 
              :loading="passwordSubmitting"
              @click="handlePasswordSubmit"
            >
              修改密码
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Camera } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { formatDate } from '@/utils/date'
import { getAvatarUrl, useImageFallback, clearImageFailedCache } from '@/utils/image'
import { updateUserProfile, updateUserAvatar, updatePassword, getUserStats, getUserSettings } from '@/api/userCenter'
import type { UserProfileForm } from '@/types/user'
import { useNotificationStore } from '@/stores/notification'
import { useSettingsStore } from '@/stores/settings'

const userStore = useUserStore()
const notificationStore = useNotificationStore()
const settingsStore = useSettingsStore()
const { imgError } = useImageFallback()

// 表单ref
const formRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()
const fileInputRef = ref<HTMLInputElement>()

// 用户统计数据
const userStats = ref({
  articleCount: 0,
  commentCount: 0,
  favoriteCount: 0,
  notificationCount: 0
})

// 表单数据
const form = ref<UserProfileForm>({
  username: userStore.userInfo?.username || '',
  email: userStore.userInfo?.email || '',
  bio: userStore.userInfo?.bio || '',
  theme: 'light',
  notificationEnabled: true,
  language: 'zh_CN'
})

// 密码表单数据
const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 提交状态
const submitting = ref(false)
const passwordSubmitting = ref(false)

// 表单校验规则
const rules = {
  bio: [
    { max: 200, message: '个人简介不能超过200个字符', trigger: 'blur' }
  ],
  theme: [
    { required: true, message: '请选择主题', trigger: 'change' }
  ],
  language: [
    { required: true, message: '请选择语言', trigger: 'change' }
  ]
}

// 密码表单校验规则
const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 加载用户统计数据
const loadUserStats = async () => {
  try {
    const { data } = await getUserStats()
    userStats.value = data
  } catch (error) {
    console.error('获取用户统计数据失败:', error)
  }
}

// 处理头像点击
const handleAvatarClick = () => {
  fileInputRef.value?.click()
}

// 处理文件选择
const handleFileChange = async (event: Event) => {
  const input = event.target as HTMLInputElement
  const file = input.files?.[0]
  
  if (!file) return
  
  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }
  
  // 验证文件大小 (2MB)
  if (file.size > 2 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过2MB')
    return
  }
  
  try {
    const { data } = await updateUserAvatar(file)
    // 清除之前的失败缓存
    clearImageFailedCache(userStore.userInfo?.avatar)
    // 立即刷新用户信息
    await userStore.getUserInfo()
    ElMessage.success('头像更新成功')
  } catch (error) {
    console.error('更新头像失败:', error)
    ElMessage.error('更新头像失败')
  } finally {
    // 清空文件输入框
    input.value = ''
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        // 更新用户资料
        await updateUserProfile({
          username: form.value.username,
          email: form.value.email,
          bio: form.value.bio
        })
        
        // 使用 settings store 更新设置
        const success = await settingsStore.updateSettings({
          theme: form.value.theme,
          notificationEnabled: form.value.notificationEnabled,
          language: form.value.language
        })

        if (success) {
          ElMessage.success('保存成功')
        }
      } catch (error) {
        console.error('更新失败:', error)
        ElMessage.error('保存失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 提交密码修改
const handlePasswordSubmit = async () => {
  if (!passwordFormRef.value) return
  
  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      passwordSubmitting.value = true
      try {
        await updatePassword({
          oldPassword: passwordForm.value.oldPassword,
          newPassword: passwordForm.value.newPassword
        })
        ElMessage.success('密码修改成功')
        // 清空表单
        passwordForm.value = {
          oldPassword: '',
          newPassword: '',
          confirmPassword: ''
        }
        // 重置表单校验结果
        passwordFormRef.value.resetFields()
      } catch (error) {
        console.error('修改密码失败:', error)
        ElMessage.error('修改密码失败')
      } finally {
        passwordSubmitting.value = false
      }
    }
  })
}

// 初始化
onMounted(async () => {
  try {
    // 获取用户统计数据
    await loadUserStats()
    
    // 获取用户设置
    const { data } = await getUserSettings()
    if (data) {
      form.value.theme = data.theme
      form.value.notificationEnabled = data.notificationEnabled
      form.value.language = data.language
    }
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('加载数据失败')
  }
})
</script> 

<style scoped>
.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.readonly-input :deep(.el-input__wrapper) {
  background-color: var(--el-fill-color-light);
  cursor: not-allowed;
}

.readonly-input :deep(.el-input__inner) {
  color: var(--el-text-color-regular);
  -webkit-text-fill-color: var(--el-text-color-regular);
  cursor: not-allowed;
}
</style> 