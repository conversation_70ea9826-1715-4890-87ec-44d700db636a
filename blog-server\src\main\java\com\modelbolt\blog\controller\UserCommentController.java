package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.comment.UserCommentVO;
import com.modelbolt.blog.service.CommentService;
import com.modelbolt.blog.utils.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Tag(name = "用户评论管理", description = "用户评论管理相关接口")
@RestController
@RequestMapping("/user/comments")
@RequiredArgsConstructor
@SecurityRequirement(name = "Bearer")
public class UserCommentController {

    private final CommentService commentService;

    @Operation(summary = "获取用户评论列表")
    @GetMapping
    public ApiResponse<PageResult<UserCommentVO>> getUserComments(
            @Parameter(description = "关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "状态：0-待审核，1-已通过，2-已拒绝") @RequestParam(required = false) String status,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer size) {
        Long userId = SecurityUtils.getCurrentUserId();
        return ApiResponse.success(commentService.getUserComments(userId, keyword, status, page, size));
    }

    @Operation(summary = "删除评论")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteComment(@Parameter(description = "评论ID") @PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        commentService.deleteUserComment(userId, id);
        return ApiResponse.success();
    }
} 