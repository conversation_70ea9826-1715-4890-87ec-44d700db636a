package com.modelbolt.blog.service;

import com.modelbolt.blog.model.dto.ai.AiWritingRequest;
import com.modelbolt.blog.model.dto.ai.AiWritingResponse;
import com.modelbolt.blog.model.dto.ai.ChatRequest;
import com.modelbolt.blog.model.dto.ai.ChatResponse;

public interface AiWritingService {
    /**
     * 处理AI写作请求
     *
     * @param request AI写作请求
     * @return AI写作响应
     */
    AiWritingResponse processWritingRequest(AiWritingRequest request);

    /**
     * 验证用户的AI使用配额
     *
     * @param userId 用户ID
     * @return 是否允许使用AI服务
     */
    boolean validateUserQuota(Long userId);

    /**
     * 记录AI使用情况
     *
     * @param userId 用户ID
     * @param tokensUsed 使用的token数量
     */
    void recordUsage(Long userId, int tokensUsed);

    /**
     * 发送聊天消息
     *
     * @param request 聊天请求
     * @return 聊天响应
     */
    ChatResponse processChat(ChatRequest request);
} 