package com.modelbolt.blog.config;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.io.File;

@Slf4j
@Configuration
public class FileUploadConfig {
    @Value("${file.upload.path}")
    private String uploadPath;

    @Value("${file.upload.subdirs.avatar}")
    private String avatarDir;

    @PostConstruct
    public void init() {
        log.info("Initializing file upload directories...");
        
        // 创建主目录
        File uploadDir = new File(uploadPath);
        if (!uploadDir.exists()) {
            if (uploadDir.mkdirs()) {
                log.info("Created upload directory: {}", uploadPath);
            } else {
                log.error("Failed to create upload directory: {}", uploadPath);
            }
        }

        // 创建头像目录
        File avatarUploadDir = new File(uploadPath, avatarDir);
        if (!avatarUploadDir.exists()) {
            if (avatarUploadDir.mkdirs()) {
                log.info("Created avatar upload directory: {}", avatarUploadDir.getAbsolutePath());
            } else {
                log.error("Failed to create avatar upload directory: {}", avatarUploadDir.getAbsolutePath());
            }
        }
    }
} 