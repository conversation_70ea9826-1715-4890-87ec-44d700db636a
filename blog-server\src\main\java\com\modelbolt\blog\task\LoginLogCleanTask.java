package com.modelbolt.blog.task;

import com.modelbolt.blog.service.LoginLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class LoginLogCleanTask {
    
    private final LoginLogService loginLogService;
    
    public LoginLogCleanTask(LoginLogService loginLogService) {
        this.loginLogService = loginLogService;
    }
    
    /**
     * 每天凌晨3点执行清理30天前的登录日志
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void cleanLoginLog() {
        log.info("开始清理登录日志");
        int count = loginLogService.cleanLoginLog(30);
        log.info("清理登录日志完成，共清理{}条记录", count);
    }
} 