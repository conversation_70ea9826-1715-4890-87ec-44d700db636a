<template>
  <div class="article-detail container mx-auto px-4 py-8">
    <div v-if="loading" class="flex justify-center items-center min-h-[400px]">
      <el-skeleton :rows="10" animated />
    </div>
    
    <div v-else-if="article.id" class="flex flex-col lg:flex-row gap-8">
      <!-- 主内容区 -->
      <div class="flex-1">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8">
          <!-- 文章头部 -->
          <article-header
            :title="article.title"
            :author-name="article.authorName"
            :author-avatar="article.authorAvatar"
            :created-at="article.createdAt"
            :view-count="article.viewCount"
            :comment-count="article.commentCount"
            :like-count="article.likeCount"
            :favorite-count="article.favoriteCount"
            :cover="article.cover"
          />

          <!-- 文章内容 -->
          <article-content
            :content="article.content"
            class="prose dark:prose-invert max-w-none prose-img:rounded-lg prose-img:shadow-lg"
          />

          <!-- 文章标签 -->
          <div v-if="article.tags?.length" class="flex flex-wrap gap-2 mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
            <router-link
              v-for="tag in article.tags"
              :key="tag.id"
              :to="`/tags/${tag.id}`"
              class="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-sm text-gray-600 dark:text-gray-300 rounded-full hover:bg-blue-100 hover:text-blue-600 dark:hover:bg-blue-900 dark:hover:text-blue-300 transition-colors duration-200"
            >
              {{ tag.name }}
            </router-link>
          </div>

          <!-- 文章操作 -->
          <div class="flex justify-center gap-4 mt-8">
            <el-button
              type="primary"
              :icon="article.liked ? 'StarFilled' : 'Star'"
              @click="isAuthenticated ? toggleLike() : handleUnauthorized()"
              :disabled="!isAuthenticated"
            >
              {{ article.liked ? '取消点赞' : '点赞' }}
            </el-button>
            <el-button
              :type="article.favorited ? 'warning' : 'info'"
              :icon="article.favorited ? 'CollectionTag' : 'Collection'"
              @click="isAuthenticated ? toggleFavorite() : handleUnauthorized()"
              :disabled="!isAuthenticated"
            >
              {{ article.favorited ? '取消收藏' : '收藏' }}
            </el-button>
               <!-- 打赏按钮组件已注释
            <reward-button
              :article-id="article.id"
              :author-id="article.authorId"
              @success="handleRewardSuccess"
            />
            -->
            <!-- 添加分享按钮 -->
            <share-button
              :title="article.title"
              :summary="article.summary"
              :cover="article.cover"
              :article-id="article.id"
              :author-name="article.authorName"
              :author-avatar="article.authorAvatar"
            />
          </div>

          <!-- 打赏记录已注释
          <div class="mt-8">
            <reward-list
              ref="rewardListRef"
              :article-id="article.id"
            />
          </div>
          -->
        </div>

        <!-- 评论区 -->
        <template v-if="isAuthenticated">
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 mt-8">
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100">评论区</h2>
              <span class="text-gray-500 dark:text-gray-400">共 {{ article.commentCount || 0 }} 条评论</span>
            </div>

            <!-- 评论输入框 -->
            <div class="mb-6">
              <el-input
                v-model="commentContent"
                type="textarea"
                :rows="3"
                placeholder="写下你的评论..."
                maxlength="500"
                show-word-limit
                @focus="handleCommentFocus"
              />
              <div class="flex justify-end mt-2">
                <el-button type="primary" @click="handleCommentSubmit" :loading="submitting">
                  发表评论
                </el-button>
              </div>
            </div>
            
            <!-- 评论列表 -->
            <div v-if="commentsLoading" class="py-4">
              <el-skeleton :rows="3" animated />
            </div>
            <template v-else>
              <div v-if="comments.length > 0" class="space-y-6">
                <comment-item
                  v-for="comment in comments"
                  :key="comment.id"
                  :comment="comment"
                  :article-id="article.id"
                  @refresh="fetchComments"
                />
              </div>
              <el-empty v-else description="暂无评论，快来抢沙发吧！" />
            </template>
          </div>
        </template>
        <template v-else>
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 mt-8">
            <div class="text-center py-8">
              <el-icon class="text-gray-400 mb-4" :size="48"><ChatDotRound /></el-icon>
              <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">登录后查看评论</h3>
              <p class="text-gray-500 dark:text-gray-400 mb-4">登录后即可查看和发表评论</p>
              <el-button 
                type="primary" 
                @click="handleUnauthorized"
              >
                去登录
              </el-button>
            </div>
          </div>
        </template>
      </div>

      <!-- 侧边栏 -->
      <div class="lg:w-80 space-y-8">
        <!-- 作者信息卡片 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <div class="flex items-center space-x-4 mb-4">
            <el-avatar 
              :size="64" 
              :src="getAvatarUrl(authorInfo.avatar)"
              @error="(e) => imgError(e)"
            />
            <div>
              <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100">{{ authorInfo.username }}</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ authorInfo.bio || '这个作者很懒，还没有写简介' }}</p>
            </div>
          </div>
          
          <div class="grid grid-cols-3 gap-4 mb-4">
            <div class="text-center">
              <div class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ authorInfo.stats.articles }}</div>
              <div class="text-sm text-gray-500 dark:text-gray-400">文章</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ authorInfo.stats.followers }}</div>
              <div class="text-sm text-gray-500 dark:text-gray-400">粉丝</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ authorInfo.stats.likes }}</div>
              <div class="text-sm text-gray-500 dark:text-gray-400">获赞</div>
            </div>
          </div>
          
          <!-- 关注按钮 -->
          <template v-if="!isCurrentUser">
            <el-button
              v-if="isAuthenticated"
              :type="isFollowing ? 'success' : 'primary'"
              class="w-full"
              :loading="followLoading"
              @click="toggleFollow"
            >
              <template #icon>
                <el-icon><Plus v-if="!isFollowing" /><Check v-else /></el-icon>
              </template>
              {{ isFollowing ? '已关注' : '关注作者' }}
            </el-button>
            <el-button
              v-else
              type="primary"
              class="w-full"
              @click="() => router.push({ path: '/auth/login', query: { redirect: route.fullPath }})"
            >
              登录后关注
            </el-button>
          </template>
          <el-button
            v-else
            type="info"
            class="w-full"
            disabled
          >
            这是你的文章
          </el-button>
        </div>

        <!-- 目录导航 -->
        <table-of-contents
          v-if="article.content"
          :content="article.content"
        />

        <!-- 相关文章 -->
        <related-articles
          v-if="relatedArticles.length"
          :articles="relatedArticles"
        />
      </div>
    </div>

    <el-empty v-else description="文章不存在或已被删除" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElSkeleton, ElButton, ElEmpty, ElAvatar } from 'element-plus'
import { Star, StarFilled, Collection, CollectionTag, Plus, Check, Money, Edit, ChatDotRound } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import ArticleHeader from './components/ArticleHeader/index.vue'
import ArticleContent from './components/ArticleContent/Markdown.vue'
import TableOfContents from './components/TableOfContents/index.vue'
import RelatedArticles from './components/RelatedArticles/index.vue'
// 打赏组件已注释
// import RewardButton from '@/components/article/RewardButton.vue'
// import RewardList from '@/components/article/RewardList.vue'
import { formatDate } from '@/utils/date'
import type { ArticleVO } from '@/types/article'
import {
  getArticleDetail,
  getRelatedArticles,
  getArticleComments,
  updateArticleViews,
  likeArticle,
  unlikeArticle,
  favoriteArticle,
  unfavoriteArticle
} from '@/api/article'
import { followUser, unfollowUser, getAuthorInfo, isFollowing as checkIsFollowing } from '@/api/user'
import { getAvatarUrl, useImageFallback } from '@/utils/image'
import ShareButton from '@/components/article/ShareButton.vue'
import CommentItem from '@/components/article/CommentItem.vue'
import { submitComment } from '@/api/comment'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const { isAuthenticated } = storeToRefs(userStore)

// 使用计算属性动态获取文章ID
const articleId = computed(() => {
  const id = Number(route.params.id)
  return !isNaN(id) && id > 0 ? id : null
})

// 文章数据
const article = ref<ArticleVO>({
  id: 0,
  title: '',
  summary: '',
  content: '',
  cover: '',
  authorId: 0,
  authorName: '',
  authorAvatar: '',
  viewCount: 0,
  commentCount: 0,
  likeCount: 0,
  isTop: false,
  status: 1,
  tags: [],
  liked: false,
  favorited: false,
  createdAt: '',
  updatedAt: ''
})

// 作者信息
const authorInfo = ref({
  id: 0,
  username: '',
  avatar: '',
  bio: '',
  stats: {
    articles: 0,
    followers: 0,
    likes: 0
  }
})

// 评论数据
const comments = ref<CommentVO[]>([])
const commentsLoading = ref(false)
const commentParams = ref({
  page: 1,
  size: 10
})

// 相关文章
const relatedArticles = ref<ArticleVO[]>([])

// 加载状态
const loading = ref(true)

// 关注状态
const isFollowing = ref(false)
const followLoading = ref(false)
const isCurrentUser = computed(() => userStore.userInfo?.id === authorInfo.value.id)

// 评论相关
const commentContent = ref('')
const submitting = ref(false)

// 获取作者信息
const fetchAuthorInfo = async () => {
  try {
    const { data } = await getAuthorInfo(article.value.authorId)
    authorInfo.value = data
    // 如果用户已登录，检查是否已关注作者
    if (isAuthenticated.value && !isCurrentUser.value) {
      const { data: following } = await checkIsFollowing(article.value.authorId)
      isFollowing.value = following
    }
  } catch (error) {
    console.error('Error fetching author info:', error)
  }
}

// 获取文章详情
const fetchArticleDetail = async () => {
  if (!articleId.value) {
    loading.value = false
    ElMessage.error('无效的文章ID')
    return
  }

  try {
    const response = await getArticleDetail(articleId.value)
    
    if (response?.code === 200 && response.data) {
      // 更新文章数据
      article.value = {
        ...article.value,
        ...response.data
      }
      
      
      // 获取作者信息
      if (article.value.authorId) {
        await fetchAuthorInfo()
      } else {
        console.warn('No author information in article data')
        ElMessage.warning('文章作者信息不完整')
      }
      
      // 更新阅读量
      if (article.value.id) {
        try {
          await updateArticleViews(article.value.id)
        } catch (error) {
          console.error('Error updating article views:', error)
        }
      }
    } else {
      console.warn('Failed to fetch article detail:', response?.msg)
      ElMessage.error(response?.msg || '获取文章详情失败')
      // 如果文章不存在，重置文章数据
      article.value = {
        id: 0,
        title: '',
        summary: '',
        content: '',
        cover: '',
        authorId: 0,
        authorName: '',
        authorAvatar: '',
        viewCount: 0,
        commentCount: 0,
        likeCount: 0,
        isTop: false,
        status: 1,
        tags: [],
        liked: false,
        favorited: false,
        createdAt: '',
        updatedAt: ''
      }
    }
  } catch (error: any) {
    console.error('Error fetching article detail:', error)
    const errorMessage = error.response?.data?.message || error.message || '获取文章详情失败'
    ElMessage.error(errorMessage)
    // 重置文章数据
    article.value = {
      id: 0,
      title: '',
      summary: '',
      content: '',
      cover: '',
      authorId: 0,
      authorName: '',
      authorAvatar: '',
      viewCount: 0,
      commentCount: 0,
      likeCount: 0,
      isTop: false,
      status: 1,
      tags: [],
      liked: false,
      favorited: false,
      createdAt: '',
      updatedAt: ''
    }
  } finally {
    loading.value = false
  }
}

// 获取相关文章
const fetchRelatedArticles = async () => {
  if (!article.value?.id) {
    console.warn('No valid article ID for fetching related articles')
    return
  }
  
  try {
    const response = await getRelatedArticles(article.value.id)
    if (response?.code === 200 && response.data) {
      relatedArticles.value = response.data
    }
  } catch (error) {
    console.error('获取相关文章失败:', error)
  }
}

// 获取文章评论
const fetchComments = async () => {
  if (!article.value?.id) {
    console.warn('No valid article ID for fetching comments')
    return
  }

  commentsLoading.value = true
  try {
    const response = await getArticleComments(article.value.id, commentParams.value)
    if (response?.code === 200 && response.data) {
      comments.value = response.data.records || []
      // 更新文章的评论总数
      article.value.commentCount = response.data.total || 0
    } else {
      console.warn('Failed to fetch comments:', response?.msg)
    }
  } catch (error) {
    console.error('获取评论失败:', error)
    ElMessage.error('获取评论失败')
  } finally {
    commentsLoading.value = false
  }
}

// 加载更多评论
const loadMoreComments = async () => {
  if (!article.value?.id) {
    console.warn('No valid article ID for loading more comments')
    return
  }

  commentParams.value.page++
  const response = await getArticleComments(article.value.id, commentParams.value)
  if (response?.code === 200 && response.data?.records) {
    comments.value.push(...response.data.records)
  }
}

// 点赞/取消点赞
const toggleLike = async () => {
  if (!isAuthenticated.value) {
    handleUnauthorized()
    return
  }

  try {
    if (article.value.liked) {
      await unlikeArticle(article.value.id)
      article.value.likeCount--
    } else {
      await likeArticle(article.value.id)
      article.value.likeCount++
    }
    article.value.liked = !article.value.liked
  } catch (error: any) {
    // 如果发生错误，重新获取文章详情以确保状态同步
    await fetchArticleDetail()
    ElMessage.error(error.response?.data?.msg || '操作失败，请稍后重试')
  }
}

// 收藏/取消收藏
const toggleFavorite = async () => {
  if (!isAuthenticated.value) {
    handleUnauthorized()
    return
  }

  try {
    if (article.value.favorited) {
      await unfavoriteArticle(article.value.id)
      article.value.favoriteCount--
    } else {
      await favoriteArticle(article.value.id)
      article.value.favoriteCount++
    }
    article.value.favorited = !article.value.favorited
  } catch (error: any) {
    // 如果发生错误，重新获取文章详情以确保状态同步
    await fetchArticleDetail()
    ElMessage.error(error.response?.data?.msg || '操作失败，请稍后重试')
  }
}

// 关注/取消关注作者
const toggleFollow = async () => {
  if (!isAuthenticated.value) {
    handleUnauthorized()
    return
  }

  if (isCurrentUser.value) {
    ElMessage.warning('不能关注自己')
    return
  }

  try {
    followLoading.value = true
    if (isFollowing.value) {
      await unfollowUser(authorInfo.value.id)
      authorInfo.value.stats.followers--
    } else {
      await followUser(authorInfo.value.id)
      authorInfo.value.stats.followers++
    }
    isFollowing.value = !isFollowing.value
    ElMessage.success(isFollowing.value ? '关注成功' : '已取消关注')
  } catch (error) {
    console.error('Error toggling follow:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    followLoading.value = false
  }
}

const hasMoreComments = computed(() => {
  return comments.value.length < (article.value?.commentCount || 0)
})

// 处理评论框聚焦
const handleCommentFocus = () => {
  if (!isAuthenticated.value) {
    handleUnauthorized()
  }
}

// 提交评论
const handleCommentSubmit = async () => {
  if (!isAuthenticated.value) {
    handleUnauthorized()
    return
  }

  if (!commentContent.value.trim()) {
    ElMessage.warning('评论内容不能为空')
    return
  }

  submitting.value = true
  try {
    await submitComment({
      articleId: article.value.id,
      content: commentContent.value.trim()
    })
    ElMessage.success('评论发表成功')
    commentContent.value = ''
    // 重新加载评论列表
    await fetchComments()
  } catch (error) {
    console.error('提交评论失败:', error)
    ElMessage.error('评论发表失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}

// 打赏成功处理方法已注释
// const handleRewardSuccess = () => {
//   // 刷新打赏记录列表
//   rewardListRef.value?.refresh()
// }

// 处理未登录的方法
const handleUnauthorized = () => {
  ElMessage.warning('请先登录')
  router.push({
    path: '/auth/login',
    query: { redirect: route.fullPath }
  })
}

// 监听路由参数变化
watch(
  () => route.params.id,
  async (newId) => {
    if (newId) {
      loading.value = true
      try {
        // 重置数据状态
        article.value = {
          id: 0,
          title: '',
          summary: '',
          content: '',
          cover: '',
          authorId: 0,
          authorName: '',
          authorAvatar: '',
          viewCount: 0,
          commentCount: 0,
          likeCount: 0,
          isTop: false,
          status: 1,
          tags: [],
          liked: false,
          favorited: false,
          createdAt: '',
          updatedAt: ''
        }
        comments.value = []
        relatedArticles.value = []
        commentParams.value.page = 1
        
        // 重新加载所有数据
        await fetchArticleDetail()
        if (article.value.id) {
          await Promise.all([
            fetchRelatedArticles(),
            fetchComments()
          ])
        }
      } catch (error) {
        console.error('Error loading article data:', error)
        ElMessage.error('加载文章失败')
      } finally {
        loading.value = false
      }
    }
  },
  { immediate: true } // 立即执行一次
)

const { imgError } = useImageFallback()
</script>

<style lang="scss" scoped>
.article-detail {
  min-height: calc(100vh - 64px);
}

// 评论区样式
.comment-section {
  .el-textarea {
    margin-bottom: 1rem;
  }
}
</style> 