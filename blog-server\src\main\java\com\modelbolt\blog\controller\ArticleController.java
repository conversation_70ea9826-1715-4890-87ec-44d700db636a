package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.article.ArticleVO;
import com.modelbolt.blog.model.dto.comment.CommentVO;
import com.modelbolt.blog.service.ArticleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "文章接口", description = "文章相关接口")
@RestController
@RequestMapping("/articles")
@RequiredArgsConstructor
public class ArticleController {

    private final ArticleService articleService;

    @Operation(summary = "获取置顶文章")
    @GetMapping("/featured")
    public ApiResponse<List<ArticleVO>> getFeaturedArticles() {
        return ApiResponse.success(articleService.getFeaturedArticles());
    }

    @Operation(summary = "分页获取文章列表")
    @GetMapping
    public ApiResponse<PageResult<ArticleVO>> getArticles(
            @RequestParam(required = false) Long categoryId,
            @RequestParam(defaultValue = "newest") String sort,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        return ApiResponse.success(articleService.getArticles(categoryId, sort, page, size));
    }

    @Operation(summary = "获取热门文章")
    @GetMapping("/hot")
    public ApiResponse<List<ArticleVO>> getHotArticles(
            @RequestParam(defaultValue = "5") Integer limit) {
        return ApiResponse.success(articleService.getHotArticles(limit));
    }

    @Operation(summary = "获取用户公开文章列表")
    @GetMapping("/user/{userId}")
    public ApiResponse<PageResult<ArticleVO>> getUserPublicArticles(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer size) {
        return ApiResponse.success(articleService.getUserPublicArticles(userId, page, size));
    }

    @Operation(summary = "获取文章详情")
    @GetMapping("/{id}")
    public ApiResponse<ArticleVO> getArticleDetail(
            @Parameter(description = "文章ID") @PathVariable Long id) {
        return ApiResponse.success(articleService.getArticleDetail(id));
    }

    @Operation(summary = "获取相关文章")
    @GetMapping("/{id}/related")
    public ApiResponse<List<ArticleVO>> getRelatedArticles(
            @Parameter(description = "文章ID") @PathVariable Long id,
            @Parameter(description = "获取数量") @RequestParam(defaultValue = "3") Integer limit) {
        return ApiResponse.success(articleService.getRelatedArticles(id, limit));
    }

    @Operation(summary = "获取文章评论")
    @GetMapping("/{id}/comments")
    public ApiResponse<PageResult<CommentVO>> getArticleComments(
            @Parameter(description = "文章ID") @PathVariable Long id,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer size) {
        return ApiResponse.success(articleService.getArticleComments(id, page, size));
    }

    @Operation(summary = "更新文章浏览量")
    @PostMapping("/{id}/views")
    public ApiResponse<Void> updateArticleViews(
            @Parameter(description = "文章ID") @PathVariable Long id) {
        articleService.updateArticleViews(id);
        return ApiResponse.success();
    }
} 