<template>
  <div class="bg-white rounded-lg shadow-sm p-6">
    <!-- 筛选区域 -->
    <div class="flex justify-between items-center mb-6">
      <div class="flex items-center space-x-4">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索收藏的文章"
          class="w-64"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 收藏列表 -->
    <div v-loading="loading" class="space-y-6">
      <div 
        v-for="article in favorites" 
        :key="article.articleId" 
        class="border-b border-gray-100 pb-6 last:border-0"
      >
        <div class="flex gap-6">
          <div v-if="article.articleCover" class="w-48 h-32 rounded-lg overflow-hidden flex-shrink-0">
            <el-image
              :src="getCoverUrl(article.articleCover)"
              :alt="article.articleTitle"
              class="w-full h-full object-cover"
              fit="cover"
            />
          </div>
          <div class="flex-1">
            <div class="flex justify-between items-start mb-2">
              <router-link 
                :to="'/articles/' + article.articleId" 
                class="text-xl font-medium text-gray-900 hover:text-blue-600"
              >
                {{ article.articleTitle }}
              </router-link>
              <el-button type="danger" link @click="handleUnfavorite(article)">
                取消收藏
              </el-button>
            </div>
            <p class="text-gray-600 mb-4 line-clamp-2">{{ article.articleSummary }}</p>
            <div class="flex items-center space-x-4 text-sm text-gray-500">
              <span class="flex items-center">
                <el-icon class="mr-1"><User /></el-icon>
                {{ article.authorName }}
              </span>
              <span class="flex items-center">
                <el-icon class="mr-1"><View /></el-icon>
                {{ article.viewCount }}
              </span>
              <span class="flex items-center">
                <el-icon class="mr-1"><ChatDotRound /></el-icon>
                {{ article.commentCount }}
              </span>
              <span class="flex items-center">
                <el-icon class="mr-1"><Timer /></el-icon>
                收藏于 {{ formatDate(article.favoriteTime) }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 无数据提示 -->
      <el-empty 
        v-if="!loading && favorites.length === 0" 
        description="暂无收藏文章" 
      />
    </div>

    <!-- 分页 -->
    <div class="flex justify-center mt-6">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 30]"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Search, User, View, ChatDotRound, Timer } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDate } from '@/utils/date'
import { getUserFavorites } from '@/api/userCenter'
import { unfavoriteArticle } from '@/api/article'
import { getCoverUrl } from '@/utils/image'
import type { UserFavoriteVO } from '@/types/favorite'

// 加载状态
const loading = ref(false)

// 搜索关键词
const searchKeyword = ref('')

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 收藏列表
const favorites = ref<UserFavoriteVO[]>([])

// 加载收藏列表
const loadFavorites = async () => {
  loading.value = true
  try {
    const { data } = await getUserFavorites({
      keyword: searchKeyword.value,
      page: currentPage.value,
      size: pageSize.value
    })
    favorites.value = data.records
    total.value = data.total
  } catch (error) {
    console.error('加载收藏列表失败:', error)
    ElMessage.error('加载收藏列表失败')
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  loadFavorites()
}

// 处理取消收藏
const handleUnfavorite = async (article: UserFavoriteVO) => {
  try {
    await ElMessageBox.confirm(
      '确定要取消收藏这篇文章吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await unfavoriteArticle(article.articleId)
    ElMessage.success('取消收藏成功')
    loadFavorites()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消收藏失败:', error)
      ElMessage.error('取消收藏失败')
    }
  }
}

// 处理分页
const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadFavorites()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadFavorites()
}

// 页面加载时获取数据
onMounted(() => {
  loadFavorites()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 