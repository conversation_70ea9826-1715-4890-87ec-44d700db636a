package com.modelbolt.blog.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class NotificationVO {
    
    private Long id;
    
    private String type;
    
    private String senderName;
    
    private String senderAvatar;
    
    private String action;
    
    private String targetType;
    
    private Long targetId;
    
    private String targetTitle;
    
    private String content;
    
    private Boolean isRead;
    
    private LocalDateTime createdAt;
} 