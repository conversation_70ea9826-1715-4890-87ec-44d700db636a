package com.modelbolt.blog.event;

import com.modelbolt.blog.model.entity.Notification;
import org.springframework.context.ApplicationEvent;
import lombok.Getter;

@Getter
public class NotificationEvent extends ApplicationEvent {
    private final Long userId;
    private final Notification notification;
    private final int unreadCount;

    public NotificationEvent(Object source, Long userId, Notification notification, int unreadCount) {
        super(source);
        this.userId = userId;
        this.notification = notification;
        this.unreadCount = unreadCount;
    }
} 