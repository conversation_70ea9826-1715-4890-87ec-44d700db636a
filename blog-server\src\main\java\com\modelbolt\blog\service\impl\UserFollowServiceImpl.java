package com.modelbolt.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.user.UserFollowDTO;
import com.modelbolt.blog.model.entity.User;
import com.modelbolt.blog.model.entity.UserFollow;
import com.modelbolt.blog.mapper.UserFollowMapper;
import com.modelbolt.blog.mapper.UserMapper;
import com.modelbolt.blog.service.UserFollowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserFollowServiceImpl implements UserFollowService {

    private final UserFollowMapper userFollowMapper;
    private final UserMapper userMapper;

    @Override
    public PageResult<UserFollowDTO> getFollowingList(Long userId, Integer current, Integer size) {
        log.debug("Getting following list for user: {}, page: {}, size: {}", userId, current, size);
        
        // 分页查询关注列表
        Page<UserFollow> page = new Page<>(current, size);
        LambdaQueryWrapper<UserFollow> wrapper = new LambdaQueryWrapper<UserFollow>()
                .eq(UserFollow::getFollowerId, userId)
                .orderByDesc(UserFollow::getCreatedAt);
        
        Page<UserFollow> followPage = userFollowMapper.selectPage(page, wrapper);
        
        if (followPage.getRecords().isEmpty()) {
            return new PageResult<>(List.of(), followPage.getTotal(), current, size);
        }
        
        // 获取被关注用户ID列表
        Set<Long> followingIds = followPage.getRecords().stream()
                .map(UserFollow::getFollowingId)
                .collect(Collectors.toSet());
        
        // 查询用户信息
        List<User> users = userMapper.selectBatchIds(followingIds);
        Map<Long, User> userMap = users.stream()
                .collect(Collectors.toMap(User::getId, user -> user));
        
        // 转换为DTO
        List<UserFollowDTO> dtoList = followPage.getRecords().stream()
                .map(follow -> {
                    User user = userMap.get(follow.getFollowingId());
                    return UserFollowDTO.builder()
                            .userId(user.getId())
                            .username(user.getUsername())
                            .avatar(user.getAvatar())
                            .bio(user.getBio())
                            .followTime(follow.getCreatedAt())
                            .isFollowEachOther(true) // 这里是关注列表，所以当前用户一定关注了这些用户
                            .build();
                })
                .collect(Collectors.toList());
        
        return new PageResult<>(dtoList, followPage.getTotal(), current, size);
    }

    @Override
    public PageResult<UserFollowDTO> getFollowersList(Long userId, Integer current, Integer size) {
        log.debug("Getting followers list for user: {}, page: {}, size: {}", userId, current, size);
        
        // 分页查询粉丝列表
        Page<UserFollow> page = new Page<>(current, size);
        LambdaQueryWrapper<UserFollow> wrapper = new LambdaQueryWrapper<UserFollow>()
                .eq(UserFollow::getFollowingId, userId)
                .orderByDesc(UserFollow::getCreatedAt);
        
        Page<UserFollow> followPage = userFollowMapper.selectPage(page, wrapper);
        
        if (followPage.getRecords().isEmpty()) {
            return new PageResult<>(List.of(), followPage.getTotal(), current, size);
        }
        
        // 获取粉丝用户ID列表
        Set<Long> followerIds = followPage.getRecords().stream()
                .map(UserFollow::getFollowerId)
                .collect(Collectors.toSet());
        
        // 查询用户信息
        List<User> users = userMapper.selectBatchIds(followerIds);
        Map<Long, User> userMap = users.stream()
                .collect(Collectors.toMap(User::getId, user -> user));
        
        // 查询当前用户是否也关注了这些粉丝
        Set<Long> mutualFollowIds = userFollowMapper.selectList(new LambdaQueryWrapper<UserFollow>()
                .eq(UserFollow::getFollowerId, userId)
                .in(UserFollow::getFollowingId, followerIds))
                .stream()
                .map(UserFollow::getFollowingId)
                .collect(Collectors.toSet());
        
        // 转换为DTO
        List<UserFollowDTO> dtoList = followPage.getRecords().stream()
                .map(follow -> {
                    User user = userMap.get(follow.getFollowerId());
                    return UserFollowDTO.builder()
                            .userId(user.getId())
                            .username(user.getUsername())
                            .avatar(user.getAvatar())
                            .bio(user.getBio())
                            .followTime(follow.getCreatedAt())
                            .isFollowEachOther(mutualFollowIds.contains(user.getId()))
                            .build();
                })
                .collect(Collectors.toList());
        
        return new PageResult<>(dtoList, followPage.getTotal(), current, size);
    }

    @Override
    public UserFollowDTO.FollowStats getFollowStats(Long userId) {
        log.debug("Getting follow stats for user: {}", userId);
        
        // 查询关注数量
        long followingCount = userFollowMapper.selectCount(new LambdaQueryWrapper<UserFollow>()
                .eq(UserFollow::getFollowerId, userId));
        
        // 查询粉丝数量
        long followerCount = userFollowMapper.selectCount(new LambdaQueryWrapper<UserFollow>()
                .eq(UserFollow::getFollowingId, userId));
        
        return UserFollowDTO.FollowStats.builder()
                .followingCount((int) followingCount)
                .followerCount((int) followerCount)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean followUser(Long followerId, Long followingId) {
        // 检查是否已经关注
        if (userFollowMapper.selectCount(new LambdaQueryWrapper<UserFollow>()
                .eq(UserFollow::getFollowerId, followerId)
                .eq(UserFollow::getFollowingId, followingId)) > 0) {
            return true;
        }

        UserFollow follow = new UserFollow();
        follow.setFollowerId(followerId);
        follow.setFollowingId(followingId);
        return userFollowMapper.insert(follow) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unfollowUser(Long followerId, Long followingId) {
        return userFollowMapper.delete(new LambdaQueryWrapper<UserFollow>()
                .eq(UserFollow::getFollowerId, followerId)
                .eq(UserFollow::getFollowingId, followingId)) > 0;
    }

    @Override
    public boolean isFollowing(Long followerId, Long followingId) {
        return userFollowMapper.selectCount(new LambdaQueryWrapper<UserFollow>()
                .eq(UserFollow::getFollowerId, followerId)
                .eq(UserFollow::getFollowingId, followingId)) > 0;
    }
} 