import { http } from '@/utils/request'
import type { AiWritingRequest, AiWritingResponse, ChatRequest, ChatResponse } from '@/types/ai'

export const aiApi = {
  /**
   * 检查AI使用配额
   */
  checkQuota() {
    return http.get<{ data: boolean }>('/ai/writing/quota/check')
  },

  /**
   * 处理AI写作请求
   */
  processWriting(data: AiWritingRequest) {
    return http.post<{ data: AiWritingResponse }>('/ai/writing/generate', data)
  },

  /**
   * 发送聊天消息
   */
  chat(data: ChatRequest) {
    return http.post<{ data: ChatResponse }>('/ai/writing/chat', data)
  }
} 