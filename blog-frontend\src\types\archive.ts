// 归档文章项
export interface ArchiveArticle {
  id: number
  title: string
  createdAt: string
  viewCount: number
  categoryName?: string
  tags?: Array<{ id: number; name: string }>
}

// 归档月份数据
export interface ArchiveMonth {
  month: string
  count: number
  articles: ArchiveArticle[]
}

// 归档年份数据
export interface ArchiveYear {
  year: string
  count: number
  months: ArchiveMonth[]
}

// 归档统计数据
export interface ArchiveStats {
  total: number
  yearStats: Array<{
    year: string
    count: number
  }>
  timeRange: {
    start: string
    end: string
  }
}

// 归档查询参数
export interface ArchiveQuery {
  year?: number
  categoryId?: number
  tagId?: number
  page: number
  size: number
} 