package com.modelbolt.blog.service;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.model.dto.auth.*;
import jakarta.servlet.http.HttpServletRequest;

public interface AuthService {
    /**
     * 用户登录
     */
    ApiResponse<LoginResponse> login(LoginRequest request, HttpServletRequest servletRequest);

    /**
     * 发送验证码
     */
    ApiResponse<Void> sendVerificationCode(SendVerificationCodeRequest request);

    /**
     * 用户注册
     */
    ApiResponse<RegisterResponse> register(RegisterRequest request);

    /**
     * 发送重置密码邮件
     */
    ApiResponse<Void> sendResetPasswordEmail(String email);

    /**
     * 重置密码
     */
    ApiResponse<Void> resetPassword(ResetPasswordRequest request);

    /**
     * 用户登出
     */
    ApiResponse<Void> logout(String token);

    /**
     * 刷新令牌
     */
    ApiResponse<RefreshTokenResponse> refreshToken(RefreshTokenRequest request);
}