import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import tailwindcss from 'tailwindcss'
import autoprefixer from 'autoprefixer'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  css: {
    postcss: {
      plugins: [
        tailwindcss,
        autoprefixer,
      ],
    },
  },
  server: {
    host: '0.0.0.0',
    port: 3001,
    open: true,
    proxy: {
      // API请求代理
      '/api': {
        target: 'http://************:8080',
        changeOrigin: true
      },
      // 静态资源代理
      '/images': {
        target: 'http://************:8080/api',
        changeOrigin: true,
        rewrite: (path) => path
      },
      // 兼容/static/images路径
      '/static/images': {
        target: 'http://************:8080/api', 
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/static\/images/, '/images')
      }
    }
  }
})
