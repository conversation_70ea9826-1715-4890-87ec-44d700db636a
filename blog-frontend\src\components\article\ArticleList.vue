<!-- 文章列表组件 -->
<template>
  <div class="article-list">
    <!-- 文章列表 -->
    <div :class="[
      'grid gap-8',
      viewMode === 'card' ? 'grid-cols-1 md:grid-cols-2' : 'grid-cols-1'
    ]">
      <article 
        v-for="article in articles" 
        :key="article.id"
        class="bg-white rounded-lg shadow-sm overflow-hidden transition-shadow duration-300 hover:shadow-md"
        :class="{ 'flex gap-6': viewMode === 'list' }"
      >
        <!-- 文章封面图 -->
        <div 
          v-if="article.cover"
          :class="[
            'relative overflow-hidden bg-gray-100',
            viewMode === 'card' ? 'aspect-[16/9]' : 'w-48 aspect-[4/3]'
          ]"
        >
          <img 
            :src="getCoverUrl(article.cover)" 
            :alt="article.title"
            class="w-full h-full object-cover"
          />
        </div>

        <!-- 文章内容 -->
        <div class="flex-1 p-6">
          <h2 class="text-xl font-bold mb-2">
            <router-link 
              :to="'/articles/' + article.id" 
              class="text-gray-900 hover:text-blue-600 transition-colors duration-200"
            >
              {{ article.title }}
            </router-link>
          </h2>

          <p class="text-gray-600 mb-4 line-clamp-2">{{ article.summary }}</p>

          <div class="flex flex-wrap items-center justify-between gap-4">
            <div class="flex items-center space-x-4 text-sm text-gray-500">
              <span class="flex items-center">
                <el-icon class="mr-1"><User /></el-icon>
                {{ article.authorName }}
              </span>
              <span class="flex items-center">
                <el-icon class="mr-1"><View /></el-icon>
                {{ article.viewCount }}
              </span>
              <span class="flex items-center">
                <el-icon class="mr-1"><ChatDotRound /></el-icon>
                {{ article.commentCount }}
              </span>
              <span class="flex items-center">
                <el-icon class="mr-1"><Star /></el-icon>
                {{ article.likeCount }}
              </span>
              <span class="flex items-center">
                <el-icon class="mr-1"><Collection /></el-icon>
                {{ article.favoriteCount }}
              </span>
              <span class="flex items-center">
                <el-icon class="mr-1"><Timer /></el-icon>
                {{ formatDate(article.createdAt) }}
              </span>
            </div>

            <div class="flex flex-wrap gap-2">
              <router-link 
                v-for="tag in article.tags" 
                :key="tag.id"
                :to="'/tags/' + tag.id"
                class="px-2 py-1 bg-gray-100 text-xs text-gray-600 rounded hover:bg-blue-100 hover:text-blue-600 transition-colors duration-200"
              >
                {{ tag.name }}
              </router-link>
            </div>
          </div>
        </div>
      </article>
    </div>

    <!-- 分页组件 -->
    <div class="mt-8 flex justify-center">
      <el-pagination
        :current-page="props.currentPage"
        :page-size="props.pageSize"
        :total="total"
        :page-sizes="[10, 20, 30]"
        layout="total, sizes, prev, pager, next"
        class="pagination-custom"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @update:current-page="(val) => emit('update:currentPage', val)"
        @update:page-size="(val) => emit('update:pageSize', val)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { User, View, ChatDotRound, Timer, Star, Collection } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/date'
import { getCoverUrl } from '@/utils/image'
import type { ArticleVO } from '@/types/article'

interface Props {
  articles: ArticleVO[]
  total: number
  modelValue: 'newest' | 'hottest' | 'recommended'
  viewMode: 'card' | 'list'
  currentPage?: number
  pageSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  currentPage: 1,
  pageSize: 10
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'update:currentPage', value: number): void
  (e: 'update:pageSize', value: number): void
  (e: 'page-change', page: number): void
  (e: 'size-change', size: number): void
}>()

const handleSizeChange = (val: number) => {
  emit('update:pageSize', val)
  emit('size-change', val)
}

const handleCurrentChange = (val: number) => {
  emit('update:currentPage', val)
  emit('page-change', val)
}
</script>

<style scoped>
.pagination-custom {
  --el-pagination-button-bg-color: transparent;
}

.pagination-custom :deep(.el-pager li.is-active) {
  background-color: var(--el-color-primary);
  color: white;
}
</style>