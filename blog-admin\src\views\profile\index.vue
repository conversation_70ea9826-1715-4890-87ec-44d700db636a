<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { useAdminStore } from '@/stores/admin'
import { getAvatarUrl, getRefreshedImageUrl, getAvatarText } from '@/utils/image'
import type { UploadChangeParam, UploadProps } from 'ant-design-vue'
import { updateAdminAvatar, updateAdminPassword } from '@/api/admin'

const adminStore = useAdminStore()
const loading = ref(false)
const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})
const passwordVisible = ref(false)

// 处理头像上传
const handleAvatarChange: UploadProps['onChange'] = async (info: UploadChangeParam) => {
  console.log('Upload change event:', info)
  // 获取原始文件对象
  const uploadFile = info.file
  const file = uploadFile instanceof File ? uploadFile : uploadFile.originFileObj

  if (!file) {
    console.log('No file selected')
    return
  }

  // 验证文件类型和大小
  if (!file.type.startsWith('image/')) {
    message.error('请选择图片文件')
    return
  }

  // 验证文件大小（2MB）
  if (file.size > 2 * 1024 * 1024) {
    message.error('图片大小不能超过2MB')
    return
  }

  console.log('Starting upload file:', {
    name: file.name,
    type: file.type,
    size: file.size
  })

  try {
    loading.value = true
    const response = await updateAdminAvatar(file)
    console.log('Upload response:', response)
    
    if (response.code === 200) {
      // 更新 store 中的用户信息，传入新的头像URL
      if (adminStore.user) {
        adminStore.user = {
          ...adminStore.user,
          avatar: response.message // 使用 message 字段作为头像URL
        }
      }
      // 刷新用户信息
      await adminStore.getAdminInfo()
      // 显示成功提示
      message.success({
        content: '头像更新成功',
        duration: 2
      })
    } else {
      message.error(response.message || '头像更新失败')
    }
  } catch (error) {
    console.error('Upload failed:', error)
    message.error('头像更新失败')
  } finally {
    loading.value = false
  }
}

// 处理密码修改
const handlePasswordUpdate = async () => {
  if (!passwordForm.value.oldPassword || !passwordForm.value.newPassword) {
    message.warning('请填写完整密码信息')
    return
  }
  
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    message.error('两次输入的新密码不一致')
    return
  }

  try {
    loading.value = true
    await updateAdminPassword({
      oldPassword: passwordForm.value.oldPassword,
      newPassword: passwordForm.value.newPassword
    })
    message.success('密码修改成功')
    passwordVisible.value = false
    passwordForm.value = {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
  } catch (error) {
    message.error('密码修改失败')
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="profile-page">
    <a-card title="个人资料" :bordered="false">
      <div class="flex items-start space-x-8">
        <!-- 头像上传 -->
        <div class="avatar-section">
          <div class="mb-4">
            <div class="avatar-wrapper w-24 h-24 rounded-full overflow-hidden">
              <img 
                v-if="adminStore.user?.avatar"
                :src="getRefreshedImageUrl(getAvatarUrl(adminStore.user.avatar))"
                :alt="adminStore.user?.username"
                class="w-full h-full object-cover"
              />
              <div 
                v-else 
                class="w-full h-full bg-blue-500 flex items-center justify-center text-white text-2xl"
              >
                {{ getAvatarText(adminStore.user?.username) }}
              </div>
            </div>
          </div>
          <a-upload
            name="avatar"
            :show-upload-list="false"
            :maxCount="1"
            :customRequest="() => {}"
            @change="handleAvatarChange"
            accept="image/*"
          >
            <a-button :loading="loading">更换头像</a-button>
          </a-upload>
        </div>

        <!-- 基本信息 -->
        <div class="flex-1">
          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2">用户名</label>
            <div class="text-gray-600">{{ adminStore.user?.username }}</div>
          </div>
          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2">邮箱</label>
            <div class="text-gray-600">{{ adminStore.user?.email }}</div>
          </div>
          <a-button type="primary" @click="passwordVisible = true">
            修改密码
          </a-button>
        </div>
      </div>
    </a-card>

    <!-- 修改密码弹窗 -->
    <a-modal
      v-model:open="passwordVisible"
      title="修改密码"
      @ok="handlePasswordUpdate"
      :confirm-loading="loading"
    >
      <a-form layout="vertical">
        <a-form-item label="原密码" required>
          <a-input-password v-model:value="passwordForm.oldPassword" placeholder="请输入原密码" />
        </a-form-item>
        <a-form-item label="新密码" required>
          <a-input-password v-model:value="passwordForm.newPassword" placeholder="请输入新密码" />
        </a-form-item>
        <a-form-item label="确认新密码" required>
          <a-input-password v-model:value="passwordForm.confirmPassword" placeholder="请再次输入新密码" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<style scoped>
.avatar-wrapper {
  border: 2px solid #f0f0f0;
}
</style> 