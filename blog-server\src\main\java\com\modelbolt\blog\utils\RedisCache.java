package com.modelbolt.blog.utils;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@RequiredArgsConstructor
public class RedisCache {
    private final RedisUtils redisUtils;

    public <T> void setCacheObject(String key, T value) {
        redisUtils.set(key, value);
    }

    public <T> void setCacheObject(String key, T value, long timeout) {
        redisUtils.set(key, value, timeout, TimeUnit.SECONDS);
    }

    public <T> T getCacheObject(String key) {
        return (T) redisUtils.get(key);
    }

    public boolean deleteObject(String key) {
        return redisUtils.delete(key);
    }
} 