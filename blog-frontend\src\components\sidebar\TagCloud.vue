<!-- 标签云组件 -->
<template>
  <div class="bg-white rounded-lg shadow-sm p-6">
    <h3 class="text-lg font-bold mb-4 flex items-center">
      <el-icon class="mr-2 text-blue-600"><Collection /></el-icon>
      标签云
    </h3>
    <div class="flex flex-wrap gap-2">
      <router-link
        v-for="tag in tags"
        :key="tag.id"
        :to="'/tags/' + tag.id"
        class="px-3 py-1.5 rounded-full text-sm transition-all duration-200 relative group"
        :style="getTagStyle(tag.articleCount)"
      >
        {{ tag.name }}
        <span 
          class="absolute -top-2 -right-2 min-w-[20px] h-5 px-1 rounded-full bg-blue-100 text-blue-600 text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"
        >
          {{ tag.articleCount }}
        </span>
      </router-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Collection } from '@element-plus/icons-vue'
import type { TagVO } from '@/types/article'

const props = defineProps<{
  tags: TagVO[]
}>()

const getTagStyle = (count: number) => {
  // 根据文章数量计算标签的样式
  const minCount = 1
  const maxCount = Math.max(...props.tags.map(tag => tag.articleCount))
  const minSize = 0.8
  const maxSize = 1.4
  const minOpacity = 0.6
  const maxOpacity = 1

  // 计算字体大小和透明度
  const size = minSize + (count - minCount) * (maxSize - minSize) / (maxCount - minCount || 1)
  const opacity = minOpacity + (count - minCount) * (maxOpacity - minOpacity) / (maxCount - minCount || 1)

  // 生成随机的柔和背景色
  const hue = Math.floor(Math.random() * 360)
  const saturation = 85 + Math.random() * 15
  const lightness = 90 + Math.random() * 5

  return {
    fontSize: `${size}rem`,
    opacity: opacity,
    backgroundColor: `hsl(${hue}, ${saturation}%, ${lightness}%)`,
    color: `hsl(${hue}, ${saturation - 30}%, 30%)`,
  }
}
</script>