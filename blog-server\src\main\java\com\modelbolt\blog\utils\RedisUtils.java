package com.modelbolt.blog.utils;

import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.Set;
import java.util.Collections;
import java.util.logging.Logger;

@Component
@RequiredArgsConstructor
public class RedisUtils {

    private static final Logger log = Logger.getLogger(RedisUtils.class.getName());

    private final RedisTemplate<String, Object> redisTemplate;

    public void set(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }

    public void set(String key, Object value, long timeout, TimeUnit unit) {
        redisTemplate.opsForValue().set(key, value, timeout, unit);
    }

    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    public Boolean delete(String key) {
        return redisTemplate.delete(key);
    }

    public Boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    public Long getExpire(String key) {
        return redisTemplate.getExpire(key);
    }

    public Boolean expire(String key, long timeout, TimeUnit unit) {
        return redisTemplate.expire(key, timeout, unit);
    }

    public Long increment(String key, long delta) {
        return redisTemplate.opsForValue().increment(key, delta);
    }

    public Long decrement(String key, long delta) {
        return redisTemplate.opsForValue().decrement(key, delta);
    }

    public Boolean setIfAbsent(String key, Object value) {
        return redisTemplate.opsForValue().setIfAbsent(key, value);
    }

    public Boolean setIfAbsent(String key, Object value, long timeout, TimeUnit unit) {
        return redisTemplate.opsForValue().setIfAbsent(key, value, timeout, unit);
    }

    /**
     * 将元素添加到Set集合
     * @param key Redis key
     * @param value 值
     * @return 添加成功返回true，值已存在返回false
     */
    public Boolean setAdd(String key, Object value) {
        try {
            Long result = redisTemplate.opsForSet().add(key, value);
            return result != null && result > 0;
        } catch (Exception e) {
            log.severe("Failed to add value to set: " + key);
            return false;
        }
    }

    /**
     * 从Set集合中移除元素
     * @param key Redis key
     * @param value 值
     * @return 移除成功返回true，值不存在返回false
     */
    public Boolean setRemove(String key, Object value) {
        try {
            Long result = redisTemplate.opsForSet().remove(key, value);
            return result != null && result > 0;
        } catch (Exception e) {
            log.severe("Failed to remove value from set: " + key);
            return false;
        }
    }

    /**
     * 检查Set集合中是否包含元素
     * @param key Redis key
     * @param value 值
     * @return 包含返回true，不包含返回false
     */
    public Boolean setContains(String key, Object value) {
        try {
            return redisTemplate.opsForSet().isMember(key, value);
        } catch (Exception e) {
            log.severe("Failed to check if set contains value: " + key);
            return false;
        }
    }

    /**
     * 获取Set集合的大小
     * @param key Redis key
     * @return Set集合大小
     */
    public Long setSize(String key) {
        try {
            return redisTemplate.opsForSet().size(key);
        } catch (Exception e) {
            log.severe("Failed to get set size: " + key);
            return 0L;
        }
    }

    /**
     * 获取Set集合中的所有成员
     * @param key Redis key
     * @return Set集合中的所有成员
     */
    public Set<Object> setMembers(String key) {
        try {
            return redisTemplate.opsForSet().members(key);
        } catch (Exception e) {
            log.severe("Failed to get set members: " + key);
            return Collections.emptySet();
        }
    }

    /**
     * 获取匹配的key集合
     * @param pattern key模式
     * @return key集合
     */
    public Set<String> keys(String pattern) {
        try {
            return redisTemplate.keys(pattern);
        } catch (Exception e) {
            log.severe("Failed to get keys for pattern: " + pattern);
            return Collections.emptySet();
        }
    }
} 