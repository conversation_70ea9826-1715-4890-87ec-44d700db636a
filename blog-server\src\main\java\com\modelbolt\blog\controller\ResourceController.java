package com.modelbolt.blog.controller;

import com.modelbolt.blog.service.FileService;
import com.modelbolt.blog.utils.FileUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.HandlerMapping;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

/**
 * 资源管理控制器
 * 处理图片等静态资源的访问请求，包括头像、封面图等
 * 实现资源不存在时返回默认资源的功能
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Controller
@RequestMapping("/api/images")
@RequiredArgsConstructor
public class ResourceController {

    private final FileUtils fileUtils;
    private final FileService fileService;

    /** 默认头像文件名 */
    private static final String DEFAULT_AVATAR = "default-avatar.png";
    
    /** 默认封面文件名 */
    private static final String DEFAULT_COVER = "article/cover/default-cover.jpg";
    
    /** 图片缓存时间（秒） */
    private static final long IMAGE_CACHE_DURATION = 86400; // 一天

    /**
     * 通过路径参数获取图片
     * 支持图片缓存功能
     * 
     * @param subDir 子目录
     * @param filename 文件名
     * @return 图片数据
     */
    @GetMapping("/{subDir}/{filename}")
    public ResponseEntity<byte[]> getImageByPath(@PathVariable String subDir, @PathVariable String filename) {
        String fileUrl = "/" + subDir + "/" + filename;
        log.debug("通过路径获取图片: {}", fileUrl);
        
        // 尝试从缓存获取图片
        String cachedImage = fileService.getImageFromCache(fileUrl);
        if (cachedImage != null) {
            log.debug("命中图片缓存: {}", fileUrl);
            byte[] imageBytes = Base64.getDecoder().decode(cachedImage);
            return ResponseEntity.ok()
                    .contentType(getMediaType(filename))
                    .body(imageBytes);
        }
        
        // 缓存未命中，从文件系统读取
        try {
            Path imagePath = Paths.get(fileUtils.getUploadPath(), subDir, filename);
            if (Files.exists(imagePath)) {
                log.debug("从文件系统读取图片: {}", imagePath);
                byte[] imageBytes = Files.readAllBytes(imagePath);
                
                // 将图片缓存到Redis
                fileService.cacheImage(fileUrl, imageBytes, IMAGE_CACHE_DURATION, TimeUnit.SECONDS);
                
                return ResponseEntity.ok()
                        .contentType(getMediaType(filename))
                        .body(imageBytes);
            } else {
                log.warn("图片文件不存在: {}", imagePath);
                
                // 如果是头像或封面，返回默认资源
                if (subDir.equalsIgnoreCase("avatar")) {
                    return getDefaultAvatar();
                } else if (subDir.equalsIgnoreCase("cover")) {
                    return getDefaultCover();
                }
            }
        } catch (IOException e) {
            log.error("读取图片文件失败: {}", e.getMessage());
        }
        
        return ResponseEntity.notFound().build();
    }

    /**
     * 处理头像请求
     * 确保头像不存在时返回默认头像
     * 
     * @param request HTTP请求
     * @return 头像资源
     */
    @GetMapping("/avatar/**")
    public ResponseEntity<Resource> getAvatar(HttpServletRequest request) {
        // 获取完整请求路径
        String path = (String) request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE);
        if (path == null) {
            return ResponseEntity.notFound().build();
        }
        
        // 提取相对路径
        String relativePath = path.replaceFirst("/api/images/avatar/", "");
        log.debug("处理头像请求: {}", relativePath);
        
        // 如果请求的是默认头像或相对路径为空，直接返回默认头像
        if (relativePath.isEmpty() || relativePath.equals(DEFAULT_AVATAR)) {
            log.debug("返回默认头像");
            return serveDefaultAvatar();
        }
        
        // 检查请求的头像是否存在
        if (!fileUtils.fileExists("/images/avatar/" + relativePath)) {
            log.warn("请求的头像不存在: {}, 返回默认头像", relativePath);
            return serveDefaultAvatar();
        }
        
        // 返回请求的头像
        return serveResource("/avatar/" + relativePath);
    }

    /**
     * 处理封面图片请求
     * 确保封面不存在时返回默认封面
     * 
     * @param request HTTP请求
     * @return 封面资源
     */
    @GetMapping("/cover/**")
    public ResponseEntity<Resource> getCover(HttpServletRequest request) {
        // 获取完整请求路径
        String path = (String) request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE);
        if (path == null) {
            return ResponseEntity.notFound().build();
        }
        
        // 提取相对路径
        String relativePath = path.replaceFirst("/api/images/cover/", "");
        log.debug("处理封面请求: {}", relativePath);
        
        // 如果请求的是默认封面或相对路径为空，直接返回默认封面
        if (relativePath.isEmpty() || relativePath.equals(DEFAULT_COVER)) {
            log.debug("返回默认封面");
            return serveDefaultCover();
        }
        
        // 检查请求的封面是否存在
        if (!fileUtils.fileExists("/images/cover/" + relativePath)) {
            log.warn("请求的封面不存在: {}, 返回默认封面", relativePath);
            return serveDefaultCover();
        }
        
        // 返回请求的封面
        return serveResource("/cover/" + relativePath);
    }

    /**
     * 处理通用图片请求
     * 根据请求类型返回相应资源
     * 
     * @param request HTTP请求
     * @return 图片资源
     */
    @GetMapping("/**")
    public ResponseEntity<Resource> getImage(HttpServletRequest request) {
        // 获取完整请求路径
        String path = (String) request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE);
        if (path == null) {
            return ResponseEntity.notFound().build();
        }
        
        // 提取相对路径
        String relativePath = path.replaceFirst("/api/images/", "");
        log.debug("处理通用图片请求: {}", relativePath);
        
        // 如果路径包含avatar，重定向到头像处理
        if (relativePath.startsWith("avatar/")) {
            return getAvatar(request);
        }
        
        // 如果路径包含cover，重定向到封面处理
        if (relativePath.startsWith("cover/")) {
            return getCover(request);
        }

        // 检查请求的图片是否存在
        if (!fileUtils.fileExists("/images/" + relativePath)) {
            log.warn("请求的图片不存在: {}", relativePath);
            // 根据请求的图片类型返回对应的默认图片
            if (relativePath.equals(DEFAULT_AVATAR) || relativePath.contains("avatar")) {
                return serveDefaultAvatar();
            } else if (relativePath.equals(DEFAULT_COVER) || relativePath.contains("cover")) {
                return serveDefaultCover();
            } else {
                // 其他类型图片不存在时返回404
                return ResponseEntity.notFound().build();
            }
        }
        
        // 返回请求的图片
        return serveResource("/" + relativePath);
    }

    /**
     * 获取默认头像（字节数组响应）
     * 
     * @return 默认头像的字节数组
     */
    private ResponseEntity<byte[]> getDefaultAvatar() {
        try {
            Path defaultAvatarPath = Paths.get(fileUtils.getUploadPath(), "images", "avatar", DEFAULT_AVATAR);
            if (Files.exists(defaultAvatarPath)) {
                byte[] imageBytes = Files.readAllBytes(defaultAvatarPath);
                return ResponseEntity.ok()
                        .contentType(MediaType.IMAGE_PNG)
                        .body(imageBytes);
            }
        } catch (IOException e) {
            log.error("读取默认头像失败: {}", e.getMessage());
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 获取默认封面（字节数组响应）
     * 
     * @return 默认封面的字节数组
     */
    private ResponseEntity<byte[]> getDefaultCover() {
        try {
            Path defaultCoverPath = Paths.get(fileUtils.getUploadPath(), "images", DEFAULT_COVER);
            if (Files.exists(defaultCoverPath)) {
                byte[] imageBytes = Files.readAllBytes(defaultCoverPath);
                return ResponseEntity.ok()
                        .contentType(MediaType.IMAGE_JPEG)
                        .body(imageBytes);
            }
        } catch (IOException e) {
            log.error("读取默认封面失败: {}", e.getMessage());
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 返回默认头像资源
     * 
     * @return 默认头像的Resource响应
     */
    private ResponseEntity<Resource> serveDefaultAvatar() {
        return serveResource("/avatar/" + DEFAULT_AVATAR);
    }

    /**
     * 返回默认封面资源
     * 
     * @return 默认封面的Resource响应
     */
    private ResponseEntity<Resource> serveDefaultCover() {
        return serveResource("/" + DEFAULT_COVER);
    }

    /**
     * 返回资源文件
     * 
     * @param resourcePath 资源路径
     * @return 资源响应
     */
    private ResponseEntity<Resource> serveResource(String resourcePath) {
        try {
            // 构建资源路径
            Path filePath = Paths.get(fileUtils.getUploadPath(), "images", resourcePath);
            File file = filePath.toFile();
            
            // 如果文件不存在，记录错误并返回404
            if (!file.exists() || !file.isFile()) {
                log.error("文件不存在: {}", filePath);
                return ResponseEntity.notFound().build();
            }
            
            // 创建资源对象
            Resource resource = new FileSystemResource(file);
            
            // 确定媒体类型
            MediaType mediaType = getMediaType(file.getName());
            
            // 返回资源响应
            return ResponseEntity.ok()
                    .contentType(mediaType)
                    .body(resource);
        } catch (Exception e) {
            log.error("读取文件失败: {}", resourcePath, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 根据文件名确定媒体类型
     * 
     * @param filename 文件名
     * @return 媒体类型
     */
    private MediaType getMediaType(String filename) {
        String lowerFilename = filename.toLowerCase();
        if (lowerFilename.endsWith(".jpg") || lowerFilename.endsWith(".jpeg")) {
            return MediaType.IMAGE_JPEG;
        } else if (lowerFilename.endsWith(".png")) {
            return MediaType.IMAGE_PNG;
        } else if (lowerFilename.endsWith(".gif")) {
            return MediaType.IMAGE_GIF;
        } else if (lowerFilename.endsWith(".webp")) {
            return MediaType.parseMediaType("image/webp");
        } else if (lowerFilename.endsWith(".svg")) {
            return MediaType.parseMediaType("image/svg+xml");
        } else {
            return MediaType.APPLICATION_OCTET_STREAM;
        }
    }
} 