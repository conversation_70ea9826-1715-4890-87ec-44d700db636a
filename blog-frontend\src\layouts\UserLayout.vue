<template>
  <div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row gap-8">
      <!-- 侧边栏导航 -->
      <div class="md:w-64">
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-lg font-medium text-gray-900 mb-4">个人中心</h2>
          <nav class="space-y-2">
            <router-link
              v-for="item in menuItems"
              :key="item.path"
              :to="item.path"
              class="flex items-center px-4 py-2 text-sm rounded-lg transition-colors duration-200"
              :class="[
                $route.path === item.path
                  ? 'bg-blue-50 text-blue-600'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              ]"
            >
              <el-icon class="mr-3">
                <component :is="item.icon" />
              </el-icon>
              {{ item.title }}
            </router-link>
          </nav>
        </div>
      </div>

      <!-- 主要内容区 -->
      <div class="flex-1">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  User,
  Document,
  ChatDotRound,
  Star,
  Bell,
  Setting,
  UserFilled
} from '@element-plus/icons-vue'
import { useSettingsStore } from '@/stores/settings'

const settingsStore = useSettingsStore()

// 使用 computed 来动态生成菜单项
const menuItems = computed(() => {
  const items = [
    {
      title: '个人资料',
      path: '/user/profile',
      icon: User
    },
    {
      title: '我的文章',
      path: '/user/articles',
      icon: Document
    },
    {
      title: '我的评论',
      path: '/user/comments',
      icon: ChatDotRound
    },
    {
      title: '我的收藏',
      path: '/user/favorites',
      icon: Star
    },
    {
      title: '关注管理',
      path: '/user/follows',
      icon: UserFilled
    }
  ]

  // 只有在通知功能开启时才显示消息通知菜单
  if (settingsStore.settings.notificationEnabled) {
    items.push({
      title: '消息通知',
      path: '/user/messages',
      icon: Bell
    })
  }

  return items
})
</script> 