package com.modelbolt.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.modelbolt.blog.model.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;

@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    @Select("SELECT * FROM user WHERE username = #{username}")
    User findByUsername(String username);
    
    @Select("SELECT * FROM user WHERE email = #{email}")
    User findByEmail(String email);

    @Select("SELECT COUNT(*) FROM user")
    int count();

    @Select("SELECT COUNT(*) FROM user WHERE DATE(created_at) = #{date}")
    int getNewUserCountByDate(LocalDate date);

    @Select("SELECT username FROM user WHERE id = #{id}")
    String selectUsernameById(Long id);

    @Select("SELECT avatar FROM user WHERE id = #{id}")
    String selectUserAvatarById(Long id);

    // 获取作者的文章总数
    @Select("SELECT COUNT(*) FROM article WHERE author_id = #{userId} AND status = 1")
    int getArticleCount(Long userId);

    // 获取作者获得的总点赞数
    @Select("SELECT COALESCE(SUM(like_count), 0) FROM article WHERE author_id = #{userId} AND status = 1")
    int getTotalLikes(Long userId);

    // 获取作者的粉丝数
    @Select("SELECT COUNT(*) FROM user_follow WHERE following_id = #{userId}")
    int getFollowersCount(Long userId);
}