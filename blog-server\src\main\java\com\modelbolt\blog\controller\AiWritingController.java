package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.model.dto.ai.AiWritingRequest;
import com.modelbolt.blog.model.dto.ai.AiWritingResponse;
import com.modelbolt.blog.model.dto.ai.ChatRequest;
import com.modelbolt.blog.model.dto.ai.ChatResponse;
import com.modelbolt.blog.service.AiWritingService;
import com.modelbolt.blog.utils.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Tag(name = "AI写作接口", description = "AI写作助手相关接口")
@RestController
@RequestMapping("/ai/writing")
@RequiredArgsConstructor
public class AiWritingController {

    private final AiWritingService aiWritingService;

    @Operation(summary = "处理AI写作请求", description = "根据不同类型处理AI写作请求，包括创意生成、内容优化和SEO建议")
    @PostMapping("/generate")
    public ApiResponse<AiWritingResponse> processWritingRequest(@RequestBody @Valid AiWritingRequest request) {
        // 获取当前用户ID
        Long userId = SecurityUtils.getCurrentUserId();
        
        // 验证用户配额
        if (!aiWritingService.validateUserQuota(userId)) {
            return ApiResponse.error(403, "已超过今日AI使用限额");
        }

        try {
            // 处理写作请求
            AiWritingResponse response = aiWritingService.processWritingRequest(request);
            
            // 记录使用情况（这里使用一个固定值，实际应该根据响应内容计算）
            aiWritingService.recordUsage(userId, 500);
            
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("AI写作请求处理失败", e);
            return ApiResponse.error(500, "AI写作请求处理失败：" + e.getMessage());
        }
    }

    @Operation(summary = "发送聊天消息", description = "发送聊天消息以调整文章内容")
    @PostMapping("/chat")
    public ApiResponse<ChatResponse> chat(@RequestBody @Valid ChatRequest request) {
        // 获取当前用户ID
        Long userId = SecurityUtils.getCurrentUserId();
        
        // 验证用户配额
        if (!aiWritingService.validateUserQuota(userId)) {
            return ApiResponse.error(403, "已超过今日AI使用限额");
        }

        try {
            // 处理聊天请求
            ChatResponse response = aiWritingService.processChat(request);
            
            // 记录使用情况
            aiWritingService.recordUsage(userId, 200);
            
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("聊天请求处理失败", e);
            return ApiResponse.error(500, "聊天请求处理失败：" + e.getMessage());
        }
    }

    @Operation(summary = "检查用户配额", description = "检查用户是否还有AI使用配额")
    @GetMapping("/quota/check")
    public ApiResponse<Boolean> checkQuota() {
        Long userId = SecurityUtils.getCurrentUserId();
        return ApiResponse.success(aiWritingService.validateUserQuota(userId));
    }
} 