<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted } from 'vue'
import { 
  FileTextOutlined,
  CommentOutlined,
  UserOutlined,
  EyeOutlined
} from '@ant-design/icons-vue'
import { http } from '@/utils/request'
import LineChart from '@/components/charts/LineChart.vue'
import Pie<PERSON>hart from '@/components/charts/PieChart.vue'
import { useWebSocketStore } from '@/stores/websocket'

interface DashboardData {
  articleCount: number
  commentCount: number
  userCount: number
  viewCount: number
  visitStats: {
    date: string
    count: number
  }[]
  userGrowth: {
    date: string
    count: number
  }[]
  contentStats: {
    type: string
    count: number
  }[]
}

const loading = ref(true)
const stats = ref<DashboardData>({
  articleCount: 0,
  commentCount: 0,
  userCount: 0,
  viewCount: 0,
  visitStats: [],
  userGrowth: [],
  contentStats: []
})

const webSocketStore = useWebSocketStore()

const fetchDashboardData = async () => {
  try {
    loading.value = true
    const response = await http.get<{code: number, data: DashboardData, message: string}>('/dashboard/stats')
    console.log('Dashboard API response:', response)
    
    if (response.code === 200 && response.data) {
      // 确保所有必要的数据都存在，如果不存在则使用默认值
      stats.value = {
        articleCount: response.data.articleCount ?? 0,
        commentCount: response.data.commentCount ?? 0,
        userCount: response.data.userCount ?? 0,
        viewCount: response.data.viewCount ?? 0,
        visitStats: Array.isArray(response.data.visitStats) ? response.data.visitStats : [],
        userGrowth: Array.isArray(response.data.userGrowth) ? response.data.userGrowth : [],
        contentStats: Array.isArray(response.data.contentStats) ? response.data.contentStats : []
      }

      // 更新实时数据
      realTimeStats.value = {
        onlineUsers: response.data.onlineUsers ?? 0,
        todayVisits: response.data.todayVisits ?? 0,
        todayComments: response.data.todayComments ?? 0,
        systemLoad: response.data.systemLoad ?? '0%'
      }
    }
    console.log('Processed dashboard data:', stats.value)
    console.log('Processed realtime stats:', realTimeStats.value)
  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
    // 发生错误时使用默认值
    stats.value = {
      articleCount: 0,
      commentCount: 0,
      userCount: 0,
      viewCount: 0,
      visitStats: [],
      userGrowth: [],
      contentStats: []
    }
    realTimeStats.value = {
      onlineUsers: 0,
      todayVisits: 0,
      todayComments: 0,
      systemLoad: '0%'
    }
  } finally {
    loading.value = false
  }
}

// 使用store中的实时数据
const realTimeStats = computed(() => webSocketStore.realTimeStats)

onMounted(() => {
  fetchDashboardData()
  webSocketStore.initWebSocket()
})

onUnmounted(() => {
  webSocketStore.closeWebSocket()
})

// 处理访问统计数据
const visitChartData = computed(() => ({
  xData: stats.value?.visitStats?.map(item => item.date) || [],
  yData: stats.value?.visitStats?.map(item => item.count) || []
}))

// 处理用户增长数据
const userGrowthData = computed(() => ({
  xData: stats.value?.userGrowth?.map(item => item.date) || [],
  yData: stats.value?.userGrowth?.map(item => item.count) || []
}))
</script>

<template>
  <div class="dashboard">
    <a-row :gutter="[16, 16]">
      <!-- 统计卡片 -->
      <a-col :span="6">
        <a-card :loading="loading">
          <a-statistic
            title="文章数"
            :value="stats.articleCount"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <FileTextOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card :loading="loading">
          <a-statistic
            title="评论数"
            :value="stats.commentCount"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <CommentOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card :loading="loading">
          <a-statistic
            title="用户数"
            :value="stats.userCount"
            :value-style="{ color: '#faad14' }"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card :loading="loading">
          <a-statistic
            title="总浏览量"
            :value="stats.viewCount"
            :value-style="{ color: '#722ed1' }"
          >
            <template #prefix>
              <EyeOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 图表区域 -->
    <a-row :gutter="[16, 16]" class="mt-4">
      <!-- 访问统计 -->
      <a-col :span="12">
        <a-card title="访问统计" :loading="loading">
          <LineChart
            :loading="loading"
            :x-data="visitChartData.xData"
            :y-data="visitChartData.yData"
            color="#1890ff"
          />
        </a-card>
      </a-col>
      
      <!-- 用户增长 -->
      <a-col :span="12">
        <a-card title="用户增长" :loading="loading">
          <LineChart
            :loading="loading"
            :x-data="userGrowthData.xData"
            :y-data="userGrowthData.yData"
            color="#52c41a"
          />
        </a-card>
      </a-col>
    </a-row>

    <!-- 内容统计 -->
    <a-row :gutter="[16, 16]" class="mt-4">
      <a-col :span="12">
        <a-card title="内容分类统计" :loading="loading">
          <PieChart
            :loading="loading"
            :data="stats.contentStats"
            title="内容分类"
          />
        </a-card>
      </a-col>

      <!-- 实时数据 -->
      <a-col :span="12">
        <a-card title="实时监控" :loading="loading">
          <div class="space-y-4">
            <a-row>
              <a-col :span="12">
                <div class="text-center">
                  <p class="text-lg font-medium">在线用户</p>
                  <h3 class="text-2xl font-bold text-blue-500">
                    {{ realTimeStats.onlineUsers }}
                  </h3>
                </div>
              </a-col>
              <a-col :span="12">
                <div class="text-center">
                  <p class="text-lg font-medium">今日访问</p>
                  <h3 class="text-2xl font-bold text-green-500">
                    {{ realTimeStats.todayVisits }}
                  </h3>
                </div>
              </a-col>
            </a-row>
            <a-row class="mt-4">
              <a-col :span="12">
                <div class="text-center">
                  <p class="text-lg font-medium">今日评论</p>
                  <h3 class="text-2xl font-bold text-orange-500">
                    {{ realTimeStats.todayComments }}
                  </h3>
                </div>
              </a-col>
              <a-col :span="12">
                <div class="text-center">
                  <p class="text-lg font-medium">系统负载</p>
                  <h3 class="text-2xl font-bold text-purple-500">
                    {{ realTimeStats.systemLoad }}
                  </h3>
                </div>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>