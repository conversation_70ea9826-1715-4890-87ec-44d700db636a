package com.modelbolt.blog.utils;

import com.modelbolt.blog.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 文件工具类
 * 提供文件上传、下载、验证等基础功能
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Component
public class FileUtils {

    /** 允许的文件类型列表 */
    @Value("${file.upload.allowed-types}")
    private List<String> allowedTypes;

    /** 最大文件大小限制（字节） */
    @Value("${file.upload.max-size}")
    private Long maxSize;

    /** 文件上传根路径 */
    @Value("${file.upload.path}")
    private String uploadPath;

    /** 文件URL前缀 */
    @Value("${file.upload.url-prefix}")
    private String urlPrefix;

    /** 头像存储子目录 */
    @Value("${file.upload.subdirs.avatar}")
    private String avatarDir;
    
    /** 默认头像文件名 */
    private static final String DEFAULT_AVATAR = "default-avatar.png";
    
    /** 默认封面文件名 */
    private static final String DEFAULT_COVER = "article/cover/default-cover.jpg";

    /**
     * 文件验证
     * 验证文件大小和类型是否符合系统要求
     * 
     * @param file 待验证的文件
     * @param allowedExtensions 允许的文件扩展名，如果为空则使用系统配置的允许类型
     * @throws IllegalArgumentException 当文件验证失败时抛出异常
     */
    public void validateFile(MultipartFile file, String... allowedExtensions) {
        // 检查文件是否为空
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > maxSize) {
            throw new IllegalArgumentException("文件大小超过限制: " + maxSize / 1024 / 1024 + "MB");
        }

        // 获取原始文件名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        // 获取文件扩展名
        String extension = getFileExtension(originalFilename);
        if (extension == null) {
            throw new IllegalArgumentException("不支持的文件类型");
        }

        // 验证文件类型
        if (allowedExtensions != null && allowedExtensions.length > 0) {
            // 使用自定义的允许扩展名列表
            boolean isAllowed = false;
            for (String ext : allowedExtensions) {
                if (ext.equalsIgnoreCase(extension)) {
                    isAllowed = true;
                    break;
                }
            }
            if (!isAllowed) {
                throw new IllegalArgumentException("不支持的文件类型，允许的类型：" + String.join(", ", allowedExtensions));
            }
        } else {
            // 使用系统配置的允许类型
            if (!allowedTypes.contains(extension.toLowerCase())) {
                throw new IllegalArgumentException("不支持的文件类型，允许的类型：" + String.join(", ", allowedTypes));
            }
        }
    }

    /**
     * 保存文件到指定目录
     * 
     * @param file 文件对象
     * @param subDir 子目录名称
     * @return 文件访问URL
     * @throws IOException 文件保存失败时抛出异常
     */
    public String saveFile(MultipartFile file, String subDir) throws IOException {
        // 验证文件
        validateFile(file);

        // 获取文件信息
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        // 获取文件扩展名
        String extension = getFileExtension(originalFilename);
        if (extension == null) {
            throw new IllegalArgumentException("文件扩展名不能为空");
        }

        // 生成安全的文件名
        String newFilename = generateFilename(extension);

        // 确保目标目录存在
        File directory = new File(uploadPath + File.separator + subDir);
        if (!directory.exists()) {
            boolean created = directory.mkdirs();
            if (!created) {
                log.error("创建目录失败: {}", directory.getPath());
                throw new IOException("创建目录失败：" + directory.getPath());
            }
        }

        // 创建目标文件
        File targetFile = new File(directory, newFilename);

        // 保存文件
        try {
            file.transferTo(targetFile);
            log.info("文件保存成功: {}", targetFile.getAbsolutePath());
        } catch (IOException e) {
            log.error("文件保存失败: {}", e.getMessage());
            throw e;
        }

        // 返回文件URL
        return urlPrefix + "/" + subDir + "/" + newFilename;
    }

    /**
     * 保存头像文件
     * 
     * @param file 头像文件
     * @return 头像URL
     * @throws IOException 文件保存失败时抛出异常
     */
    public String saveAvatar(MultipartFile file) throws IOException {
        return saveFile(file, avatarDir);
    }

    /**
     * 删除文件
     * 
     * @param fileUrl 文件URL
     */
    public void deleteFile(String fileUrl) {
        if (StringUtils.isEmpty(fileUrl)) {
            log.warn("文件URL为空，无法删除");
            return;
        }

        // 处理URL格式，获取文件相对路径
        String relativePath = fileUrl;
        if (fileUrl.startsWith(urlPrefix)) {
            relativePath = fileUrl.substring(urlPrefix.length());
        } else if (fileUrl.startsWith("/")) {
            relativePath = fileUrl.substring(1);
        }

        // 构建绝对路径
        File file = new File(uploadPath, relativePath);

        // 检查文件是否存在
        if (!file.exists()) {
            log.warn("文件不存在，无法删除: {}", file.getAbsolutePath());
            return;
        }

        // 删除文件
        boolean deleted = file.delete();
        if (deleted) {
            log.info("文件删除成功: {}", file.getAbsolutePath());
        } else {
            log.error("文件删除失败: {}", file.getAbsolutePath());
        }
    }

    /**
     * 获取文件扩展名
     * 
     * @param filename 文件名
     * @return 扩展名，不包含点号(.)
     */
    private String getFileExtension(String filename) {
        int dotIndex = filename.lastIndexOf('.');
        if (dotIndex < 0) {
            return null;
        }
        return filename.substring(dotIndex + 1);
    }

    /**
     * 生成安全的文件名
     * 防止文件名冲突和安全问题
     * 
     * @param extension 文件扩展名，不包含点号(.)
     * @return 新的文件名，包含扩展名
     */
    private String generateFilename(String extension) {
        // 生成UUID
        String uuid = UUID.randomUUID().toString().replace("-", "");

        // 获取当前日期时间
        String dateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

        // 返回 UUID + 日期时间 + 扩展名
        return uuid + "_" + dateTime + "." + extension;
    }

    /**
     * 检查文件是否存在
     * 
     * @param path 文件路径
     * @return 是否存在
     */
    public boolean fileExists(String path) {
        if (StringUtils.isEmpty(path)) {
            return false;
        }
        
        // 处理相对路径
        String relativePath = path;
        if (path.startsWith(urlPrefix)) {
            relativePath = path.substring(urlPrefix.length());
        } else if (path.startsWith("/")) {
            relativePath = path.substring(1);
        }
        
        // 构建绝对路径
        Path filePath = Paths.get(uploadPath, relativePath);
        
        return Files.exists(filePath);
    }
    
    /**
     * 获取有效的头像URL
     * 确保头像文件存在，不存在时返回默认头像
     * 
     * @param avatarPath 头像路径
     * @return 有效的头像URL
     */
    public String ensureAvatarExists(String avatarPath) {
        // 如果路径为空或为默认头像，直接返回默认头像路径
        if (StringUtils.isEmpty(avatarPath) || avatarPath.contains(DEFAULT_AVATAR)) {
            return urlPrefix + "/" + DEFAULT_AVATAR;
        }
        
        // 如果是完整URL，直接返回
        if (avatarPath.startsWith("http://") || avatarPath.startsWith("https://")) {
            return avatarPath;
        }
        
        // 检查头像文件是否存在
        if (!fileExists(avatarPath)) {
            log.warn("头像文件不存在: {}, 使用默认头像", avatarPath);
            return urlPrefix + "/" + DEFAULT_AVATAR;
        }
        
        return avatarPath;
    }
    
    /**
     * 获取有效的封面URL
     * 确保封面文件存在，不存在时返回默认封面
     * 
     * @param coverPath 封面路径
     * @return 有效的封面URL
     */
    public String ensureCoverExists(String coverPath) {
        // 如果路径为空或为默认封面，直接返回默认封面路径
        if (StringUtils.isEmpty(coverPath) || coverPath.contains(DEFAULT_COVER)) {
            return urlPrefix + "/" + DEFAULT_COVER;
        }
        
        // 如果是完整URL，直接返回
        if (coverPath.startsWith("http://") || coverPath.startsWith("https://")) {
            return coverPath;
        }
        
        // 检查封面文件是否存在
        if (!fileExists(coverPath)) {
            log.warn("封面文件不存在: {}, 使用默认封面", coverPath);
            return urlPrefix + "/" + DEFAULT_COVER;
        }
        
        return coverPath;
    }
    
    /**
     * 获取上传文件根路径
     * 
     * @return 上传文件根路径
     */
    public String getUploadPath() {
        return uploadPath;
    }
    
    /**
     * 获取文件URL前缀
     * 
     * @return 文件URL前缀
     */
    public String getUrlPrefix() {
        return urlPrefix;
    }
    
    /**
     * 获取默认头像文件名
     * 
     * @return 默认头像文件名
     */
    public String getDefaultAvatarFilename() {
        return DEFAULT_AVATAR;
    }
    
    /**
     * 获取默认封面文件名
     * 
     * @return 默认封面文件名
     */
    public String getDefaultCoverFilename() {
        return DEFAULT_COVER;
    }
} 