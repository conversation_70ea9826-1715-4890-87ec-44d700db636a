<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { UserOutlined, LockOutlined, MailOutlined, KeyOutlined } from '@ant-design/icons-vue'
import { useAdminStore } from '@/stores/admin'

const router = useRouter()
const adminStore = useAdminStore()

const loginForm = reactive({
  username: '',
  password: '',
  verificationCode: ''
})

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  verificationCode: [
    { required: false, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码长度为6位', trigger: 'blur' }
  ]
}

const loading = ref(false)
const showVerification = ref(false)
const sendingCode = ref(false)
const countdown = ref(0)

const startCountdown = () => {
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

const handleSendCode = async () => {
  if (!loginForm.username) {
    message.error('请先输入用户名')
    return
  }
  
  try {
    sendingCode.value = true
    if (!adminStore.user?.email) {
      // 如果还没有用户信息，先尝试登录
      const result = await adminStore.login({
        username: loginForm.username,
        password: loginForm.password
      })
      
      if (!result.needVerification || !result.user?.email) {
        throw new Error('获取用户邮箱失败')
      }
      // 登录接口已经发送了验证码，这里不需要再次发送
      startCountdown()
      return
    }
    
    // 如果已经有用户信息，直接发送验证码
    await adminStore.sendVerificationCode(adminStore.user.email)
    message.success('验证码已发送，请查收邮件')
    startCountdown()
  } catch (error: any) {
    message.error(error.message || '发送验证码失败')
  } finally {
    sendingCode.value = false
  }
}

const handleLogin = async (values: any) => {
  loading.value = true
  try {
    // 如果已经显示验证码输入框，且有验证码，则带上验证码
    const loginParams = {
      username: values.username,
      password: values.password,
      ...(showVerification.value && values.verificationCode ? { verificationCode: values.verificationCode } : {})
    }
    
    const result = await adminStore.login(loginParams)
    
    if (result.needVerification) {
      showVerification.value = true
      // 登录接口已经发送了验证码，只需要启动倒计时
      if (!countdown.value) {
        startCountdown()
      }
      message.info('请查收邮箱验证码')
    } else if (result.success) {
      message.success('登录成功')
      await nextTick()
      router.push('/')
    } else {
      message.error(result.message || '登录失败')
    }
  } catch (error: any) {
    message.error(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-card">
        <div class="login-header">
          <img src="@/assets/logo.png" alt="Logo" class="login-logo">
          <h2 class="login-title">管理后台</h2>
          <p class="login-subtitle">请登录您的管理员账号</p>
        </div>

        <a-form
          :model="loginForm"
          :rules="rules"
          @finish="handleLogin"
          class="login-form"
        >
          <a-form-item name="username">
            <a-input
              v-model:value="loginForm.username"
              placeholder="用户名"
              size="large"
            >
              <template #prefix>
                <UserOutlined />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item name="password">
            <a-input-password
              v-model:value="loginForm.password"
              placeholder="密码"
              size="large"
            >
              <template #prefix>
                <LockOutlined />
              </template>
            </a-input-password>
          </a-form-item>

          <a-form-item v-if="showVerification" name="verificationCode">
            <a-space>
              <a-input
                v-model:value="loginForm.verificationCode"
                placeholder="验证码"
                size="large"
                style="width: 200px"
              >
                <template #prefix>
                  <KeyOutlined />
                </template>
              </a-input>
              <a-button
                :disabled="countdown > 0"
                :loading="sendingCode"
                @click="handleSendCode"
              >
                {{ countdown > 0 ? `${countdown}s后重试` : '获取验证码' }}
              </a-button>
            </a-space>
          </a-form-item>

          <a-form-item>
            <a-button
              type="primary"
              html-type="submit"
              :loading="loading"
              block
              size="large"
            >
              登录
            </a-button>
          </a-form-item>
        </a-form>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f2f5;
  padding: 20px;
}

.login-container {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
}

.login-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 32px;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-logo {
  width: 64px;
  height: 64px;
  margin-bottom: 24px;
}

.login-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.login-subtitle {
  font-size: 14px;
  color: #666;
}

.login-form {
  :deep(.ant-form-item) {
    margin-bottom: 24px;
  }
}
</style> 