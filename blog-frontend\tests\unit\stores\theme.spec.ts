/// <reference types="vitest" />
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

// 直接创建一个模拟的主题管理对象，不依赖于Pinia
const createMockThemeManager = () => {
  // 模拟DOM元素
  const documentElementMock = {
    classList: {
      add: vi.fn(),
      remove: vi.fn()
    }
  };
  
  // 模拟localStorage
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    clear: vi.fn()
  };
  
  // 模拟主题管理器
  const themeManager = {
    // 状态
    theme: 'light',
    fontSize: 'medium',
    
    // 初始化
    init() {
      // 从localStorage获取主题设置
      const storedTheme = localStorageMock.getItem('theme');
      if (storedTheme) {
        this.theme = storedTheme;
      }
      
      // 从localStorage获取字体大小
      const storedFontSize = localStorageMock.getItem('fontSize');
      if (storedFontSize) {
        this.fontSize = storedFontSize;
      }
      
      // 更新DOM
      this.updateTheme();
      this.updateFontSize();
      
      return this;
    },
    
    // 设置主题
    setTheme(newTheme) {
      this.theme = newTheme;
      this.updateTheme();
      return this;
    },
    
    // 设置字体大小
    setFontSize(newSize) {
      this.fontSize = newSize;
      this.updateFontSize();
      return this;
    },
    
    // 更新DOM主题类
    updateTheme() {
      documentElementMock.classList.remove('light', 'dark');
      documentElementMock.classList.add(this.theme);
      localStorageMock.setItem('theme', this.theme);
    },
    
    // 更新DOM字体大小类
    updateFontSize() {
      documentElementMock.classList.remove('text-small', 'text-medium', 'text-large');
      documentElementMock.classList.add(`text-${this.fontSize}`);
      localStorageMock.setItem('fontSize', this.fontSize);
    }
  };
  
  return {
    themeManager,
    documentElementMock,
    localStorageMock
  };
};

describe('主题功能测试', () => {
  let themeManager, documentElementMock, localStorageMock;
  
  beforeEach(() => {
    const mocks = createMockThemeManager();
    themeManager = mocks.themeManager;
    documentElementMock = mocks.documentElementMock;
    localStorageMock = mocks.localStorageMock;
    
    vi.clearAllMocks();
  });
  
  describe('初始化', () => {
    it('如果localStorage有主题则使用localStorage的值', () => {
      // 安排: 设置localStorage返回值
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'theme') return 'dark';
        return null;
      });
      
      // 执行: 初始化主题管理器
      themeManager.init();
      
      // 断言: 验证主题值和DOM更新
      expect(themeManager.theme).toBe('dark');
      expect(documentElementMock.classList.add).toHaveBeenCalledWith('dark');
      expect(localStorageMock.setItem).toHaveBeenCalledWith('theme', 'dark');
    });
    
    it('如果localStorage没有主题则使用默认值', () => {
      // 安排: 确保localStorage返回null
      localStorageMock.getItem.mockReturnValue(null);
      
      // 执行: 初始化主题管理器
      themeManager.init();
      
      // 断言: 验证使用了默认值
      expect(themeManager.theme).toBe('light');
    });
    
    it('如果localStorage有字体大小则使用localStorage的值', () => {
      // 安排: 设置localStorage返回值
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'fontSize') return 'large';
        return null;
      });
      
      // 执行: 初始化主题管理器
      themeManager.init();
      
      // 断言: 验证字体大小值和DOM更新
      expect(themeManager.fontSize).toBe('large');
      expect(documentElementMock.classList.add).toHaveBeenCalledWith('text-large');
      expect(localStorageMock.setItem).toHaveBeenCalledWith('fontSize', 'large');
    });
  });
  
  describe('状态变更', () => {
    it('更改主题时应更新DOM和localStorage', () => {
      // 安排: 初始化主题管理器
      themeManager.init();
      
      // 清除初始化时的调用记录
      vi.clearAllMocks();
      
      // 执行: 更改主题
      themeManager.setTheme('dark');
      
      // 断言: 验证DOM和localStorage更新
      expect(documentElementMock.classList.remove).toHaveBeenCalled();
      expect(documentElementMock.classList.add).toHaveBeenCalledWith('dark');
      expect(localStorageMock.setItem).toHaveBeenCalledWith('theme', 'dark');
    });
    
    it('更改字体大小时应更新DOM和localStorage', () => {
      // 安排: 初始化主题管理器
      themeManager.init();
      
      // 清除初始化时的调用记录
      vi.clearAllMocks();
      
      // 执行: 更改字体大小
      themeManager.setFontSize('small');
      
      // 断言: 验证DOM和localStorage更新
      expect(documentElementMock.classList.remove).toHaveBeenCalled();
      expect(documentElementMock.classList.add).toHaveBeenCalledWith('text-small');
      expect(localStorageMock.setItem).toHaveBeenCalledWith('fontSize', 'small');
    });
  });
}); 