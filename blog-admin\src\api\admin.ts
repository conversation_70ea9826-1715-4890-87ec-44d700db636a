import { http } from '@/utils/request'
import type { R } from '@/types/common'
import type { AdminInfo, LoginParams, LoginResult, AdminSettings } from '@/types/admin'

export interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

// RefreshTokenResponse 接口
export interface RefreshTokenResponse {
  accessToken: string
  tokenType: string
  expiresIn: number
}

/**
 * 管理员登录
 */
export function login(data: LoginParams) {
  return http.post<R<LoginResult>>('/auth/admin/login', data)
}

/**
 * 刷新令牌
 */
export function refreshToken(refreshToken: string) {
  return http.post<R<RefreshTokenResponse>>('/auth/admin/refresh-token', { refreshToken })
}

/**
 * 获取管理员信息
 */
export function getAdminInfo() {
  return http.get<R<AdminInfo>>('/admin/info')
}

/**
 * 发送验证码
 */
export function sendVerificationCode(email: string) {
  return http.post<R<void>>('/auth/admin/send-verification-code', { email })
}

/**
 * 退出登录
 */
export function logout() {
  return http.post<R<void>>('/auth/admin/logout')
}

/**
 * 更新管理员头像
 */
export const updateAdminAvatar = (file: File) => {
  const formData = new FormData()
  formData.append('avatar', file)
  return http.post<R<string>>('/admin/avatar', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 更新管理员密码
 */
export const updateAdminPassword = (data: { oldPassword: string; newPassword: string }) => {
  return http.put<R<void>>('/admin/password', data)
} 