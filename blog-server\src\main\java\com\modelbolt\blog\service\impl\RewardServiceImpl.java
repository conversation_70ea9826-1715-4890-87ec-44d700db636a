package com.modelbolt.blog.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.reward.RewardConfigDTO;
import com.modelbolt.blog.model.dto.reward.RewardRecordDTO;
import com.modelbolt.blog.model.entity.ArticleReward;
import com.modelbolt.blog.model.entity.RewardConfig;
import com.modelbolt.blog.mapper.ArticleRewardMapper;
import com.modelbolt.blog.mapper.RewardConfigMapper;
import com.modelbolt.blog.service.RewardService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.List;

@Service
@RequiredArgsConstructor
public class RewardServiceImpl implements RewardService {

    private final ArticleRewardMapper articleRewardMapper;
    private final RewardConfigMapper rewardConfigMapper;
    private static final Logger log = LoggerFactory.getLogger(RewardServiceImpl.class);

    @Override
    public RewardConfigDTO getRewardConfig(Long userId) {
        log.debug("开始获取打赏配置, userId: {}", userId);
        RewardConfig config = rewardConfigMapper.selectByUserId(userId);
        log.debug("从数据库查询到的打赏配置: {}", config);
        
        if (config == null) {
            log.debug("未找到打赏配置");
            return null;
        }
        
        RewardConfigDTO dto = new RewardConfigDTO();
        BeanUtils.copyProperties(config, dto);
        dto.setAmounts(config.getAmounts());
        log.debug("转换后的DTO对象: {}", dto);
        return dto;
    }

    @Override
    @Transactional
    public void updateRewardConfig(RewardConfigDTO dto) {
        RewardConfig config = new RewardConfig();
        BeanUtils.copyProperties(dto, config);
        rewardConfigMapper.updateById(config);
    }

    @Override
    @Transactional
    public ArticleReward createReward(Long articleId, Long userId, BigDecimal amount, String paymentType, String message) {
        ArticleReward reward = new ArticleReward();
        reward.setArticleId(articleId);
        reward.setUserId(userId);
        reward.setAmount(amount);
        reward.setPaymentType(paymentType);
        reward.setPaymentStatus("PENDING");
        reward.setMessage(message);
        articleRewardMapper.insert(reward);
        return reward;
    }

    @Override
    public PageResult<RewardRecordDTO> getRewardRecords(Long articleId, Integer page, Integer size) {
        IPage<ArticleReward> pageParam = new Page<>(page, size);
        List<RewardRecordDTO> records = articleRewardMapper.selectRewardRecords(articleId, pageParam);
        return new PageResult<>(records, pageParam.getTotal(), page, size);
    }

    @Override
    public BigDecimal getTotalRewardAmount(Long articleId) {
        return articleRewardMapper.selectTotalRewardAmount(articleId);
    }

    @Override
    @Transactional
    public void updatePaymentStatus(Long rewardId, String status, String transactionId) {
        ArticleReward reward = new ArticleReward();
        reward.setId(rewardId);
        reward.setPaymentStatus(status);
        reward.setTransactionId(transactionId);
        articleRewardMapper.updateById(reward);
    }
}