# ModelBolt 博客系统技术文档

![ModelBolt Logo](https://i.imgur.com/XYZ123.png)

## 目录

- [系统概述](#系统概述)
- [技术架构](#技术架构)
- [模块详解](#模块详解)
  - [博客前台](#博客前台)
  - [管理后台](#管理后台)
  - [后端服务](#后端服务)
- [数据库设计](#数据库设计)
- [API接口规范](#api接口规范)
- [部署说明](#部署说明)
- [扩展与优化](#扩展与优化)

## 系统概述

ModelBolt是一个集成DeepSeek AI能力的现代化博客系统，为内容创作者提供智能化的写作辅助、内容管理和用户交互功能。系统包含三个主要模块：博客前台（面向读者）、管理后台（面向博主）和后端服务，通过RESTful API实现前后端分离架构。

### 系统特点

- **AI赋能内容创作**: 集成DeepSeek模型，提供智能写作、内容优化和SEO建议
- **响应式设计**: 适配各种设备，提供流畅的用户体验
- **丰富的交互功能**: 评论、点赞、收藏、分享等社交功能
- **完善的用户系统**: 用户注册、登录、个人中心、关注等功能
- **多样化内容展示**: 文章分类、标签、归档、搜索等内容组织方式
- **可视化数据分析**: 访问统计、用户行为分析等数据展示

## 技术架构

### 总体架构

ModelBolt博客系统采用前后端分离架构，包含三个主要模块：

1. **博客前台**: 面向读者的Web应用，负责内容展示和基础交互
2. **管理后台**: 面向博主的内容管理系统，提供内容创作和系统管理功能
3. **后端服务**: 提供API接口，处理业务逻辑和数据存储

### 技术栈

#### 前端技术栈

- **核心框架**: Vue 3.0+ (Composition API)
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **UI组件库**: 
  - 博客前台: Element Plus
  - 管理后台: Ant Design Vue
- **样式方案**: TailwindCSS + SCSS
- **构建工具**: Vite 4.0+
- **包管理器**: pnpm

#### 后端技术栈

- **核心框架**: Spring Boot 3.0+
- **ORM框架**: MyBatis Plus
- **数据库**: MySQL 8.0+
- **缓存**: Redis
- **认证授权**: JWT + Spring Security
- **API文档**: Swagger/OpenAPI 3
- **AI集成**: DeepSeek API

## 模块详解

### 博客前台

博客前台是面向普通用户/读者的Web应用，提供文章浏览、用户互动等功能。

#### 目录结构

```
blog-frontend/
├── src/
│   ├── api/          # API接口定义
│   ├── assets/       # 静态资源
│   ├── components/   # 公共组件
│   ├── composables/  # 组合式函数
│   ├── layouts/      # 布局组件
│   ├── router/       # 路由配置
│   ├── stores/       # 状态管理
│   ├── styles/       # 样式文件
│   ├── utils/        # 工具函数
│   └── views/        # 页面组件
│       ├── article/    # 文章相关页面
│       ├── auth/       # 认证相关页面
│       ├── category/   # 分类相关页面
│       ├── error/      # 错误页面
│       ├── home/       # 首页相关
│       ├── search/     # 搜索页面
│       ├── tag/        # 标签相关页面
│       └── user/       # 用户中心页面
```

#### 核心功能

1. **用户认证**
   - 登录/注册/找回密码
   - 第三方账号登录
   - 用户信息展示与编辑

2. **内容浏览**
   - 首页文章列表
   - 文章详情页
   - 分类/标签文章列表
   - 热门文章推荐
   - 文章搜索功能

3. **用户互动**
   - 文章评论
   - 点赞/收藏功能
   - 分享功能
   - 用户关注

4. **其他功能**
   - 文章归档
   - 个人中心
   - 通知消息
   - 暗黑模式切换

#### 关键页面

| 页面 | 路径 | 描述 |
|------|------|------|
| 首页 | `/` | 展示置顶文章、最新文章、热门文章 |
| 文章详情 | `/article/:id` | 展示文章内容、评论、相关推荐 |
| 分类列表 | `/categories` | 展示所有分类及其文章数量 |
| 标签列表 | `/tags` | 展示所有标签及其文章数量 |
| 归档 | `/archives` | 按时间展示所有文章 |
| 搜索结果 | `/search` | 展示搜索结果 |
| 个人中心 | `/user/:id` | 展示用户信息、文章、收藏等 |

### 管理后台

管理后台是面向博主的内容管理系统，提供内容创作、数据统计、系统设置等功能。

#### 目录结构

```
blog-admin/
├── src/
│   ├── api/          # API接口定义
│   ├── assets/       # 静态资源
│   ├── components/   # 公共组件
│   ├── layouts/      # 布局组件
│   ├── router/       # 路由配置
│   ├── stores/       # 状态管理
│   ├── styles/       # 样式文件
│   ├── utils/        # 工具函数
│   └── views/        # 页面组件
│       ├── article/    # 文章管理页面
│       ├── auth/       # 认证相关页面
│       ├── category/   # 分类管理页面
│       ├── comment/    # 评论管理页面
│       ├── dashboard/  # 控制台/首页
│       ├── profile/    # 个人资料页面
│       ├── setting/    # 系统设置页面
│       ├── tag/        # 标签管理页面
│       └── user/       # 用户管理页面
```

#### 核心功能

1. **内容管理**
   - 文章创建/编辑/删除
   - 分类管理
   - 标签管理
   - 评论管理

2. **用户管理**
   - 用户列表查看
   - 用户状态管理
   - 用户角色分配

3. **统计分析**
   - 访问统计
   - 用户增长趋势
   - 内容数据分析
   - 热门文章分析

4. **系统设置**
   - 网站基础设置
   - 个人资料设置
   - AI功能配置
   - 安全设置

5. **智能创作辅助**
   - AI写作助手
   - 内容创意生成
   - SEO优化建议
   - 文章质量分析

#### 关键页面

| 页面 | 路径 | 描述 |
|------|------|------|
| 控制台 | `/dashboard` | 展示系统概览、数据统计 |
| 文章管理 | `/article/list` | 管理所有文章 |
| 写文章 | `/article/edit` | 创建/编辑文章 |
| 分类管理 | `/category` | 管理文章分类 |
| 标签管理 | `/tag` | 管理文章标签 |
| 评论管理 | `/comment` | 管理文章评论 |
| 用户管理 | `/user` | 管理用户信息 |
| 系统设置 | `/setting` | 配置系统参数 |

### 后端服务

后端服务提供RESTful API接口，处理前端请求，实现业务逻辑和数据持久化。

#### 目录结构

```
blog-server/
├── src/
│   ├── main/
│   │   ├── java/com/modelbolt/blog/
│   │   │   ├── annotation/   # 自定义注解
│   │   │   ├── aspect/       # AOP切面
│   │   │   ├── common/       # 通用类
│   │   │   ├── config/       # 配置类
│   │   │   ├── constant/     # 常量定义
│   │   │   ├── controller/   # 控制器
│   │   │   ├── enums/        # 枚举类型
│   │   │   ├── event/        # 事件相关
│   │   │   ├── exception/    # 异常处理
│   │   │   ├── filter/       # 过滤器
│   │   │   ├── interceptor/  # 拦截器
│   │   │   ├── mapper/       # MyBatis映射器
│   │   │   ├── model/        # 数据模型
│   │   │   │   ├── dto/      # 数据传输对象
│   │   │   │   └── entity/   # 实体类
│   │   │   ├── service/      # 服务接口及实现
│   │   │   ├── task/         # 定时任务
│   │   │   ├── typehandler/  # 类型处理器
│   │   │   ├── utils/        # 工具类
│   │   │   └── websocket/    # WebSocket相关
│   │   └── resources/        # 配置资源
│   └── test/                 # 测试代码
```

#### 核心功能

1. **用户管理**
   - 用户注册、登录、认证授权
   - 用户信息管理
   - 用户权限控制

2. **内容管理**
   - 文章CRUD操作
   - 分类/标签管理
   - 评论管理

3. **交互功能**
   - 点赞、收藏功能
   - 用户关注关系
   - 通知消息推送

4. **AI服务集成**
   - DeepSeek API调用
   - AI写作辅助
   - 内容智能分析

5. **系统服务**
   - 系统配置管理
   - 文件上传服务
   - 缓存服务
   - 定时任务

#### 核心接口

##### 用户接口

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 登录 | POST | `/auth/login` | 用户登录 |
| 注册 | POST | `/auth/register` | 用户注册 |
| 发送验证码 | POST | `/auth/sendCode` | 发送邮箱验证码 |
| 获取用户信息 | GET | `/user/info` | 获取当前用户信息 |
| 更新用户资料 | PUT | `/user/profile` | 更新用户资料 |

##### 文章接口

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 获取文章列表 | GET | `/articles` | 分页获取文章列表 |
| 获取文章详情 | GET | `/articles/{id}` | 获取文章详情 |
| 创建文章 | POST | `/user/articles` | 创建新文章 |
| 更新文章 | PUT | `/user/articles/{id}` | 更新文章内容 |
| 删除文章 | DELETE | `/user/articles/{id}` | 删除文章 |

##### 分类接口

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 获取分类列表 | GET | `/categories` | 获取所有分类 |
| 获取分类详情 | GET | `/categories/{id}` | 获取分类详情 |
| 获取分类下文章 | GET | `/categories/{id}/articles` | 获取分类下的文章 |

##### AI服务接口

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| AI写作生成 | POST | `/ai/writing/generate` | 处理AI写作请求 |
| AI对话处理 | POST | `/ai/chat` | 处理AI聊天请求 |

## 数据库设计

ModelBolt博客系统使用MySQL数据库，主要包含以下数据表：

### 用户管理相关表

- **user**: 用户表
- **user_settings**: 用户配置表
- **user_follow**: 用户关注表
- **login_log**: 登录日志表

### 内容管理相关表

- **article**: 文章表
- **category**: 分类表
- **tag**: 标签表
- **article_category**: 文章分类关联表
- **article_tag**: 文章标签关联表
- **visit_history**: 访问历史记录表

### 互动管理相关表

- **comment**: 评论表
- **comment_like**: 评论点赞表
- **user_like**: 用户点赞表
- **user_favorite**: 用户收藏表
- **share_record**: 分享记录表
- **notification**: 通知表

### 打赏相关表

- **reward_config**: 打赏配置表
- **article_reward**: 打赏记录表

### 系统管理相关表

- **system_config**: 系统配置表
- **operation_log**: 操作日志表

### AI服务相关表

- **ai_chat_history**: AI对话历史表
- **ai_usage_stats**: AI使用统计表

## API接口规范

ModelBolt博客系统采用RESTful API设计风格，接口规范如下：

### 请求格式

- **基础路径**: `/api`
- **内容类型**: `application/json`
- **认证方式**: Bearer Token

### 响应格式

```json
{
  "code": 200,          // 状态码
  "message": "success", // 状态消息
  "data": {},           // 响应数据
  "timestamp": 1628097433123 // 时间戳
}
```

### 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 部署说明

ModelBolt博客系统支持多种部署方式，包括传统部署和容器化部署。

### 环境要求

- **JDK**: 17+
- **Node.js**: 16+
- **MySQL**: 8.0+
- **Redis**: 6.0+

### 前端部署

1. 安装依赖
   ```bash
   cd blog-frontend
   pnpm install
   ```

2. 构建生产环境代码
   ```bash
   pnpm build
   ```

3. 部署静态资源到Web服务器(Nginx等)

### 后端部署

1. 配置数据库
   - 创建数据库
   - 导入初始SQL脚本

2. 配置应用参数
   - 修改`application.yml`，设置数据库连接、Redis连接等

3. 构建项目
   ```bash
   cd blog-server
   mvn clean package -DskipTests
   ```

4. 运行服务
   ```bash
   java -jar target/blog-server-1.0.0.jar
   ```

### 容器化部署

可使用Docker Compose实现一键部署：
```bash
docker-compose up -d
```

## 扩展与优化

### 功能扩展

1. **多语言支持**
   - 添加国际化配置
   - 支持多语言切换

2. **媒体库管理**
   - 图片/视频管理功能
   - 支持多种存储方式(本地、OSS等)

3. **社交功能增强**
   - 私信系统
   - 用户群组

### 性能优化

1. **缓存优化**
   - 多级缓存策略
   - 热点数据缓存

2. **数据库优化**
   - 读写分离
   - 分库分表

3. **前端优化**
   - 懒加载/代码分割
   - 图片压缩与CDN加速

### AI能力增强

1. **智能推荐系统**
   - 基于用户行为的内容推荐
   - 相似文章推荐

2. **内容分析能力**
   - 文章质量评估
   - 情感分析

3. **多模态AI集成**
   - 图像识别
   - 语音转文字 