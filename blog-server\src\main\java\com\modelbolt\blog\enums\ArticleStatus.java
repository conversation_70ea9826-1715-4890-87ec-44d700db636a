package com.modelbolt.blog.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "文章状态")
public enum ArticleStatus {
    
    @Schema(description = "草稿")
    DRAFT(0, "草稿"),
    
    @Schema(description = "已发布")
    PUBLISHED(1, "已发布"),
    
    @Schema(description = "已下架")
    UNPUBLISHED(2, "已下架");

    private final Integer code;
    private final String description;

    ArticleStatus(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
} 