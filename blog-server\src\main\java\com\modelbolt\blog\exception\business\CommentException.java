package com.modelbolt.blog.exception.business;

import com.modelbolt.blog.exception.BusinessException;

/**
 * 评论相关异常类
 */
public class CommentException extends BusinessException {
    
    private CommentException(int code, String message) {
        super(code, message);
    }
    
    public static class CommentNotFound extends CommentException {
        public CommentNotFound() {
            super(404, "评论不存在");
        }
    }
    
    public static class InvalidStatus extends CommentException {
        public InvalidStatus() {
            super(400, "无效的评论状态");
        }
    }
    
    public static class NotAllowedOperation extends CommentException {
        public NotAllowedOperation() {
            super(403, "不允许的操作");
        }
    }

    // 新增审核相关异常
    public static class AlreadyAudited extends CommentException {
        public AlreadyAudited() {
            super(400, "评论已审核，不能重复审核");
        }
    }

    public static class AuditFailed extends CommentException {
        public AuditFailed() {
            super(500, "评论审核失败");
        }
    }

    public static class DeleteFailed extends CommentException {
        public DeleteFailed() {
            super(500, "删除评论失败");
        }
    }
} 