import { ref } from 'vue'
import { message } from 'ant-design-vue'

// 直接使用相对路径，依靠代理配置转发请求
const defaultAvatar = '/images/avatar/default-avatar.png'
const defaultCover = '/images/article/cover/default-cover.jpg'

// 缓存已经加载失败的URL，避免重复请求
const failedUrls = new Set<string>()
// 缓存默认头像是否加载失败
let isDefaultAvatarFailed = false
// 缓存默认封面是否加载失败
let isDefaultCoverFailed = false
// 最大重试次数 - 严格限制为最多2次重试
const MAX_RETRIES = 2

// 为Window接口添加全局图片重试计数器属性
declare global {
  interface Window {
    __IMAGE_RETRY_COUNTER__?: Map<string, number>;
  }
}

/**
 * 处理头像URL
 * @param url 头像URL
 * @returns 完整的头像URL
 */
export function getAvatarUrl(url: string | undefined | null): string {
  // 如果默认头像已经失败过，直接返回空字符串让组件显示首字母头像
  if (isDefaultAvatarFailed) {
    return ''
  }

  // 如果URL为空，返回默认头像
  if (!url) {
    return defaultAvatar
  }
  
  // 如果已经是完整URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }
  
  // 如果这个URL已经加载失败过，返回默认头像
  if (failedUrls.has(url)) {
    return defaultAvatar
  }
  
  // 处理相对路径
  if (url.startsWith('/api/') || url.startsWith('/images/')) {
    // 去除可能存在的前缀
    let cleanPath = url
    if (url.startsWith('/api/')) {
      cleanPath = url.substring(4)
    } else if (url.startsWith('/static/')) {
      cleanPath = url.substring(7)
    }
    
    // 确保路径以/开头
    if (!cleanPath.startsWith('/')) {
      cleanPath = '/' + cleanPath
    }
    
    return cleanPath
  }
  
  // 如果没有前缀，确保有/images前缀
  if (!url.startsWith('/api/') && !url.startsWith('/images/')) {
    return `/images/${url.replace(/^\/+/, '')}`
  }
  
  return url
}

/**
 * 处理封面URL
 * @param url 封面URL
 * @returns 完整的封面URL
 */
export function getCoverUrl(url: string | undefined | null): string {
  // 如果URL为空，返回默认封面
  if (!url) {
    return defaultCover
  }

  // 如果已经是完整URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }

  // 如果这个URL已经加载失败过，直接返回默认封面
  if (failedUrls.has(url)) {
    return defaultCover
  }

  // 处理相对路径
  if (url.startsWith('/api/') || url.startsWith('/images/')) {
    // 去除可能存在的前缀
    let cleanPath = url
    if (url.startsWith('/api/')) {
      cleanPath = url.substring(4)
    } else if (url.startsWith('/static/')) {
      cleanPath = url.substring(7)
    }
    
    // 确保路径以/开头
    if (!cleanPath.startsWith('/')) {
      cleanPath = '/' + cleanPath
    }
    
    return cleanPath
  }
  
  // 如果没有前缀，确保有/images前缀
  if (!url.startsWith('/api/') && !url.startsWith('/images/')) {
    return `/images/${url.replace(/^\/+/, '')}`
  }
  
  return url
}

/**
 * 处理图片加载错误
 * @param defaultImage 默认图片路径
 * @returns 错误处理函数
 */
export function useImageFallback(defaultImage: string = defaultAvatar) {
  // 使用全局共享的图片失败计数映射
  const globalRetryCounter = window.__IMAGE_RETRY_COUNTER__ = window.__IMAGE_RETRY_COUNTER__ || new Map();
  
  const imgError = (e: Event) => {
    const img = e.target as HTMLImageElement
    const originalSrc = img.getAttribute('data-original-src') || img.src
    
    // 记录原始URL到img元素
    if (!img.getAttribute('data-original-src')) {
      img.setAttribute('data-original-src', originalSrc)
    }
    
    // 提取基本URL（不含查询参数）
    const baseUrl = originalSrc.split('?')[0]
    
    // 检查是否已经记录到失败集合中
    if (failedUrls.has(baseUrl)) {
      console.log(`[图片] 使用替代图: ${baseUrl} -> ${defaultImage || '首字母头像'}`)
      if (defaultImage === defaultAvatar && isDefaultAvatarFailed) {
        img.src = '' // 使用首字母头像
      } else if (defaultImage && defaultImage !== '') {
        img.src = defaultImage // 使用默认图片
      } else {
        img.src = '' // 没有默认图片则留空
      }
      return
    }
    
    // 获取当前重试次数
    let currentRetries = globalRetryCounter.get(baseUrl) || 0
    
    // 严格检查重试次数
    if (currentRetries >= MAX_RETRIES) {
      console.warn(`[图片] 达到最大重试次数(${MAX_RETRIES}): ${baseUrl}`)
      failedUrls.add(baseUrl)
      
      // 根据图片类型选择合适的替代方式
      if (baseUrl.includes('avatar') || defaultImage === defaultAvatar) {
        if (isDefaultAvatarFailed) {
          img.src = '' // 使用首字母头像
        } else {
          img.src = defaultAvatar // 使用默认头像
        }
      } else if (baseUrl.includes('cover') || defaultImage === defaultCover) {
        if (isDefaultCoverFailed) {
          img.src = '' // 空图片
        } else {
          img.src = defaultCover // 使用默认封面
        }
      } else if (defaultImage) {
        img.src = defaultImage // 使用指定的默认图片
      } else {
        img.src = '' // 没有默认图片则留空
      }
      
      // 清理重试计数
      globalRetryCounter.delete(baseUrl)
      return
    }
    
    // 特殊处理默认头像和封面
    if (baseUrl.includes('default-avatar')) {
      isDefaultAvatarFailed = true
      img.src = '' // 直接使用首字母头像
      console.warn('[图片] 默认头像加载失败:', baseUrl)
      message.error('默认头像加载失败，将使用首字母头像')
      return
    }
    
    if (baseUrl.includes('default-cover')) {
      isDefaultCoverFailed = true
      img.src = '' // 显示空图片
      console.warn('[图片] 默认封面加载失败:', baseUrl)
      message.error('默认封面加载失败')
      return
    }
    
    // 增加重试次数
    currentRetries += 1
    globalRetryCounter.set(baseUrl, currentRetries)
    
    // 在URL中嵌入重试信息，确保浏览器不会使用缓存
    const timestamp = Date.now()
    const newSrc = baseUrl.includes('?') 
      ? `${baseUrl}&t=${timestamp}&retry=${currentRetries}` 
      : `${baseUrl}?t=${timestamp}&retry=${currentRetries}`
    
    console.log(`[图片] 第${currentRetries}次重试/${MAX_RETRIES}: ${baseUrl}`)
    
    // 设置新的图片源
    img.src = newSrc
  }
  
  return {
    imgError
  }
}

/**
 * 清除图片加载失败缓存
 * @param url 指定URL，如果不指定则清除所有
 */
export function clearImageFailedCache(url?: string) {
  if (url) {
    failedUrls.delete(url)
    // 重置重试计数
    const retryCount = ref(new Map<string, number>())
    retryCount.value.delete(url)
  } else {
    failedUrls.clear()
    // 重置所有重试计数
    const retryCount = ref(new Map<string, number>())
    retryCount.value.clear()
  }
  // 重置默认头像失败状态
  isDefaultAvatarFailed = false
  isDefaultCoverFailed = false
}

/**
 * 获取带时间戳的图片URL，用于强制刷新
 * @param url 图片URL
 * @returns 带时间戳的URL
 */
export function getRefreshedImageUrl(url: string): string {
  // 如果是默认头像且已经失败过，直接返回原URL
  if (url === defaultAvatar && isDefaultAvatarFailed) {
    return url
  }
  
  // 如果URL已经在失败集合中，直接返回默认图片
  if (failedUrls.has(url)) {
    return defaultAvatar
  }
  
  const timestamp = Date.now()
  return url.includes('?') 
    ? `${url}&t=${timestamp}` 
    : `${url}?t=${timestamp}`
}

/**
 * 获取用户头像的首字母
 * @param username 用户名
 * @returns 首字母（大写）
 */
export function getAvatarText(username: string | undefined | null): string {
  if (!username) return 'U'
  return username.charAt(0).toUpperCase()
} 