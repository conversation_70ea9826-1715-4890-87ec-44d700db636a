import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useUserStore } from './user'

// 开发环境下的调试日志
const DEBUG = import.meta.env.DEV
function log(...args: any[]) {
  if (DEBUG) {
    console.log('[OnlineStore]', ...args)
  }
}

function error(...args: any[]) {
  if (DEBUG) {
    console.error('[OnlineStore]', ...args)
  }
}

export const useOnlineStore = defineStore('online', () => {
  const userStore = useUserStore()
  
  // 状态定义
  const webSocket = ref<WebSocket | null>(null)
  const isConnected = ref(false)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 5
  const onlineStats = ref({
    onlineUsers: 0,
    todayVisits: 0,
    todayComments: 0,
    systemLoad: '0%'
  })

  // 初始化WebSocket连接
  function initWebSocket() {
    if (!userStore.token) {
      log('初始化WebSocket失败: 未找到用户token')
      return
    }

    if (webSocket.value?.readyState === WebSocket.OPEN) {
      log('WebSocket已连接，无需重新初始化')
      return
    }

    // 构建WebSocket URL，使用相对路径以便使用Vite的代理
    const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/api/ws/online?token=${userStore.token}`
    log('正在连接WebSocket:', wsUrl)
    
    try {
      webSocket.value = new WebSocket(wsUrl)
      log('WebSocket实例创建成功')

      webSocket.value.onopen = () => {
        log('WebSocket连接成功')
        isConnected.value = true
        reconnectAttempts.value = 0
      }

      webSocket.value.onmessage = (event) => {
        log('收到原始WebSocket消息:', event.data)
        try {
          const data = JSON.parse(event.data)
          log('解析后的WebSocket消息:', data)
          onlineStats.value = data
        } catch (err) {
          error('解析WebSocket消息失败:', err)
        }
      }

      webSocket.value.onclose = (event: CloseEvent) => {
        log('WebSocket连接断开', {
          code: event.code,
          reason: event.reason,
          wasClean: event.wasClean
        })
        isConnected.value = false
        handleReconnect()
      }

      webSocket.value.onerror = (event: Event) => {
        error('WebSocket连接错误:', {
          event,
          readyState: webSocket.value?.readyState
        })
        isConnected.value = false
      }
    } catch (err) {
      error('创建WebSocket实例失败:', err)
      isConnected.value = false
    }
  }

  // 处理WebSocket重连
  function handleReconnect() {
    if (reconnectAttempts.value >= maxReconnectAttempts) {
      error('达到最大重连次数:', maxReconnectAttempts)
      return
    }

    const timeout = Math.min(1000 * Math.pow(2, reconnectAttempts.value), 10000)
    reconnectAttempts.value++

    log(`计划重连 (${reconnectAttempts.value}/${maxReconnectAttempts}), 等待时间: ${timeout}ms`)
    setTimeout(() => {
      log(`开始第 ${reconnectAttempts.value} 次重连尝试`)
      initWebSocket()
    }, timeout)
  }

  // 关闭WebSocket连接
  function closeWebSocket() {
    if (webSocket.value) {
      log('正在关闭WebSocket连接')
      try {
        webSocket.value.close()
      } catch (err) {
        error('关闭WebSocket连接时出错:', err)
      } finally {
        webSocket.value = null
        isConnected.value = false
      }
    }
  }

  return {
    isConnected,
    onlineStats,
    initWebSocket,
    closeWebSocket
  }
}) 