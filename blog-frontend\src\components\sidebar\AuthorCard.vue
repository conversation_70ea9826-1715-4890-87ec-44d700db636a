<!-- 作者信息卡片组件 -->
<template>
  <div class="bg-white rounded-lg shadow-sm p-6">
    <div class="text-center">
      <div class="w-20 h-20 mx-auto rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mb-4 shadow-lg">
        <img 
          v-if="author.avatar" 
          :src="author.avatar" 
          :alt="author.name"
          class="w-full h-full rounded-full object-cover"
        >
        <span v-else class="text-2xl font-bold text-white">
          {{ author.name?.charAt(0).toUpperCase() }}
        </span>
      </div>
      <h3 class="text-lg font-bold text-gray-900 mb-2">{{ author.name }}</h3>
      <p class="text-sm text-gray-600 mb-4">
        {{ author.bio }}
      </p>
      <div class="flex items-center space-x-4 mt-4">
        <a v-for="social in author.socials" 
           :key="social.type" 
           :href="social.url" 
           target="_blank" 
           class="text-gray-500 hover:text-primary">
          <!-- 根据不同类型显示不同图标 -->
          <el-icon v-if="social.type === 'github'" class="text-xl">
            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24">
              <path fill="currentColor" d="M12 2A10 10 0 0 0 2 12c0 4.42 2.87 8.17 6.84 9.5c.5.08.66-.23.66-.5v-1.69c-2.77.6-3.36-1.34-3.36-1.34c-.46-1.16-1.11-1.47-1.11-1.47c-.91-.62.07-.6.07-.6c1 .07 1.53 1.03 1.53 1.03c.87 1.52 2.34 1.07 2.91.83c.09-.65.35-1.09.63-1.34c-2.22-.25-4.55-1.11-4.55-4.92c0-1.11.38-2 1.03-2.71c-.1-.25-.45-1.29.1-2.64c0 0 .84-.27 2.75 1.02c.79-.22 1.65-.33 2.5-.33c.85 0 1.71.11 2.5.33c1.91-1.29 2.75-1.02 2.75-1.02c.55 1.35.2 2.39.1 2.64c.65.71 1.03 1.6 1.03 2.71c0 3.82-2.34 4.66-4.57 4.91c.36.31.69.92.69 1.85V21c0 .27.16.59.67.5C19.14 20.16 22 16.42 22 12A10 10 0 0 0 12 2z"/>
            </svg>
          </el-icon>
          <el-icon v-else-if="social.type === 'qq'" class="text-xl">
            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24">
              <path fill="currentColor" d="M12.003 2c-2.265 0-6.29 1.364-6.29 7.325v1.195S3.55 14.96 3.55 17.474c0 .665.17 1.025.281 1.025c.114 0 .902-.484 1.748-2.072c0 0-.18 2.197 1.904 3.967c0 0-1.77.495-1.77 1.182c0 .686 4.078.43 6.29 0c2.239.425 6.287.687 6.287 0c0-.688-1.768-1.182-1.768-1.182c2.085-1.77 1.905-3.967 1.905-3.967c.845 1.588 1.634 2.072 1.746 2.072c.111 0 .283-.36.283-1.025c0-2.514-2.166-6.954-2.166-6.954V9.325C18.29 3.364 14.268 2 12.003 2z"/>
            </svg>
          </el-icon>
        </a>
      </div>
      <div class="flex justify-between mt-6 pt-6 border-t border-gray-100">
        <div class="text-center">
          <div class="text-xl font-bold text-gray-900">{{ author.stats.articles }}</div>
          <div class="text-sm text-gray-500">文章</div>
        </div>
        <div class="text-center">
          <div class="text-xl font-bold text-gray-900">{{ author.stats.comments }}</div>
          <div class="text-sm text-gray-500">评论</div>
        </div>
        <div class="text-center">
          <div class="text-xl font-bold text-gray-900">{{ author.stats.favorites }}</div>
          <div class="text-sm text-gray-500">收藏</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Platform, ChatDotSquare, Connection } from '@element-plus/icons-vue'

interface Social {
  type: string
  url: string
}

interface AuthorStats {
  articles: number
  comments: number
  favorites: number
}

interface Author {
  name: string
  avatar?: string
  bio: string
  socials: Social[]
  stats: AuthorStats
}

defineProps<{
  author: Author
}>()

const getSocialIcon = (type: string) => {
  const icons: Record<string, any> = {
    github: Platform,
    twitter: ChatDotSquare,
    website: Connection
  }
  return icons[type.toLowerCase()] || Platform
}
</script>