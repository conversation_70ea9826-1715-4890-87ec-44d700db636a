import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useAdminStore } from './admin'
import { log as logUtil, error as errorUtil } from '@/utils/log'

// 开发环境下的调试日志
const DEBUG = import.meta.env.DEV
function log(...args: any[]) {
  if (DEBUG) {
    console.log('[WebSocketStore]', ...args)
  }
}

function error(...args: any[]) {
  if (DEBUG) {
    console.error('[WebSocketStore]', ...args)
  }
}

export const useWebSocketStore = defineStore('websocket', () => {
  const webSocket = ref<WebSocket | null>(null)
  const isConnected = ref(false)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 5
  const reconnectDelay = 1000 // 初始重连延迟（毫秒）

  // 实时统计数据
  const realTimeStats = ref({
    onlineUsers: 0,
    todayVisits: 0,
    todayComments: 0,
    systemLoad: '0%'
  })

  // 初始化WebSocket连接
  const initWebSocket = () => {
    const adminStore = useAdminStore()
    if (!adminStore.token) {
      logUtil('未登录，不建立WebSocket连接')
      return
    }

    if (webSocket.value?.readyState === WebSocket.OPEN) {
      logUtil('WebSocket已连接，无需重新初始化')
      return
    }

    try {
      // 使用环境变量中的WebSocket基础URL
      const wsBaseUrl = import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:8080/api'
      const wsUrl = `${wsBaseUrl}/ws/online?token=${adminStore.token}`
      logUtil('正在连接WebSocket:', wsUrl)
      
      webSocket.value = new WebSocket(wsUrl)

      webSocket.value.onopen = () => {
        logUtil('WebSocket连接成功')
        isConnected.value = true
        reconnectAttempts.value = 0
      }

      webSocket.value.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          logUtil('收到WebSocket消息:', data)
          if (data && typeof data === 'object') {
            realTimeStats.value = {
              onlineUsers: data.onlineUsers ?? 0,
              todayVisits: data.todayVisits ?? 0,
              todayComments: data.todayComments ?? 0,
              systemLoad: data.systemLoad ?? '0%'
            }
          }
        } catch (err) {
          errorUtil('解析WebSocket消息失败:', err)
        }
      }

      webSocket.value.onclose = () => {
        logUtil('WebSocket连接关闭')
        isConnected.value = false
        handleReconnect()
      }

      webSocket.value.onerror = (err) => {
        errorUtil('WebSocket错误:', err)
        isConnected.value = false
      }
    } catch (err) {
      errorUtil('初始化WebSocket失败:', err)
      handleReconnect()
    }
  }

  // 处理重连
  const handleReconnect = () => {
    if (reconnectAttempts.value >= maxReconnectAttempts) {
      logUtil('达到最大重连次数，停止重连')
      return
    }

    const delay = reconnectDelay * Math.pow(2, reconnectAttempts.value)
    logUtil(`${delay}ms后尝试重连，当前重试次数: ${reconnectAttempts.value + 1}`)

    setTimeout(() => {
      reconnectAttempts.value++
      initWebSocket()
    }, delay)
  }

  // 关闭WebSocket连接
  const closeWebSocket = () => {
    if (webSocket.value) {
      webSocket.value.close()
      webSocket.value = null
      isConnected.value = false
      reconnectAttempts.value = 0
    }
  }

  return {
    isConnected,
    realTimeStats,
    initWebSocket,
    closeWebSocket
  }
}) 