<template>
  <router-view />
</template>

<script setup lang="ts">
import { onMounted, watch, onUnmounted, nextTick } from 'vue'
import { useUserStore } from '@/stores/user'
import { useOnlineStore } from '@/stores/online'
import { ElMessage } from 'element-plus'
import { startTokenCheck, stopTokenCheck } from '@/utils/request'
import { useRoute } from 'vue-router'

const userStore = useUserStore()
const onlineStore = useOnlineStore()
const route = useRoute()

// 监听用户认证状态
watch(() => userStore.isAuthenticated, (isAuthenticated) => {
  if (isAuthenticated) {
    startTokenCheck()
  } else {
    stopTokenCheck()
  }
})

// 监听路由变化，确保登录状态正确
watch(() => route.fullPath, async () => {
  // 每次路由变化时，如果有token但没有用户信息，则尝试获取用户信息
  if (userStore.token && !userStore.userInfo) {
    await userStore.getUserInfo()
  }
  // 如果刚刚登录成功，确保页面内容能正常显示用户状态
  if (userStore.isAuthenticated && route.path === '/') {
    await nextTick()
  }
}, { immediate: true })

// 监听WebSocket连接状态
watch(() => onlineStore.isConnected, (isConnected) => {
  if (!isConnected && userStore.isAuthenticated) {
    ElMessage.warning('实时数据连接已断开，正在尝试重连...')
  }
})

onMounted(async () => {
  // 如果有token，获取用户信息
  if (userStore.token && !userStore.userInfo) {
    try {
      await userStore.getUserInfo()
      // 用户信息获取成功后，初始化WebSocket连接
      if (userStore.isAuthenticated) {
        onlineStore.initWebSocket()
        startTokenCheck() // 启动token检查
      }
    } catch (error) {
      console.error('Failed to get user info:', error)
      // token无效，清除登录状态
      userStore.logout()
    }
  } else if (userStore.isAuthenticated) {
    // 如果已经有用户信息，直接初始化WebSocket连接
    onlineStore.initWebSocket()
    startTokenCheck() // 启动token检查
  }
})

onUnmounted(() => {
  stopTokenCheck() // 停止token检查
})
</script>

<style>
#app {
  width: 100%;
  height: 100%;
}
</style>
