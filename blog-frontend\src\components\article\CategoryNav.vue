<!-- 分类导航组件 -->
<template>
  <div class="bg-white rounded-lg shadow-sm mb-8">
    <div class="flex items-center space-x-4 overflow-x-auto p-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
      <button
        v-for="category in categories"
        :key="category.id"
        class="px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-all duration-300 flex items-center hover:shadow-md relative"
        :class="[
          modelValue === category.id
            ? 'bg-blue-600 text-white shadow-lg scale-105'
            : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
        ]"
        @click="handleCategoryClick(category.id)"
      >
        {{ category.name }}
        <span 
          v-if="category.count" 
          class="ml-2 px-2 py-0.5 text-xs rounded-full transition-all duration-300"
          :class="modelValue === category.id ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-600'"
        >
          {{ category.count }}
        </span>
        <div 
          v-if="modelValue === category.id"
          class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 bg-blue-600 rounded-full"
        ></div>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Category {
  id: number
  name: string
  count?: number
}

const props = defineProps<{
  categories: Category[]
  modelValue: number | null
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: number | null): void
}>()

const handleCategoryClick = (categoryId: number) => {
  // 如果点击当前选中的分类，不做任何操作
  if (props.modelValue === categoryId) {
    return
  }
  // 发出更新事件
  emit('update:modelValue', categoryId)
}
</script>

<style scoped>
.scrollbar-thin::-webkit-scrollbar {
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: var(--scrollbar-track, #f1f1f1);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb, #888);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #666;
}
</style>