<script setup lang="ts">
import { ref, watch } from 'vue'
import type { UserForm } from '@/types/user'
import { UserRole, UserStatus } from '@/types/user'
import type { FormInstance } from 'ant-design-vue'

const props = defineProps<{
  visible: boolean
  loading?: boolean
  editData?: UserForm | null
}>()

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
  (e: 'submit', data: UserForm): void
}>()

const formRef = ref<FormInstance>()
const formState = ref<UserForm>({
  username: '',
  email: '',
  role: UserRole.USER,
  status: UserStatus.ENABLED
})

const roleOptions = [
  { label: '管理员', value: UserRole.ADMIN },
  { label: '普通用户', value: UserRole.USER }
]

const statusOptions = [
  { label: '启用', value: UserStatus.ENABLED },
  { label: '禁用', value: UserStatus.DISABLED }
]

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  role: [{ required: true, message: '请选择角色', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

watch(
  () => props.editData,
  (val) => {
    if (val) {
      formState.value = { ...val }
    } else {
      formState.value = {
        username: '',
        email: '',
        role: UserRole.USER,
        status: UserStatus.ENABLED
      }
    }
  }
)

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    emit('submit', { ...formState.value })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCancel = () => {
  formRef.value?.resetFields()
  emit('update:visible', false)
}
</script>

<template>
  <a-modal
    :visible="visible"
    :title="editData ? '编辑用户' : '新增用户'"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="用户名" name="username">
        <a-input
          v-model:value="formState.username"
          placeholder="请输入用户名"
        />
      </a-form-item>

      <a-form-item label="邮箱" name="email">
        <a-input
          v-model:value="formState.email"
          placeholder="请输入邮箱"
        />
      </a-form-item>

      <a-form-item label="角色" name="role">
        <a-select
          v-model:value="formState.role"
          placeholder="请选择角色"
        >
          <a-select-option
            v-for="option in roleOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="状态" name="status">
        <a-select
          v-model:value="formState.status"
          placeholder="请选择状态"
        >
          <a-select-option
            v-for="option in statusOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template> 