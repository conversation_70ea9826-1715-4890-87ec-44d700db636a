<template>
  <div class="timeline-list">
    <!-- 加载状态 -->
    <div v-if="loading" class="py-8">
      <el-skeleton :rows="5" animated />
    </div>

    <!-- 空状态 -->
    <el-empty
      v-else-if="archives.length === 0"
      description="暂无文章"
    />

    <!-- 文章列表 -->
    <template v-else>
      <div v-for="year in archives" :key="year.year" class="year-group mb-8">
        <div class="year-header" @click="toggleYear(year.year)">
          <h2 class="text-xl font-bold flex items-center">
            {{ year.year }}年
            <span class="text-sm ml-2 text-[var(--el-text-color-secondary)]">({{ year.count }}篇)</span>
            <el-icon class="ml-2" :class="{ 'transform rotate-180': expandedYears.includes(year.year) }">
              <ArrowDown />
            </el-icon>
          </h2>
        </div>
        
        <div v-show="expandedYears.includes(year.year)" class="year-content mt-4">
          <div v-for="month in year.months" :key="`${year.year}-${month.month}`" class="month-group mb-6">
            <h3 class="text-lg font-semibold mb-3 flex items-center">
              {{ month.month }}月
              <span class="text-sm ml-2 text-[var(--el-text-color-secondary)]">({{ month.count }}篇)</span>
            </h3>
            <div class="space-y-4">
              <div v-for="article in month.articles" :key="article.id" 
                   class="article-item p-4 rounded-lg hover:shadow-md transition-shadow">
                <div class="flex items-center justify-between">
                  <router-link 
                    :to="`/articles/${article.id}`"
                    class="hover:text-[var(--el-color-primary)] font-medium flex-1"
                  >
                    {{ article.title }}
                  </router-link>
                  <div class="flex items-center space-x-4 text-sm text-[var(--el-text-color-secondary)]">
                    <span class="flex items-center">
                      <el-icon class="mr-1"><View /></el-icon>
                      {{ article.viewCount }}
                    </span>
                    <time :datetime="article.createdAt">
                      {{ formatDate(article.createdAt) }}
                    </time>
                  </div>
                </div>
                
                <div class="mt-2 flex items-center flex-wrap gap-2">
                  <template v-if="article.categories?.length > 0">
                    <el-tag 
                      v-for="category in article.categories"
                      :key="category.id"
                      size="small" 
                      type="success" 
                      class="cursor-pointer" 
                      @click.stop="handleCategoryClick(category.id)"
                    >
                      {{ category.name }}
                    </el-tag>
                  </template>
                  <template v-if="article.tags?.length > 0">
                    <el-tag
                      v-for="tag in article.tags"
                      :key="tag.id"
                      size="small"
                      class="cursor-pointer"
                      @click.stop="handleTagClick(tag.id)"
                    >
                      {{ tag.name }}
                    </el-tag>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ArrowDown, View } from '@element-plus/icons-vue'
import type { ArchiveYear } from '@/types/archive'
import { useRouter } from 'vue-router'

const props = defineProps<{
  archives: ArchiveYear[]
  loading?: boolean
}>()

const router = useRouter()
const expandedYears = ref<string[]>([])

// 监听 archives 变化，自动展开第一年
watch(() => props.archives, (newArchives) => {
  if (newArchives.length > 0) {
    // 如果当前没有展开的年份，则展开第一年
    if (expandedYears.value.length === 0) {
      expandedYears.value = [newArchives[0].year]
    }
  } else {
    expandedYears.value = []
  }
}, { immediate: true })

// 切换年份展开/折叠
const toggleYear = (year: string) => {
  const index = expandedYears.value.indexOf(year)
  if (index > -1) {
    expandedYears.value.splice(index, 1)
  } else {
    expandedYears.value.push(year)
  }
}

// 格式化日期
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 处理标签点击
const handleTagClick = (tagId: number) => {
  if (tagId) {
    router.push(`/tags/${tagId}`)
  }
}

// 处理分类点击
const handleCategoryClick = (categoryId: number | undefined) => {
  if (categoryId) {
    router.push(`/categories/${categoryId}`)
  }
}
</script>

<style scoped>
.timeline-list {
  @apply space-y-8;
}

.year-group {
  @apply rounded-lg shadow-sm overflow-hidden;
  background-color: var(--el-bg-color);
}

.year-header {
  @apply p-4 cursor-pointer transition-colors;
  color: var(--el-text-color-primary);
}

.year-header:hover {
  background-color: var(--el-fill-color-light);
}

.year-content {
  @apply p-4;
  border-top: 1px solid var(--el-border-color);
}

.month-group:last-child {
  @apply mb-0;
}

.article-item {
  @apply transition-all duration-300;
  background-color: var(--el-bg-color);
  color: var(--el-text-color-primary);
}

.article-item:hover {
  @apply transform translate-x-1;
  background-color: var(--el-fill-color-light);
}

:deep(.el-icon) {
  @apply transition-transform duration-300;
}
</style> 