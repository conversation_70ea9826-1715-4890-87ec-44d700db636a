// 暂时注释整个文件内容

// import { http } from '@/utils/request'
// import type { R, PageResult } from '@/types/common'

// export interface RewardConfig {
//   userId: number
//   enabled: boolean
//   amounts: number[]
//   qrcodeWechat: string
//   qrcodeAlipay: string
// }

// export interface RewardRecord {
//   id: number
//   articleId: number
//   userId: number
//   username: string
//   userAvatar: string
//   amount: number
//   paymentType: string
//   paymentStatus: string
//   message: string
//   createdAt: string
// }

// /**
//  * 获取打赏配置
//  */
// export function getRewardConfig(userId: number) {
//   return http<R<RewardConfig>>({
//     url: `/rewards/config/${userId}`,
//     method: 'get'
//   })
// }

// /**
//  * 更新打赏配置
//  */
// export function updateRewardConfig(config: RewardConfig) {
//   return http<R<void>>({
//     url: '/rewards/config',
//     method: 'put',
//     data: config
//   })
// }

// /**
//  * 创建打赏记录
//  */
// export function createReward(params: {
//   articleId: number
//   userId: number
//   amount: number
//   paymentType: string
//   message?: string
// }) {
//   return http<R<any>>({
//     url: `/rewards/articles/${params.articleId}`,
//     method: 'post',
//     params
//   })
// }

// /**
//  * 获取打赏记录
//  */
// export function getRewardRecords(articleId: number, page: number = 1, size: number = 10) {
//   return http<R<PageResult<RewardRecord>>>({
//     url: `/rewards/articles/${articleId}/records`,
//     method: 'get',
//     params: { page, size }
//   })
// }

// /**
//  * 获取总打赏金额
//  */
// export function getTotalRewardAmount(articleId: number) {
//   return http<R<number>>({
//     url: `/rewards/articles/${articleId}/total`,
//     method: 'get'
//   })
// }

// /**
//  * 更新支付状态
//  */
// export function updatePaymentStatus(rewardId: number, status: string, transactionId: string) {
//   return http<R<void>>({
//     url: `/rewards/${rewardId}/status`,
//     method: 'put',
//     params: { status, transactionId }
//   })
// }
