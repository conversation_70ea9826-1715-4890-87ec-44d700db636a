package com.modelbolt.blog.utils;

import eu.bitwalker.useragentutils.UserAgent;
import jakarta.servlet.http.HttpServletRequest;

/**
 * User-Agent工具类
 * 用于解析HTTP请求中的User-Agent信息，获取浏览器类型和操作系统等信息
 */
public class UserAgentUtil {

    /**
     * 从请求中获取User-Agent字符串
     * 
     * @param request HTTP请求
     * @return User-Agent字符串
     */
    public static String getUserAgent(HttpServletRequest request) {
        return request.getHeader("User-Agent");
    }
    
    /**
     * 获取浏览器类型
     * 
     * @param userAgent User-Agent字符串
     * @return 浏览器类型名称
     */
    public static String getBrowser(String userAgent) {
        UserAgent agent = UserAgent.parseUserAgentString(userAgent);
        return agent.getBrowser().getName();
    }
    
    /**
     * 获取操作系统
     * 
     * @param userAgent User-Agent字符串
     * @return 操作系统名称
     */
    public static String getOs(String userAgent) {
        UserAgent agent = UserAgent.parseUserAgentString(userAgent);
        return agent.getOperatingSystem().getName();
    }
    
    /**
     * 从请求中直接获取浏览器类型
     * 
     * @param request HTTP请求
     * @return 浏览器类型名称
     */
    public static String getBrowserFromRequest(HttpServletRequest request) {
        return getBrowser(getUserAgent(request));
    }
    
    /**
     * 从请求中直接获取操作系统
     * 
     * @param request HTTP请求
     * @return 操作系统名称
     */
    public static String getOsFromRequest(HttpServletRequest request) {
        return getOs(getUserAgent(request));
    }
} 