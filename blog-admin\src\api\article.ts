import { http } from '@/utils/request'
import type { R, PageResult } from '@/types/common'
import type { Article, ArticleForm, ArticleQuery } from '@/types/article'

/**
 * 获取文章列表
 */
export function getArticleList(params: ArticleQuery) {
  return http.get<R<PageResult<Article>>>('/admin/article/list', { params })
}

/**
 * 获取文章详情
 */
export function getArticleDetail(id: number) {
  return http.get<R<Article>>(`/admin/article/${id}`)
}

/**
 * 获取文章编辑信息
 */
export function getArticleForEdit(id: number) {
  return http.get<R<Article>>(`/admin/article/${id}/edit`)
}

/**
 * 创建文章
 */
export function createArticle(data: ArticleForm) {
  const submitData = {
    ...data,
    isTop: Boolean(data.isTop)
  }
  return http.post<R<Article>>('/admin/article', submitData)
}

/**
 * 更新文章
 */
export function updateArticle(id: number, data: ArticleForm) {
  const submitData = {
    ...data,
    isTop: Boolean(data.isTop)
  }
  return http.put<R<Article>>(`/admin/article/${id}`, submitData)
}

/**
 * 删除文章
 */
export function deleteArticle(id: number) {
  return http.delete<R<void>>(`/admin/article/${id}`)
}

/**
 * 更新文章状态
 */
export function updateArticleStatus(id: number, status: number) {
  return http.put<R<void>>(`/admin/article/${id}/status`, { status })
}

/**
 * 更新文章置顶状态
 */
export function updateArticleTop(id: number, isTop: boolean) {
  return http.put<R<void>>(`/admin/article/${id}/top?isTop=${isTop}`)
}

/**
 * 上传文章封面
 */
export function uploadArticleCover(file: File) {
  const formData = new FormData()
  formData.append('cover', file)
  return http.post<{code: number, message: string, data: null}>('/admin/article/cover', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
} 