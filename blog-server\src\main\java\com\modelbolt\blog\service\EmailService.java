package com.modelbolt.blog.service;

public interface EmailService {
    
    /**
     * 发送验证码邮件
     *
     * @param to 收件人
     * @param code 验证码
     */
    void sendVerificationCode(String to, String code);
    
    /**
     * 发送重置密码邮件
     *
     * @param to 收件人
     * @param resetLink 重置链接
     */
    void sendResetPasswordEmail(String to, String resetLink);
    
    /**
     * 发送通知邮件
     *
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     */
    void sendNotification(String to, String subject, String content);
} 