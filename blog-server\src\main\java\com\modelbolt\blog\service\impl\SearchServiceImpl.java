package com.modelbolt.blog.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.modelbolt.blog.mapper.ArticleMapper;
import com.modelbolt.blog.mapper.TagMapper;
import com.modelbolt.blog.mapper.CategoryMapper;
import com.modelbolt.blog.model.vo.SearchVO;
import com.modelbolt.blog.model.vo.HotSearchVO;
import com.modelbolt.blog.service.SearchService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SearchServiceImpl implements SearchService {
    private final ArticleMapper articleMapper;
    private final TagMapper tagMapper;
    private final CategoryMapper categoryMapper;

    @Override
    public IPage<SearchVO> search(String keyword, String type, Integer page, Integer size) {
        Page<SearchVO> pageParam = new Page<>(page, size);
        
        if ("all".equals(type) || type == null) {
            // 合并所有类型的搜索结果
            List<SearchVO> allResults = new ArrayList<>();
            allResults.addAll(searchArticles(keyword));
            allResults.addAll(searchTags(keyword));
            allResults.addAll(searchCategories(keyword));
            
            // 手动分页
            int start = (int) pageParam.offset();
            int end = Math.min(start + size, allResults.size());
            
            pageParam.setRecords(allResults.subList(start, end));
            pageParam.setTotal(allResults.size());
            return pageParam;
        }
        
        // 根据类型搜索
        switch (type) {
            case "article":
                return new Page<SearchVO>().setRecords(searchArticles(keyword));
            case "tag":
                return new Page<SearchVO>().setRecords(searchTags(keyword));
            case "category":
                return new Page<SearchVO>().setRecords(searchCategories(keyword));
            default:
                return new Page<>();
        }
    }

    @Override
    public List<SearchVO> getSuggestions(String keyword) {
        // 获取前5条建议
        List<SearchVO> suggestions = new ArrayList<>();
        suggestions.addAll(searchArticles(keyword).stream().limit(3).collect(Collectors.toList()));
        suggestions.addAll(searchTags(keyword).stream().limit(1).collect(Collectors.toList()));
        suggestions.addAll(searchCategories(keyword).stream().limit(1).collect(Collectors.toList()));
        return suggestions;
    }

    @Override
    public List<HotSearchVO> getHotSearches() {
        // 从文章标题中获取热门关键词
        return articleMapper.findHotKeywords();
    }

    private List<SearchVO> searchArticles(String keyword) {
        return articleMapper.searchArticles(keyword);
    }

    private List<SearchVO> searchTags(String keyword) {
        return tagMapper.searchTags(keyword);
    }

    private List<SearchVO> searchCategories(String keyword) {
        return categoryMapper.searchCategories(keyword);
    }
} 