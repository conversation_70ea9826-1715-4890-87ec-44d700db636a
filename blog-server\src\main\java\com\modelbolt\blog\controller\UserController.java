package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.model.dto.user.UserInfoDTO;
import com.modelbolt.blog.model.dto.user.AuthorInfoDTO;
import com.modelbolt.blog.model.dto.user.UserSettingsDTO;
import com.modelbolt.blog.model.dto.user.UserProfileDTO;
import com.modelbolt.blog.model.dto.user.UserHomeDTO;
import com.modelbolt.blog.service.UserService;
import com.modelbolt.blog.service.ArticleService;
import com.modelbolt.blog.service.NotificationService;
import com.modelbolt.blog.utils.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
@Tag(name = "用户接口")
public class UserController {

    private final UserService userService;
    private final ArticleService articleService;
    private final NotificationService notificationService;

    @GetMapping("/info")
    public ApiResponse<UserInfoDTO> getUserInfo(Authentication authentication) {
        log.debug("Getting user info for user: {}", authentication.getName());
        UserInfoDTO userInfo = userService.getUserInfo(authentication.getName());
        return ApiResponse.success(userInfo);
    }

    @Operation(summary = "获取作者详细信息")
    @GetMapping("/{id}/author")
    public ApiResponse<AuthorInfoDTO> getAuthorInfo(@PathVariable Long id) {
        log.debug("Getting author info for id: {}", id);
        AuthorInfoDTO authorInfo = userService.getAuthorInfo(id);
        return ApiResponse.success(authorInfo);
    }

    @PostMapping("/{id}/follow")
    @Operation(summary = "关注用户")
    public ApiResponse<Void> followUser(@PathVariable Long id, Authentication authentication) {
        String username = authentication.getName();
        log.debug("Following user: {}, follower: {}", id, username);
        UserInfoDTO currentUser = userService.getUserInfo(username);
        boolean success = userService.followUser(currentUser.getId(), id);
        return success ? ApiResponse.success() : ApiResponse.error("关注失败");
    }

    @DeleteMapping("/{id}/follow")
    @Operation(summary = "取消关注用户")
    public ApiResponse<Void> unfollowUser(@PathVariable Long id, Authentication authentication) {
        String username = authentication.getName();
        log.debug("Unfollowing user: {}, follower: {}", id, username);
        UserInfoDTO currentUser = userService.getUserInfo(username);
        boolean success = userService.unfollowUser(currentUser.getId(), id);
        return success ? ApiResponse.success() : ApiResponse.error("取消关注失败");
    }

    @GetMapping("/{id}/following")
    @Operation(summary = "检查是否已关注")
    public ApiResponse<Boolean> isFollowing(@PathVariable Long id, Authentication authentication) {
        String username = authentication.getName();
        log.debug("Checking if user {} is following {}", username, id);
        UserInfoDTO currentUser = userService.getUserInfo(username);
        boolean following = userService.isFollowing(currentUser.getId(), id);
        return ApiResponse.success(following);
    }

    @PostMapping("/articles/{id}/like")
    @Operation(summary = "点赞文章")
    public ApiResponse<Void> likeArticle(@PathVariable Long id, Authentication authentication) {
        log.debug("User {} liking article {}", authentication.getName(), id);
        articleService.likeArticle(id);
        return ApiResponse.success();
    }

    @DeleteMapping("/articles/{id}/like")
    @Operation(summary = "取消点赞")
    public ApiResponse<Void> unlikeArticle(@PathVariable Long id, Authentication authentication) {
        log.debug("User {} unliking article {}", authentication.getName(), id);
        articleService.unlikeArticle(id);
        return ApiResponse.success();
    }

    @PostMapping("/articles/{id}/favorite")
    @Operation(summary = "收藏文章")
    public ApiResponse<Void> favoriteArticle(@PathVariable Long id, Authentication authentication) {
        log.debug("User {} favoriting article {}", authentication.getName(), id);
        articleService.favoriteArticle(id);
        return ApiResponse.success();
    }

    @DeleteMapping("/articles/{id}/favorite")
    @Operation(summary = "取消收藏")
    public ApiResponse<Void> unfavoriteArticle(@PathVariable Long id, Authentication authentication) {
        log.debug("User {} unfavoriting article {}", authentication.getName(), id);
        articleService.unfavoriteArticle(id);
        return ApiResponse.success();
    }

    @GetMapping("/settings")
    @Operation(summary = "获取用户设置")
    public ApiResponse<UserSettingsDTO> getUserSettings() {
        Long userId = SecurityUtils.getCurrentUserId();
        log.debug("Getting settings for user: {}", userId);
        return ApiResponse.success(userService.getUserSettings(userId));
    }

    @PutMapping("/settings")
    @Operation(summary = "更新用户设置")
    public ApiResponse<Void> updateUserSettings(@RequestBody UserSettingsDTO settings) {
        Long userId = SecurityUtils.getCurrentUserId();
        log.debug("Updating settings for user: {}", userId);
        userService.updateUserSettings(userId, settings);
        return ApiResponse.success();
    }

    @PutMapping("/profile")
    @Operation(summary = "更新用户资料")
    public ApiResponse<Void> updateUserProfile(@RequestBody UserProfileDTO profile) {
        Long userId = SecurityUtils.getCurrentUserId();
        log.debug("Updating profile for user: {}", userId);
        userService.updateUserProfile(userId, profile);
        return ApiResponse.success();
    }

    @PostMapping("/avatar")
    @Operation(summary = "更新用户头像")
    public ApiResponse<String> updateUserAvatar(@RequestParam("avatar") MultipartFile avatar) {
        Long userId = SecurityUtils.getCurrentUserId();
        log.debug("Updating avatar for user: {}", userId);
        String avatarUrl = userService.updateUserAvatar(userId, avatar);
        return ApiResponse.success(avatarUrl);
    }

    @PutMapping("/password")
    @Operation(summary = "修改密码")
    public ApiResponse<Void> updatePassword(@RequestBody Map<String, String> passwordData) {
        Long userId = SecurityUtils.getCurrentUserId();
        log.debug("Updating password for user: {}", userId);
        
        String oldPassword = passwordData.get("oldPassword");
        String newPassword = passwordData.get("newPassword");
        
        if (oldPassword == null || newPassword == null) {
            return ApiResponse.error("密码参数不能为空");
        }
        
        userService.updatePassword(userId, oldPassword, newPassword);
        return ApiResponse.success();
    }

    @GetMapping("/stats")
    @Operation(summary = "获取用户统计数据")
    public ApiResponse<Map<String, Integer>> getUserStats() {
        Long userId = SecurityUtils.getCurrentUserId();
        log.debug("Getting stats for user: {}", userId);
        
        Map<String, Integer> stats = new HashMap<>();
        
        // 获取文章数量
        int articleCount = userService.getArticleCount(userId);
        stats.put("articleCount", articleCount);
        
        // 获取评论数量
        int commentCount = userService.getCommentCount(userId);
        stats.put("commentCount", commentCount);
        
        // 获取收藏数量
        int favoriteCount = userService.getFavoriteCount(userId);
        stats.put("favoriteCount", favoriteCount);
        
        // 获取未读通知数量
        int notificationCount = notificationService.getUnreadCount(userId);
        stats.put("notificationCount", notificationCount);
        
        return ApiResponse.success(stats);
    }

    @GetMapping("/profile/{userId}")
    @Operation(summary = "获取用户主页信息")
    public ApiResponse<UserHomeDTO> getUserHomeInfo(@PathVariable Long userId) {
        log.debug("Getting user home info for user: {}", userId);
        UserHomeDTO userHome = userService.getUserHomeInfo(userId);
        return ApiResponse.success(userHome);
    }
} 