package com.modelbolt.blog.config;

import com.modelbolt.blog.filter.JwtAuthenticationFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.filter.CorsFilter;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;

import static org.springframework.security.web.util.matcher.AntPathRequestMatcher.antMatcher;

@Slf4j
@Configuration
@EnableWebSecurity
// 安全配置类，用于配置Spring Security
public class SecurityConfig {

    private final CorsFilter corsFilter;
    private final JwtAuthenticationFilter jwtAuthenticationFilter;

    public SecurityConfig(CorsFilter corsFilter, JwtAuthenticationFilter jwtAuthenticationFilter) {
        this.corsFilter = corsFilter;
        this.jwtAuthenticationFilter = jwtAuthenticationFilter;
    }

    @Bean
    // 配置安全过滤链，定义了安全过滤规则和过滤器顺序
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        log.debug("Configuring SecurityFilterChain");

        http
            // 禁用CSRF保护
            .csrf(csrf -> csrf.disable())
            // 配置CORS支持
            .cors(cors -> cors.configure(http))
            // 配置会话管理为无状态模式
            .sessionManagement(session ->
                session.sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            )
            // 配置HTTP请求的授权规则
            .authorizeHttpRequests(auth -> {
                // 允许静态资源访问
                auth.requestMatchers(antMatcher("/api/images/**")).permitAll()
                    .requestMatchers(antMatcher("/images/**")).permitAll()
                    .requestMatchers(antMatcher("/static/**")).permitAll();
                // 允许公开路径访问
                auth.requestMatchers(SecurityConstants.PUBLIC_PATHS.toArray(new String[0])).permitAll();
                // 允许GET请求的公开路径前缀
                SecurityConstants.PUBLIC_GET_PATH_PREFIXES.forEach(prefix ->
                    auth.requestMatchers(HttpMethod.GET, prefix + "**").permitAll()
                );
                // WebSocket需要认证
                auth.requestMatchers(antMatcher("/ws/**")).authenticated();
                // 其他所有请求需要认证
                auth.anyRequest().authenticated();
            })
            // 添加CORS和JWT过滤器到安全过滤链中，并指定它们在UsernamePasswordAuthenticationFilter之前工作
            .addFilterBefore(corsFilter, CorsFilter.class)
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
            // 配置认证异常处理器，返回401状态码和自定义错误信息
            .exceptionHandling(exceptions -> exceptions
                .authenticationEntryPoint((request, response, authException) -> {
                    log.debug("Authentication failed: {}", authException.getMessage());
                    if (request.getRequestURI().contains("/ws/")) {
                        // WebSocket连接的认证失败处理
                        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                        return;
                    }
                    // 普通HTTP请求的认证失败处理
                    response.setContentType("application/json;charset=UTF-8");
                    response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                    response.getWriter().write("{\"code\":401,\"message\":\"请先登录\"}");
                })
            );

        return http.build();
    }

    @Bean
    // 提供BCryptPasswordEncoder实例，用于密码加密
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
