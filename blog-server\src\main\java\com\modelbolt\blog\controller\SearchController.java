package com.modelbolt.blog.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.model.vo.SearchVO;
import com.modelbolt.blog.model.vo.HotSearchVO;
import com.modelbolt.blog.service.SearchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "搜索管理", description = "搜索相关接口")
@RestController
@RequestMapping("/search")
@RequiredArgsConstructor
public class SearchController {
    private final SearchService searchService;

    @Operation(summary = "搜索", description = "支持文章、标签、分类的全文搜索")
    @GetMapping
    public ApiResponse<IPage<SearchVO>> search(
            @Parameter(description = "搜索关键词", required = true) @RequestParam String keyword,
            @Parameter(description = "搜索类型: all/article/tag/category") @RequestParam(required = false) String type,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size
    ) {
        return ApiResponse.success(searchService.search(keyword, type, page, size));
    }

    @Operation(summary = "获取搜索建议", description = "根据输入关键词获取搜索建议")
    @GetMapping("/suggestions")
    public ApiResponse<List<SearchVO>> getSuggestions(
            @Parameter(description = "搜索关键词", required = true) @RequestParam String keyword
    ) {
        return ApiResponse.success(searchService.getSuggestions(keyword));
    }

    @Operation(summary = "获取热门搜索", description = "获取热门搜索关键词")
    @GetMapping("/hot")
    public ApiResponse<List<HotSearchVO>> getHotSearches() {
        return ApiResponse.success(searchService.getHotSearches());
    }
} 