package com.modelbolt.blog.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.config.SecurityConstants;
import com.modelbolt.blog.service.SessionService;
import com.modelbolt.blog.utils.JwtUtils;
import com.modelbolt.blog.utils.RedisUtils;
import io.jsonwebtoken.ExpiredJwtException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import lombok.extern.slf4j.Slf4j;

/**
 * JWT认证过滤器
 * 用于处理请求中的JWT令牌验证和用户认证
 */
@Slf4j
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtUtils jwtUtils;
    private final RedisUtils redisUtils;
    private final SessionService sessionService;
    private final ObjectMapper objectMapper;

    public JwtAuthenticationFilter(JwtUtils jwtUtils, RedisUtils redisUtils, SessionService sessionService, ObjectMapper objectMapper) {
        this.jwtUtils = jwtUtils;
        this.redisUtils = redisUtils;
        this.sessionService = sessionService;
        this.objectMapper = objectMapper;
    }

    /**
     * 过滤器的主要处理方法
     * 处理所有进入系统的HTTP请求，进行JWT token的验证和用户认证
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @param filterChain 过滤器链
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        // 处理请求URI，移除/api前缀
        String requestURI = request.getRequestURI().replaceFirst("/api", "");
        
        // 去除查询参数部分
        if (requestURI.contains("?")) {
            requestURI = requestURI.substring(0, requestURI.indexOf("?"));
        }
        
        String queryString = request.getQueryString();
        if (queryString != null && !queryString.isEmpty()) {
            log.debug("Processing request: {} with query: {}", requestURI, queryString);
        } else {
            log.debug("Processing request: {}", requestURI);
        }
        
        // 首先检查是否为静态资源路径，如果是直接放行
        if (isStaticResourcePath(requestURI)) {
            log.debug("Static resource accessed: {}", requestURI);
            filterChain.doFilter(request, response);
            return;
        }

        // 尝试从请求头中提取JWT token
        String token = extractToken(request);
        log.debug("Token from request: {}", token);
        
        boolean isAuthenticated = false;

        // 如果有token，先尝试验证，不管是什么路径
        if (StringUtils.hasText(token)) {
            try {
                // 验证JWT token的有效性
                if (jwtUtils.validateToken(token)) {
                    // 从token中提取用户名和角色
                    String username = jwtUtils.getUsernameFromToken(token);
                    String role = jwtUtils.getRoleFromToken(token);
                    log.debug("Username from token: {}", username);
                    log.debug("Role from token: {}", role);

                    // 从Redis中获取保存的token
                    String redisToken = (String) redisUtils.get("token:" + username);
                    log.debug("Redis token: {}", redisToken);

                    // 验证当前token与Redis中的token是否匹配
                    if (!token.equals(redisToken)) {
                        log.debug("Token mismatch or expired in Redis");
                        // 不立即返回错误，继续检查是否是公开路径
                    } else {
                        // 从token中提取会话ID
                        String tokenId = jwtUtils.getTokenIdFromToken(token);
                        log.debug("Token ID from token: {}", tokenId);

                        // 验证会话的有效性
                        if (sessionService.validateSession(tokenId)) {
                            // 创建Spring Security的认证对象并设置到上下文中
                            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                                    username,
                                    null,
                                    Collections.singletonList(new SimpleGrantedAuthority("ROLE_" + role))
                            );
                            SecurityContextHolder.getContext().setAuthentication(authentication);
                            isAuthenticated = true;
                        }
                    }
                }
            } catch (ExpiredJwtException e) {
                log.debug("Token has expired: {}", e.getMessage());
                // 不立即返回错误，继续检查是否是公开路径
            } catch (Exception e) {
                log.error("Token validation error", e);
                // 不立即返回错误，继续检查是否是公开路径
            }
        }

        // 检查是否为公开路径或者公开资源的GET请求
        boolean isPublic = isPublicPath(requestURI) || 
                          (request.getMethod().equals("GET") && isPublicGetPath(requestURI));
        
        if (isPublic) {
            log.debug("Public path accessed: {}, method: {}, allowing access", requestURI, request.getMethod());
            filterChain.doFilter(request, response);
            return;
        }
        
        // 如果不是公开路径且没有通过认证，则返回401
        if (!isAuthenticated) {
            log.debug("Authentication required but not provided for path: {}, method: {}", 
                     requestURI, request.getMethod());
            handleAuthenticationFailure(response, "请先登录", "AUTHENTICATION_REQUIRED");
            return;
        }

        // 已认证的请求，继续过滤器链
        filterChain.doFilter(request, response);
    }

    /**
     * 处理认证失败情况
     * 
     * @param response HTTP响应对象
     * @param message 错误消息
     * @param errorCode 错误代码
     */
    private void handleAuthenticationFailure(HttpServletResponse response, String message, String errorCode) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        
        ApiResponse<Object> apiResponse = ApiResponse.builder()
                .code(401)
                .message(message)
                .error(errorCode)
                .build();
        
        response.getWriter().write(objectMapper.writeValueAsString(apiResponse));
    }

    /**
     * 从请求中提取JWT令牌
     * 依次从以下位置尝试获取token：
     * 1. 请求头的Authorization字段
     * 2. URL参数中的token参数
     * 3. WebSocket连接的查询字符串中的token参数
     *
     * @param request HTTP请求对象
     * @return 提取到的JWT令牌，如果未找到则返回null
     */
    private String extractToken(HttpServletRequest request) {
        // 首先尝试从请求头中提取JWT token
        String bearerToken = request.getHeader(HttpHeaders.AUTHORIZATION);
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }

        // 如果请求头中没有token，尝试从URL参数中获取
        String token = request.getParameter("token");
        if (StringUtils.hasText(token)) {
            log.debug("Token found in URL parameter");
            return token;
        }

        // 如果是WebSocket握手请求，从查询字符串中获取token
        if (request.getRequestURI().contains("/ws/")) {
            String queryString = request.getQueryString();
            if (StringUtils.hasText(queryString) && queryString.contains("token=")) {
                String[] params = queryString.split("&");
                for (String param : params) {
                    if (param.startsWith("token=")) {
                        token = param.substring(6);
                        log.debug("Token found in WebSocket query string");
                        return token;
                    }
                }
            }
        }

        return null;
    }

    /**
     * 判断请求路径是否为公开路径
     * 检查请求的URL是否匹配预定义的公开访问路径列表
     *
     * @param path 请求路径
     * @return 如果是公开路径返回true，否则返回false
     */
    private boolean isPublicPath(String path) {
        // 去除查询参数部分，仅保留路径部分
        final String pathOnly;
        if (path.contains("?")) {
            pathOnly = path.substring(0, path.indexOf("?"));
        } else {
            pathOnly = path;
        }
        
        log.debug("Checking if path is public path: {}", pathOnly);
        
        // 简化匹配逻辑：移除路径末尾斜杠，然后比较路径的主要部分
        final String normalizedPath = pathOnly.endsWith("/") ? pathOnly.substring(0, pathOnly.length() - 1) : pathOnly;
        log.debug("Normalized path for public path matching: {}", normalizedPath);
        
        for (String publicPath : SecurityConstants.PUBLIC_PATHS) {
            // 对于包含通配符的路径进行特殊处理
            if (publicPath.endsWith("/**")) {
                String basePath = publicPath.substring(0, publicPath.length() - 3);
                if (normalizedPath.equals(basePath)) {
                    log.debug("Found exact matching wildcard public path: {} matches {}", normalizedPath, publicPath);
                    return true;
                } else if (normalizedPath.startsWith(basePath + "/")) {
                    log.debug("Found prefix matching wildcard public path: {} matches {}", normalizedPath, publicPath);
                    return true;
                }
            } else {
                // 标准化公开路径，移除末尾斜杠
                String normalizedPublicPath = publicPath.endsWith("/") ? 
                    publicPath.substring(0, publicPath.length() - 1) : publicPath;
                
                // 完全匹配
                if (normalizedPath.equals(normalizedPublicPath)) {
                    log.debug("Found exact matching public path: {} matches {}", normalizedPath, publicPath);
                    return true;
                } 
                // 处理路径变量（如 /user/{id}/author）
                else if (matchesPathWithVariables(normalizedPath, normalizedPublicPath)) {
                    log.debug("Found variable matching public path: {} matches pattern {}", normalizedPath, publicPath);
                    return true;
                }
            }
        }
        
        log.debug("No matching public path found for: {}", normalizedPath);
        return false;
    }

    /**
     * 匹配包含路径变量的路径
     * 例如：/user/1/author 应该匹配 /user/{id}/author
     *
     * @param actualPath 实际请求路径
     * @param patternPath 带变量的路径模式
     * @return 是否匹配
     */
    private boolean matchesPathWithVariables(String actualPath, String patternPath) {
        if (!patternPath.contains("{")) {
            return false;
        }
        
        String[] actualParts = actualPath.split("/");
        String[] patternParts = patternPath.split("/");
        
        if (actualParts.length != patternParts.length) {
            return false;
        }
        
        for (int i = 0; i < patternParts.length; i++) {
            if (patternParts[i].startsWith("{") && patternParts[i].endsWith("}")) {
                // 这是一个路径变量，跳过比较
                continue;
            } else if (!patternParts[i].equals(actualParts[i])) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 判断GET请求的路径是否为公开资源路径
     * 检查GET请求的URL是否匹配预定义的公开资源路径前缀列表
     *
     * @param path 请求路径
     * @return 如果是公开资源路径返回true，否则返回false
     */
    private boolean isPublicGetPath(String path) {
        // 去除查询参数部分，仅保留路径部分
        final String pathOnly;
        if (path.contains("?")) {
            pathOnly = path.substring(0, path.indexOf("?"));
        } else {
            pathOnly = path;
        }
        
        log.debug("Checking if path is public GET path: {}", pathOnly);
        
        // 简化匹配逻辑：移除路径末尾斜杠，然后比较路径的主要部分
        final String normalizedPath = pathOnly.endsWith("/") ? pathOnly.substring(0, pathOnly.length() - 1) : pathOnly;
        log.debug("Normalized path for matching: {}", normalizedPath);
        
        for (String prefix : SecurityConstants.PUBLIC_GET_PATH_PREFIXES) {
            // 标准化前缀，移除末尾斜杠
            String normalizedPrefix = prefix.endsWith("/") ? prefix.substring(0, prefix.length() - 1) : prefix;
            
            // 完全匹配或前缀匹配
            if (normalizedPath.equals(normalizedPrefix)) {
                log.debug("Found exact matching public GET path: {}", prefix);
                return true;
            } else if (normalizedPath.startsWith(normalizedPrefix + "/")) {
                log.debug("Found prefix matching public GET path: {} matches prefix {}", normalizedPath, prefix);
                return true;
            }
        }
        
        log.debug("No matching public GET path found for: {}", normalizedPath);
        return false;
    }

    /**
     * 判断请求路径是否为静态资源路径
     * 静态资源路径不需要进行JWT认证
     * 
     * @param path 请求路径
     * @return 如果是静态资源路径返回true，否则返回false
     */
    private boolean isStaticResourcePath(String path) {
        // 去除查询参数部分，仅保留路径部分
        final String pathOnly;
        if (path.contains("?")) {
            pathOnly = path.substring(0, path.indexOf("?"));
        } else {
            pathOnly = path;
        }
        
        // 定义静态资源路径前缀
        final List<String> STATIC_PATHS = List.of(
            "/images/",  // 图片资源
            "/static/",  // 静态资源
            "/favicon.ico",  // 网站图标
            "/css/",  // CSS样式
            "/js/",   // JavaScript脚本
            "/fonts/" // 字体文件
        );
        
        // 标准化路径
        final String normalizedPath = pathOnly.endsWith("/") ? pathOnly.substring(0, pathOnly.length() - 1) : pathOnly;
        
        // 对单个文件进行精确匹配
        if (normalizedPath.equals("/favicon.ico")) {
            return true;
        }
        
        // 对目录进行前缀匹配
        for (String staticPath : STATIC_PATHS) {
            if (staticPath.equals("/favicon.ico")) {
                continue; // 已经在上面处理过了
            }
            
            // 标准化静态路径前缀，移除末尾斜杠
            String normalizedStaticPath = staticPath.endsWith("/") ? 
                staticPath.substring(0, staticPath.length() - 1) : staticPath;
            
            if (normalizedPath.equals(normalizedStaticPath) || 
                normalizedPath.startsWith(normalizedStaticPath + "/")) {
                log.debug("Found matching static resource path: {}", staticPath);
                return true;
            }
        }
        
        return false;
    }
}
