package com.modelbolt.blog.model.dto.ai;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class ChatRequest {
    /**
     * 聊天消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    private String message;

    /**
     * 文章ID
     */
    private Long articleId;

    /**
     * 对话上下文
     */
    private List<ChatMessage> context;

    private String originalContent;  // 原始文章内容
    private String originalTitle;    // 原始标题
    private String originalSummary;  // 原始摘要
} 