<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 文章列表 -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <!-- 排序和视图切换 -->
      <div class="flex justify-between items-center mb-6">
        <div class="flex items-center space-x-4">
          <button
            v-for="sort in ['newest', 'hottest', 'recommended']"
            :key="sort"
            class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200"
            :class="[
              currentSort === sort
                ? 'bg-blue-600 text-white'
                : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
            ]"
            @click="currentSort = sort"
          >
            {{ sort === 'newest' ? '最新' : sort === 'hottest' ? '最热' : '推荐' }}
          </button>
        </div>

        <div class="flex items-center space-x-2">
          <button
            class="p-2 rounded-lg text-gray-600 hover:bg-gray-100 transition-colors duration-200"
            :class="{ 'text-blue-600': viewMode === 'card' }"
            @click="viewMode = 'card'"
          >
            <el-icon><Grid /></el-icon>
          </button>
          <button
            class="p-2 rounded-lg text-gray-600 hover:bg-gray-100 transition-colors duration-200"
            :class="{ 'text-blue-600': viewMode === 'list' }"
            @click="viewMode = 'list'"
          >
            <el-icon><List /></el-icon>
          </button>
        </div>
      </div>

      <!-- 文章列表组件 -->
      <el-skeleton :loading="loading" animated :count="3" :throttle="500">
        <template #default>
          <ArticleList
            :articles="articles"
            :total="total"
            :model-value="currentSort"
            :view-mode="viewMode"
            :current-page="currentPage"
            :page-size="pageSize"
            @update:model-value="(val) => currentSort = val"
            @update:current-page="handlePageChange"
            @update:page-size="handleSizeChange"
          />
        </template>
        <template #template>
          <div class="space-y-6">
            <div v-for="i in 3" :key="i" class="flex gap-6">
              <el-skeleton-item variant="image" style="width: 240px; height: 160px" />
              <div class="flex-1">
                <el-skeleton-item variant="h3" style="width: 50%" />
                <div class="my-4">
                  <el-skeleton-item variant="text" style="width: 100%" />
                  <el-skeleton-item variant="text" style="width: 80%" />
                </div>
                <el-skeleton-item variant="text" style="width: 30%" />
              </div>
            </div>
          </div>
        </template>
      </el-skeleton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { Grid, List } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import ArticleList from '@/components/article/ArticleList.vue'
import { getArticles } from '@/api/article'
import type { ArticleVO } from '@/types/article'

// 视图状态
const currentSort = ref<'newest' | 'hottest' | 'recommended'>('newest')
const viewMode = ref<'card' | 'list'>('card')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

// 文章数据
const articles = ref<ArticleVO[]>([])

// 加载文章数据
const loadArticles = async () => {
  loading.value = true
  try {
    const response = await getArticles({
      sort: currentSort.value,
      page: currentPage.value,
      size: pageSize.value
    })
    if (response?.code === 200 && response.data) {
      articles.value = response.data.records || []
      total.value = response.data.total || 0
    } else {
      ElMessage.error(response?.msg || '加载文章列表失败')
    }
  } catch (error: any) {
    console.error('Failed to load articles:', error)
    ElMessage.error(error.response?.data?.msg || '加载文章列表失败')
  } finally {
    loading.value = false
  }
}

// 处理分页
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadArticles()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadArticles()
}

// 监听排序变化
watch(currentSort, () => {
  currentPage.value = 1
  loadArticles()
})

// 初始加载
onMounted(() => {
  loadArticles()
})
</script> 