# 添加要在索引期间忽略的目录或文件模式（例如 foo/ 或 *.csv）
# IDE和编辑器文件
.idea/
.vscode/
*.iml
*.iws
*.ipr
.settings/
.project
.classpath

# 构建和依赖目录
node_modules/
dist/
target/
build/
.gradle/
out/

# 打包文件
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# 日志和数据库文件
# *.log
# logs/
# *.sqlite
# *.db

# 缓存和临时文件
.cache/
.temp/
.tmp/
*.swp
*.swo
.DS_Store
Thumbs.db

# 测试覆盖率和报告
coverage/
.nyc_output/
test-results/
reports/

# 环境配置文件
# .env
# .env.*
# !.env.example

# 自动生成的文件
auto-imports.d.ts
components.d.ts
*.generated.*

# Vue相关文件
.nuxt/
.output/
.nitro/

# Spring Boot相关文件
# application-*.yml
# !application.yml
# !application-example.yml

# 文档相关
docs/_site/
docs/.vuepress/dist/

# 其他文件
*.bak
*.orig
*.patch
*.diff
