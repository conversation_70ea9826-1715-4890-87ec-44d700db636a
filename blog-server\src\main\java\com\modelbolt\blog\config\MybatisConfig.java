package com.modelbolt.blog.config;

import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * MyBatis-Plus 配置类
 */
@Configuration
@Profile("test")
public class MybatisConfig {

    /**
     * 分页插件
     */
    @Bean
    @Profile("test")
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        return interceptor;
    }

    /**
     * 全局配置
     */
    @Bean
    @Profile("test")
    public GlobalConfig globalConfigForTest() {
        GlobalConfig globalConfig = new GlobalConfig();
        globalConfig.setDbConfig(new GlobalConfig.DbConfig()
                .setTablePrefix("")
                .setColumnFormat("\"%s\"") // 在测试环境中，列名使用双引号包裹
        );
        return globalConfig;
    }
} 