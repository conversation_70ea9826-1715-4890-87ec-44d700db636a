import { http } from '@/utils/request'
import type { R, PageResult } from '@/types/common'
import type { ArticleVO } from '@/types/article'
import type { CommentVO } from '@/types/comment'
import type { NotificationVO } from '@/types/notification'
import type { UserProfileForm, UserSettings, FollowQueryParams, FollowUserInfo, FollowStats } from '@/types/user'
import type { UserFavoriteVO } from '@/types/favorite'

interface QueryParams {
  page: number
  size: number
  keyword?: string
}

// 获取用户文章列表
export const getUserArticles = (params: QueryParams & { status?: string }) => {
  return http.get<R<PageResult<ArticleVO>>>('/user/articles', { params })
}

// 获取用户评论列表
export const getUserComments = (params: QueryParams) => {
  return http.get<R<PageResult<CommentVO>>>('/user/comments', { params })
}

// 删除评论
export const deleteComment = (id: number) => {
  return http.delete<R<null>>(`/user/comments/${id}`)
}

// 获取用户收藏列表
export const getUserFavorites = (params: QueryParams) => {
  return http.get<R<PageResult<UserFavoriteVO>>>('/user/favorites', { params })
}

// 取消收藏
export const cancelFavorite = (articleId: number) => {
  return http.delete<R<null>>(`/user/articles/${articleId}/favorite`)
}

// 获取消息通知列表
export const getNotifications = (params: QueryParams & { 
  type?: 'all' | 'comment' | 'like' | 'system'
  isRead?: boolean 
}) => {
  return http.get<R<PageResult<NotificationVO>>>('/user/notifications', { params })
}

// 标记消息已读
export const markNotificationRead = (id: number) => {
  return http.put<R<null>>(`/user/notifications/${id}/read`)
}

// 标记所有消息已读
export const markAllNotificationsRead = () => {
  return http.put<R<null>>('/user/notifications/read-all')
}

// 删除消息
export const deleteNotification = (id: number) => {
  return http.delete<R<null>>(`/user/notifications/${id}`)
}

// 获取未读消息数量
export const getUnreadCount = () => {
  return http.get<R<number>>('/user/notifications/unread-count')
}

// 获取用户统计数据
export const getUserStats = () => {
  return http.get<R<{
    articleCount: number
    commentCount: number
    favoriteCount: number
    notificationCount: number
  }>>('/user/stats')
}

/**
 * 更新用户资料
 */
export const updateUserProfile = (data: UserProfileForm) => {
  return http.put<R<void>>('/user/profile', data)
}

/**
 * 更新用户头像
 */
export const updateUserAvatar = (file: File) => {
  const formData = new FormData()
  formData.append('avatar', file)
  return http.post<R<string>>('/user/avatar', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取用户设置
 */
export const getUserSettings = () => {
  return http.get<R<UserSettings>>('/user/settings')
}

/**
 * 更新用户设置
 */
export const updateUserSettings = (data: UserSettings) => {
  return http.put<R<void>>('/user/settings', data)
}

/**
 * 修改密码
 */
export const updatePassword = (data: { oldPassword: string; newPassword: string }) => {
  return http.put<R<void>>('/user/password', data)
}

/**
 * 关注用户
 */
export const followUser = (userId: number) => {
  return http.post<R<null>>(`/user/follow/${userId}`)
}

/**
 * 取消关注
 */
export const unfollowUser = (userId: number) => {
  return http.delete<R<null>>(`/user/follow/${userId}`)
}

/**
 * 获取关注列表
 */
export const getFollowList = (params: FollowQueryParams & { userId?: number }) => {
  return http.get<R<PageResult<FollowUserInfo>>>('/user/follows', { params })
}

/**
 * 获取关注统计
 */
export const getFollowStats = (userId: number) => {
  return http.get<R<FollowStats>>(`/user/follow/stats/${userId}`)
}

/**
 * 检查是否关注
 */
export const checkIsFollowing = (userId: number) => {
  return http.get<R<boolean>>(`/user/follow/check/${userId}`)
}

/**
 * 获取用户信息
 */
export const getUserProfile = (userId: number) => {
  return http.get<R<{
    id: number
    username: string
    avatar: string
    bio: string
    stats: {
      articles: number
      following: number
      followers: number
    }
  }>>(`/user/profile/${userId}`)
} 
