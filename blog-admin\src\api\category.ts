import { http } from '@/utils/request'
import type { R } from '@/types/common'
import type { CategoryVO, Category } from '@/types/category'
import type { TagVO } from '@/types/tag'

/**
 * 获取所有分类
 */
export function getAllCategories() {
  return http.get<R<CategoryVO[]>>('/admin/category/list')
}

/**
 * 获取所有标签
 */
export function getAllTags() {
  return http.get<R<TagVO[]>>('/admin/tag/list')
}

/**
 * 分类管理相关接口
 */
export const categoryApi = {
  /**
   * 获取分类树形列表
   */
  getTreeList() {
    return http.get<R<Category[]>>('/admin/category/tree')
  },

  /**
   * 创建分类
   */
  create(data: Omit<Category, 'id' | 'children' | 'isEditing' | 'articleCount'>) {
    return http.post<R<Category>>('/admin/category', data)
  },

  /**
   * 更新分类
   */
  update(id: number, data: Partial<Omit<Category, 'id' | 'children' | 'isEditing' | 'articleCount'>>) {
    return http.put<R<Category>>(`/admin/category/${id}`, data)
  },

  /**
   * 删除分类
   */
  delete(id: number) {
    return http.delete<R<null>>(`/admin/category/${id}`)
  },

  /**
   * 更新分类排序
   */
  updateOrder(id: number, orderNum: number) {
    return http.put<R<Category>>(`/admin/category/${id}/order`, { orderNum })
  }
} 