package com.modelbolt.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.modelbolt.blog.model.entity.Article;
import com.modelbolt.blog.model.entity.ShareRecord;
import com.modelbolt.blog.model.entity.User;
import com.modelbolt.blog.mapper.ArticleMapper;
import com.modelbolt.blog.mapper.ShareRecordMapper;
import com.modelbolt.blog.mapper.UserMapper;
import com.modelbolt.blog.model.dto.share.PosterConfigDTO;
import com.modelbolt.blog.model.dto.share.ShareRecordDTO;
import com.modelbolt.blog.service.ShareService;
import com.modelbolt.blog.utils.IpUtil;
import com.modelbolt.blog.utils.PosterUtil;
import com.modelbolt.blog.utils.UserAgentUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.beans.factory.annotation.Value;

import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;

/**
 * 分享服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShareServiceImpl implements ShareService {

    private final ShareRecordMapper shareRecordMapper;
    private final ArticleMapper articleMapper;
    private final UserMapper userMapper;
    private final PosterUtil posterUtil;
    @Value("${file.upload.path}")
    private String uploadPath;

    @Override
    public String generatePoster(PosterConfigDTO config) {
        String posterData = posterUtil.generatePoster(config);
        
        // 保存到文件系统（如果需要）
        String fileName = "poster_" + System.currentTimeMillis() + ".png";
        try {
            // Base64解码
            String base64Data = posterData.split(",")[1];
            byte[] decodedBytes = Base64.getDecoder().decode(base64Data);
            
            Path path = Paths.get(uploadPath + "/posters/" + fileName);
            Files.createDirectories(path.getParent());
            Files.write(path, decodedBytes);
            
            return "/images/posters/" + fileName;
        } catch (IOException e) {
            log.error("保存海报文件失败", e);
            throw new RuntimeException("保存海报文件失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordShare(ShareRecordDTO record) {
        log.debug("Recording share event for article: {}, platform: {}", record.getArticleId(), record.getPlatform());
        
        // 检查文章是否存在
        Article article = articleMapper.selectById(record.getArticleId());
        if (article == null) {
            log.error("Article not found with id: {}", record.getArticleId());
            throw new RuntimeException("文章不存在");
        }

        ShareRecord shareRecord = new ShareRecord();
        shareRecord.setArticleId(record.getArticleId());
        shareRecord.setPlatform(record.getPlatform());
        
        // 获取当前登录用户ID
        Long userId = getCurrentUserId();
        if (userId != null) {
            shareRecord.setUserId(userId);
            log.debug("Recording share for user: {}", userId);
        } else {
            log.debug("Recording anonymous share");
        }
        
        // 获取当前请求
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        
        // 设置IP和User-Agent
        shareRecord.setIpAddress(IpUtil.getIpAddress(request));
        shareRecord.setUserAgent(UserAgentUtil.getUserAgent(request));
        
        // 保存分享记录
        shareRecordMapper.insert(shareRecord);
        log.debug("Share record saved successfully");
    }

    /**
     * 获取当前登录用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() && 
            !(authentication instanceof AnonymousAuthenticationToken)) {
            try {
                String username = authentication.getName();
                // 通过用户名查询用户ID
                User user = userMapper.selectOne(
                    new LambdaQueryWrapper<User>()
                        .eq(User::getUsername, username)
                );
                if (user != null) {
                    return user.getId();
                }
            } catch (Exception e) {
                log.warn("Failed to get user id for username: {}", authentication.getName(), e);
            }
            log.warn("Unable to extract userId from authentication");
        }
        return null;
    }
} 