package com.modelbolt.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.model.entity.AdminSettings;
import com.modelbolt.blog.mapper.AdminSettingsMapper;
import com.modelbolt.blog.service.AdminSettingsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 管理员设置服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdminSettingsServiceImpl implements AdminSettingsService {

    private final AdminSettingsMapper adminSettingsMapper;

    @Override
    public ApiResponse<AdminSettings> getSettings(Long adminId) {
        try {
            AdminSettings settings = adminSettingsMapper.selectOne(
                new LambdaQueryWrapper<AdminSettings>()
                    .eq(AdminSettings::getAdminId, adminId)
            );
            
            // 如果没有设置记录，创建默认设置
            if (settings == null) {
                settings = new AdminSettings()
                    .setAdminId(adminId)
                    .setEmailVerificationEnabled(true);
                adminSettingsMapper.insert(settings);
            }
            
            return ApiResponse.success(settings);
        } catch (Exception e) {
            log.error("Failed to get admin settings", e);
            return ApiResponse.error("获取设置失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<AdminSettings> updateSettings(Long adminId, AdminSettings settings) {
        try {
            AdminSettings existingSettings = adminSettingsMapper.selectOne(
                new LambdaQueryWrapper<AdminSettings>()
                    .eq(AdminSettings::getAdminId, adminId)
            );
            
            if (existingSettings == null) {
                settings.setAdminId(adminId);
                adminSettingsMapper.insert(settings);
                return ApiResponse.success(settings);
            }
            
            existingSettings.setEmailVerificationEnabled(settings.getEmailVerificationEnabled());
            adminSettingsMapper.updateById(existingSettings);
            
            return ApiResponse.success(existingSettings);
        } catch (Exception e) {
            log.error("Failed to update admin settings", e);
            return ApiResponse.error("更新设置失败");
        }
    }
} 