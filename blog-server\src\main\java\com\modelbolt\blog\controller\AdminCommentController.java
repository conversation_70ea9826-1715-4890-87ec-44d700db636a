package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.comment.CommentAuditDTO;
import com.modelbolt.blog.model.dto.comment.CommentQuery;
import com.modelbolt.blog.model.dto.comment.CommentVO;
import com.modelbolt.blog.service.CommentService;
import com.modelbolt.blog.annotation.OperationLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/admin/comment")
@RequiredArgsConstructor
@Tag(name = "管理后台-评论管理", description = "管理后台评论管理相关接口")
@PreAuthorize("hasRole('ADMIN')")
public class AdminCommentController {

    private final CommentService commentService;

    @Operation(summary = "获取评论列表")
    @GetMapping("/list")
    public ApiResponse<PageResult<CommentVO>> getList(CommentQuery query) {
        return ApiResponse.success(commentService.getCommentList(query));
    }

    @Operation(summary = "审核评论")
    @PutMapping("/{id}/audit")
    @OperationLog("审核评论")
    public ApiResponse<Void> audit(
        @Parameter(description = "评论ID") @PathVariable Long id,
        @Valid @RequestBody CommentAuditDTO dto
    ) {
        commentService.auditComment(id, dto);
        return ApiResponse.success();
    }

    @Operation(summary = "删除评论")
    @DeleteMapping("/{id}")
    @OperationLog("删除评论")
    public ApiResponse<Void> delete(
        @Parameter(description = "评论ID") @PathVariable Long id
    ) {
        commentService.deleteComment(id);
        return ApiResponse.success();
    }

    @Operation(summary = "批量删除评论")
    @DeleteMapping("/batch")
    @OperationLog("批量删除评论")
    public ApiResponse<Void> batchDelete(@RequestBody List<Long> ids) {
        commentService.batchDeleteComments(ids);
        return ApiResponse.success();
    }
} 