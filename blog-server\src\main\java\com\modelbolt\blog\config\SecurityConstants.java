package com.modelbolt.blog.config;

import java.util.List;

/**
 * 安全配置常量
 */
public class SecurityConstants {
    
    /**
     * 公开路径
     */
    public static final List<String> PUBLIC_PATHS = List.of(
        // 认证相关
        "/auth/admin/login",
        "/auth/admin/send-verification-code",
        "/auth/admin/logout",
        "/auth/login",
        "/auth/register",
        "/auth/refresh-token",  // 添加刷新令牌端点
        "/auth/password/reset/email",
        "/auth/password/reset",
        "/auth/send-verification-code",
        // 文章浏览量
        "/articles/{id}/views",
        // 获取作者详细信息
        "/user/{id}/author",
        // 静态资源
        "/images/**",  // 新增静态资源路径
        "/static/**",  // 所有静态资源
        // API文档
        "/swagger-ui.html",
        "/swagger-ui/**",
        "/v3/api-docs",
        "/v3/api-docs/**"
    );

    /**
     * 公开GET请求路径前缀
     */
    public static final List<String> PUBLIC_GET_PATH_PREFIXES = List.of(
        "/archives/",
        "/articles/",
        "/categories/",
        "/rewards/",
        "/search/",
        "/tags/"
    );

    private SecurityConstants() {
        // 私有构造函数防止实例化
    }
} 