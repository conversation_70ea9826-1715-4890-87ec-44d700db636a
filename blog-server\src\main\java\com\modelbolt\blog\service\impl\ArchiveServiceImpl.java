// blog-server/src/main/java/com/modelbolt/blog/service/impl/ArchiveServiceImpl.java
package com.modelbolt.blog.service.impl;

import com.modelbolt.blog.mapper.ArticleMapper;
import com.modelbolt.blog.model.vo.ArchiveVO;
import com.modelbolt.blog.model.vo.ArchiveStatsVO;
import com.modelbolt.blog.service.ArchiveService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.modelbolt.blog.model.dto.tag.TagVO;
import com.modelbolt.blog.model.dto.category.CategoryVO;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.stream.Collectors;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@Service
@RequiredArgsConstructor
public class ArchiveServiceImpl implements ArchiveService {
    private final ArticleMapper articleMapper;
    private final ObjectMapper objectMapper;
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public List<ArchiveVO> getArchives(Integer year, Long categoryId, Long tagId) {
        log.info("Getting archives with year: {}, categoryId: {}, tagId: {}", year, categoryId, tagId);
        
        // 1. 获取年份统计
        List<ArchiveVO> yearStats = articleMapper.selectYearStats(year, categoryId, tagId);
        
        // 2. 遍历每个年份，获取月份统计和文章列表
        for (ArchiveVO yearStat : yearStats) {
            List<Map<String, Object>> monthStats = articleMapper.selectMonthStats(Integer.parseInt(yearStat.getYear()), categoryId, tagId);
            List<ArchiveVO.ArchiveMonthVO> months = new ArrayList<>();
            
            // 3. 遍历每个月份，获取文章列表
            for (Map<String, Object> monthStat : monthStats) {
                Integer monthNum = ((Number) monthStat.get("month")).intValue();
                Integer count = ((Number) monthStat.get("count")).intValue();
                
                List<Map<String, Object>> articles = articleMapper.selectMonthArticles(
                    Integer.parseInt(yearStat.getYear()),
                    monthNum,
                    categoryId,
                    tagId
                );
                
                // 4. 构建月份VO对象
                ArchiveVO.ArchiveMonthVO monthVO = new ArchiveVO.ArchiveMonthVO();
                monthVO.setMonth(String.format("%02d", monthNum));  // 确保月份是两位数
                monthVO.setCount(count);
                
                // 5. 转换文章列表，使用DateTimeFormatter解析日期
                List<ArchiveVO.ArchiveArticleVO> articleVOs = articles.stream()
                    .map(article -> {
                        ArchiveVO.ArchiveArticleVO vo = new ArchiveVO.ArchiveArticleVO();
                        vo.setId(((Number) article.get("id")).longValue());
                        vo.setTitle((String) article.get("title"));
                        
                        // 处理日期转换
                        String createdAt = (String) article.get("createdAt");
                        if (createdAt != null) {
                            vo.setCreatedAt(LocalDateTime.parse(createdAt, DATE_TIME_FORMATTER));
                        }
                        
                        vo.setViewCount(((Number) article.get("viewCount")).intValue());
                        
                        // 处理分类列表
                        Object categoriesObj = article.get("categories");
                        if (categoriesObj != null) {
                            try {
                                if (categoriesObj instanceof String) {
                                    // 如果是字符串，尝试解析 JSON
                                    List<CategoryVO> categories = objectMapper.readValue((String) categoriesObj, new TypeReference<List<CategoryVO>>() {});
                                    vo.setCategories(categories);
                                } else if (categoriesObj instanceof List) {
                                    // 如果已经是列表，直接转换
                                    List<?> categoriesList = (List<?>) categoriesObj;
                                    List<CategoryVO> categories = objectMapper.convertValue(categoriesList, new TypeReference<List<CategoryVO>>() {});
                                    vo.setCategories(categories);
                                } else {
                                    log.warn("Unexpected categories type for article {}: {}", vo.getId(), categoriesObj.getClass());
                                    vo.setCategories(new ArrayList<>());
                                }
                            } catch (Exception e) {
                                log.error("Error processing categories for article {}: {}", vo.getId(), e.getMessage());
                                vo.setCategories(new ArrayList<>());
                            }
                        } else {
                            vo.setCategories(new ArrayList<>());
                        }
                        
                        // 处理标签
                        Object tagsObj = article.get("tags");
                        if (tagsObj != null) {
                            try {
                                if (tagsObj instanceof String) {
                                    // 如果是字符串，尝试解析 JSON
                                    List<TagVO> tags = objectMapper.readValue((String) tagsObj, new TypeReference<List<TagVO>>() {});
                                    vo.setTags(tags);
                                } else if (tagsObj instanceof List) {
                                    // 如果已经是列表，直接转换
                                    List<?> tagsList = (List<?>) tagsObj;
                                    List<TagVO> tags = objectMapper.convertValue(tagsList, new TypeReference<List<TagVO>>() {});
                                    vo.setTags(tags);
                                } else {
                                    log.warn("Unexpected tags type for article {}: {}", vo.getId(), tagsObj.getClass());
                                    vo.setTags(new ArrayList<>());
                                }
                            } catch (Exception e) {
                                log.error("Error processing tags for article {}: {}", vo.getId(), e.getMessage());
                                vo.setTags(new ArrayList<>());
                            }
                        } else {
                            vo.setTags(new ArrayList<>());
                        }
                        
                        return vo;
                    })
                    .collect(Collectors.toList());
                monthVO.setArticles(articleVOs);
                
                months.add(monthVO);
            }
            
            yearStat.setMonths(months);
            log.debug("Archive year: {}, count: {}, months: {}", yearStat.getYear(), yearStat.getCount(), months.size());
        }
        
        log.info("Found {} archive entries", yearStats.size());
        return yearStats;
    }

    @Override
    public ArchiveStatsVO getArchiveStats() {
        log.info("Getting archive statistics");
        ArchiveStatsVO stats = articleMapper.selectArchiveStats();
        log.info("Archive stats - total: {}, yearStats: {}, timeRange: {}", 
            stats != null ? stats.getTotal() : null,
            stats != null ? stats.getYearStats() : null,
            stats != null ? stats.getTimeRange() : null);
        return stats;
    }
}