<template>
  <div class="archive-filter bg-white rounded-lg shadow-sm p-4">
    <!-- 统计信息 -->
    <div class="stats mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-3">归档统计</h3>
      <div class="text-gray-500 space-y-2">
        <p>共计 {{ stats?.total || 0 }} 篇文章</p>
        <p v-if="stats?.timeRange">
          时间跨度：{{ formatTimeRange(stats.timeRange) }}
        </p>
      </div>
    </div>

    <!-- 年份筛选 -->
    <div class="filter-section mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-3">
        年份筛选
        <el-tag v-if="currentYear" size="small" closable @close="handleYearClick('')">
          {{ currentYear }}
        </el-tag>
      </h3>
      <div class="flex flex-wrap gap-2">
        <el-tag
          v-for="yearStat in sortedYearStats"
          :key="yearStat.year"
          :type="currentYear === yearStat.year ? 'primary' : 'info'"
          class="cursor-pointer year-tag"
          @click="handleYearClick(yearStat.year)"
        >
          {{ yearStat.year }}
          <template #suffix>
            <span class="ml-1">({{ yearStat.count }})</span>
          </template>
        </el-tag>
      </div>
    </div>

    <!-- 分类筛选 -->
    <div class="filter-section mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-3">
        分类筛选
        <el-tag v-if="currentCategory" size="small" closable @close="handleCategoryChange(undefined)">
          {{ getCategoryName(currentCategory) }}
        </el-tag>
      </h3>
      <el-select
        v-model="currentCategory"
        placeholder="选择分类"
        clearable
        class="w-full"
        @change="handleCategoryChange"
      >
        <el-option
          v-for="category in categories"
          :key="category.id"
          :label="`${category.name} (${category.articleCount || 0})`"
          :value="category.id"
        />
      </el-select>
    </div>

    <!-- 标签筛选 -->
    <div class="filter-section">
      <h3 class="text-lg font-semibold text-gray-900 mb-3">
        标签筛选
        <el-tag v-if="currentTag" size="small" closable @close="handleTagChange(undefined)">
          {{ getTagName(currentTag) }}
        </el-tag>
      </h3>
      <el-select
        v-model="currentTag"
        placeholder="选择标签"
        clearable
        class="w-full"
        @change="handleTagChange"
      >
        <el-option
          v-for="tag in tags"
          :key="tag.id"
          :label="`${tag.name} (${tag.articleCount || 0})`"
          :value="tag.id"
        />
      </el-select>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { ArchiveStats } from '@/types/archive'
import type { CategoryVO, TagVO } from '@/types/article'
import { useArchiveStore } from '@/stores/archive'

const props = defineProps<{
  stats: ArchiveStats | null
  categories: CategoryVO[]
  tags: TagVO[]
}>()

const archiveStore = useArchiveStore()

// 使用计算属性获取和设置筛选状态
const currentYear = computed({
  get: () => archiveStore.filterState.year?.toString() || '',
  set: (value: string) => {
    archiveStore.updateFilter({ year: value ? parseInt(value) : undefined })
  }
})

const currentCategory = computed({
  get: () => archiveStore.filterState.categoryId,
  set: (value: number | undefined) => {
    archiveStore.updateFilter({ categoryId: value })
  }
})

const currentTag = computed({
  get: () => archiveStore.filterState.tagId,
  set: (value: number | undefined) => {
    archiveStore.updateFilter({ tagId: value })
  }
})

// 按年份降序排序
const sortedYearStats = computed(() => {
  if (!props.stats?.yearStats) return []
  return [...props.stats.yearStats].sort((a, b) => 
    parseInt(b.year) - parseInt(a.year)
  )
})

// 格式化时间范围
const formatTimeRange = (range: { start: string; end: string }) => {
  const start = new Date(range.start).toLocaleDateString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit' 
  })
  const end = new Date(range.end).toLocaleDateString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit' 
  })
  return `${start} ~ ${end}`
}

// 获取分类名称
const getCategoryName = (categoryId: number) => {
  const category = props.categories.find(c => c.id === categoryId)
  return category?.name || ''
}

// 获取标签名称
const getTagName = (tagId: number) => {
  const tag = props.tags.find(t => t.id === tagId)
  return tag?.name || ''
}

// 处理年份点击
const handleYearClick = (year: string) => {
  currentYear.value = year
}

// 处理分类变化
const handleCategoryChange = (categoryId: number | undefined) => {
  currentCategory.value = categoryId
}

// 处理标签变化
const handleTagChange = (tagId: number | undefined) => {
  currentTag.value = tagId
}
</script>

<style scoped>
.year-tag {
  @apply transition-all duration-300;
}

.year-tag:hover {
  @apply transform scale-105;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-tag--small) {
  @apply ml-2;
}
</style> 