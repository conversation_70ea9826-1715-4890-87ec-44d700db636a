package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.comment.CommentVO;
import com.modelbolt.blog.model.dto.comment.CommentForm;
import com.modelbolt.blog.service.CommentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@Slf4j
@Tag(name = "评论接口", description = "评论相关接口")
@RestController
@RequestMapping("/comments")
@RequiredArgsConstructor
public class CommentController {

    private final CommentService commentService;

    @Operation(summary = "发表评论")
    @PostMapping
    public ApiResponse<CommentVO> submitComment(
            @Valid @RequestBody CommentForm form,
            Authentication authentication) {
        log.debug("User {} submitting comment for article {}", authentication.getName(), form.getArticleId());
        return ApiResponse.success(commentService.submitComment(form));
    }

    @Operation(summary = "回复评论")
    @PostMapping("/reply")
    public ApiResponse<CommentVO> replyComment(
            @Valid @RequestBody CommentForm form,
            Authentication authentication) {
        log.debug("User {} replying to comment {}", authentication.getName(), form.getParentId());
        return ApiResponse.success(commentService.replyComment(form));
    }

    @Operation(summary = "删除评论")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteComment(
            @Parameter(description = "评论ID") @PathVariable Long id,
            Authentication authentication) {
        log.debug("User {} deleting comment {}", authentication.getName(), id);
        commentService.deleteComment(id);
        return ApiResponse.success();
    }

    @Operation(summary = "点赞评论")
    @PostMapping("/{id}/like")
    public ApiResponse<Void> likeComment(
            @Parameter(description = "评论ID") @PathVariable Long id,
            Authentication authentication) {
        log.debug("User {} liking comment {}", authentication.getName(), id);
        commentService.likeComment(id);
        return ApiResponse.success();
    }

    @Operation(summary = "取消点赞评论")
    @DeleteMapping("/{id}/like")
    public ApiResponse<Void> unlikeComment(
            @Parameter(description = "评论ID") @PathVariable Long id,
            Authentication authentication) {
        log.debug("User {} unliking comment {}", authentication.getName(), id);
        commentService.unlikeComment(id);
        return ApiResponse.success();
    }

    @Operation(summary = "获取评论回复列表")
    @GetMapping("/{id}/replies")
    public ApiResponse<PageResult<CommentVO>> getReplies(
            @Parameter(description = "评论ID") @PathVariable Long id,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer size) {
        log.debug("Getting replies for comment {}, page: {}, size: {}", id, page, size);
        return ApiResponse.success(commentService.getReplies(id, page, size));
    }
} 