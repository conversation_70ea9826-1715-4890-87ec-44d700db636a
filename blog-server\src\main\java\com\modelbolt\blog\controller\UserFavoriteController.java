package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.favorite.UserFavoriteVO;
import com.modelbolt.blog.service.UserFavoriteService;
import com.modelbolt.blog.service.ArticleService;
import com.modelbolt.blog.utils.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Tag(name = "用户收藏管理", description = "用户收藏管理相关接口")
@RestController
@RequestMapping("/user/favorites")
@RequiredArgsConstructor
@SecurityRequirement(name = "Bearer")
public class UserFavoriteController {

    private final UserFavoriteService userFavoriteService;
    private final ArticleService articleService;

    @Operation(summary = "获取用户收藏列表")
    @GetMapping
    public ApiResponse<PageResult<UserFavoriteVO>> getUserFavorites(
            @Parameter(description = "关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer size) {
        Long userId = SecurityUtils.getCurrentUserId();
        return ApiResponse.success(userFavoriteService.getUserFavorites(userId, keyword, page, size));
    }

    @Operation(summary = "取消收藏")
    @DeleteMapping("/{articleId}")
    public ApiResponse<Void> cancelFavorite(@Parameter(description = "文章ID") @PathVariable Long articleId) {
        articleService.unfavoriteArticle(articleId);
        return ApiResponse.success();
    }
} 