@use "sass:color";
@use "variables" as *;

// 定义全局 CSS 变量
:root {
  --primary-color: #{$primary-color};
  --primary-color-hover: #{$primary-hover};
  --el-color-primary: #{$primary-color};
  --el-color-primary-light-3: #{color.adjust($primary-color, $lightness: 5%)};
  --el-color-primary-light-5: #{color.adjust($primary-color, $lightness: 10%)};
  --el-color-primary-light-7: #{color.adjust($primary-color, $lightness: 20%)};
  --el-color-primary-light-8: #{color.adjust($primary-color, $lightness: 30%)};
  --el-color-primary-light-9: #{color.adjust($primary-color, $lightness: 40%)};
  --el-color-primary-dark-2: #{color.adjust($primary-color, $lightness: -10%)};
  --el-button-disabled-bg-color: #f5f7fa;
  --el-button-disabled-text-color: #c0c4cc;
}

// 重置样式
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

// 基础样式
html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.5;
  color: $text-primary;
  background-color: $bg-light;
}

// 链接样式
a {
  color: inherit;
  text-decoration: none;
  
  &:hover {
    color: $primary-color;
  }
}

// 按钮重置 (仅针对普通按钮，不影响Element Plus按钮)
button:not(.el-button) {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  font: inherit;
  color: inherit;
}

// 图片样式
img {
  max-width: 100%;
  height: auto;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: $bg-light;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: $text-light;
  border-radius: 3px;
  
  &:hover {
    background: $text-secondary;
  }
}

// Element Plus 组件样式覆盖
.el-button {
  &.el-button--primary {
    --el-button-bg-color: var(--el-color-primary);
    --el-button-border-color: var(--el-color-primary);
    --el-button-hover-bg-color: var(--el-color-primary-light-3);
    --el-button-hover-border-color: var(--el-color-primary-light-3);
    --el-button-active-bg-color: var(--el-color-primary-dark-2);
    --el-button-active-border-color: var(--el-color-primary-dark-2);
    --el-button-text-color: #ffffff;
    --el-button-disabled-bg-color: var(--el-color-primary-light-8);
    --el-button-disabled-border-color: var(--el-color-primary-light-8);
  }

  &.el-button--default {
    --el-button-bg-color: #ffffff;
    --el-button-border-color: var(--el-border-color);
    --el-button-hover-bg-color: var(--el-color-primary-light-9);
    --el-button-hover-border-color: var(--el-color-primary-light-7);
    --el-button-active-bg-color: var(--el-color-primary-light-9);
    --el-button-active-border-color: var(--el-color-primary);
    --el-button-text-color: var(--el-text-color-regular);
  }
}

.el-input {
  --el-input-border-color: #{$border-color};
  --el-input-hover-border-color: #{$text-secondary};
  --el-input-focus-border-color: #{$primary-color};
}

.el-form-item {
  --el-form-error-color: #ef4444;
}

.el-pagination {
  --el-pagination-hover-color: #{$primary-color};
  --el-pagination-button-color: #{$text-secondary};
  --el-pagination-hover-bg-color: #{$bg-light};
}

.el-dropdown-menu {
  --el-dropdown-menuItem-hover-fill: #{$bg-light};
  --el-dropdown-menuItem-hover-color: #{$primary-color};
}

// 容器类
.container {
  width: 100%;
  max-width: $container-max-width;
  margin: 0 auto;
  padding: 0 $spacing-4;
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 响应式工具类
@media (max-width: $screen-md) {
  .md\:hidden {
    display: none;
  }
}

@media (min-width: $screen-md) {
  .md\:block {
    display: block;
  }
} 