package com.modelbolt.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.comment.CommentAuditDTO;
import com.modelbolt.blog.model.dto.comment.CommentQuery;
import com.modelbolt.blog.model.dto.comment.CommentVO;
import com.modelbolt.blog.model.dto.comment.CommentForm;
import com.modelbolt.blog.model.dto.comment.UserCommentVO;
import com.modelbolt.blog.model.entity.Comment;

import java.util.List;

public interface CommentService extends IService<Comment> {
    /**
     * 发表评论
     *
     * @param form 评论表单
     * @return 评论视图对象
     */
    CommentVO submitComment(CommentForm form);

    /**
     * 回复评论
     *
     * @param form 评论表单
     * @return 评论视图对象
     */
    CommentVO replyComment(CommentForm form);

    /**
     * 删除评论
     *
     * @param id 评论ID
     */
    void deleteComment(Long id);

    /**
     * 点赞评论
     *
     * @param id 评论ID
     */
    void likeComment(Long id);

    /**
     * 取消点赞评论
     *
     * @param id 评论ID
     */
    void unlikeComment(Long id);

    /**
     * 获取评论回复列表
     *
     * @param id 评论ID
     * @param page 页码
     * @param size 每页数量
     * @return 评论分页结果
     */
    PageResult<CommentVO> getReplies(Long id, Integer page, Integer size);

    /**
     * 获取用户评论列表
     *
     * @param userId 用户ID
     * @param keyword 关键词
     * @param status 状态
     * @param page 页码
     * @param size 每页数量
     * @return 评论分页结果
     */
    PageResult<UserCommentVO> getUserComments(Long userId, String keyword, String status, Integer page, Integer size);

    /**
     * 删除用户评论
     *
     * @param userId 用户ID
     * @param commentId 评论ID
     */
    void deleteUserComment(Long userId, Long commentId);

    /**
     * 分页查询评论列表
     */
    PageResult<CommentVO> getCommentList(CommentQuery query);

    /**
     * 审核评论
     */
    void auditComment(Long id, CommentAuditDTO dto);

    /**
     * 批量删除评论
     */
    void batchDeleteComments(List<Long> ids);
} 