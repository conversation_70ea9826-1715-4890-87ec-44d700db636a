package com.modelbolt.blog.task;

import com.modelbolt.blog.model.entity.UserSession;
import com.modelbolt.blog.service.SessionService;
import com.modelbolt.blog.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Set;

@Slf4j
@Component
public class SessionCleanupTask {

    private static final String SESSION_PREFIX = "session:";
    private final SessionService sessionService;
    private final RedisUtils redisUtils;

    public SessionCleanupTask(SessionService sessionService, RedisUtils redisUtils) {
        this.sessionService = sessionService;
        this.redisUtils = redisUtils;
    }

    /**
     * 清理过期会话任务，每5分钟执行一次
     */
    @Scheduled(fixedDelay = 300000) // 每5分钟执行一次
    public void cleanupExpiredSessions() {
        try {
            log.debug("Starting cleanup of expired sessions");
            String sessionPattern = SESSION_PREFIX + "*";
            Set<String> sessionKeys = redisUtils.keys(sessionPattern);
            int cleanedCount = 0;

            for (String sessionKey : sessionKeys) {
                UserSession session = (UserSession) redisUtils.get(sessionKey);
                if (session != null && session.getExpirationTime().isBefore(LocalDateTime.now())) {
                    sessionService.removeSession(sessionKey.substring(SESSION_PREFIX.length()));
                    cleanedCount++;
                }
            }

            if (cleanedCount > 0) {
                log.info("Cleaned up {} expired sessions", cleanedCount);
            } else {
                log.debug("No expired sessions found");
            }
        } catch (Exception e) {
            log.error("Failed to cleanup expired sessions", e);
        }
    }
}
