package com.modelbolt.blog.model.dto.user;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "管理后台用户视图对象")
public class AdminUserVO {
    @ExcelProperty("用户ID")
    @Schema(description = "用户ID")
    private Long id;

    @ExcelProperty("用户名")
    @Schema(description = "用户名")
    private String username;

    @ExcelProperty("邮箱")
    @Schema(description = "邮箱")
    private String email;

    @ExcelProperty("头像")
    @Schema(description = "头像")
    private String avatar;

    @ExcelProperty("角色")
    @Schema(description = "角色")
    private String role;

    @ExcelProperty("状态")
    @Schema(description = "状态")
    private Integer status;

    @ExcelProperty("创建时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @ExcelProperty("更新时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
} 