package com.modelbolt.blog.service;

import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.article.ArticleVO;
import com.modelbolt.blog.model.dto.tag.TagVO;
import com.modelbolt.blog.model.dto.tag.CreateTagDTO;
import java.util.List;

public interface TagService {
    /**
     * 获取热门标签
     *
     * @param limit 获取数量
     */
    List<TagVO> getHotTags(Integer limit);

    /**
     * 获取所有标签
     * @return 标签列表
     */
    List<TagVO> getAllTags();

    /**
     * 获取标签详情
     * @param id 标签ID
     * @return 标签详情
     */
    TagVO getTagById(Long id);

    /**
     * 获取标签下的文章列表
     * @param tagId 标签ID
     * @param page 页码
     * @param size 每页大小
     * @return 文章列表分页数据
     */
    PageResult<ArticleVO> getArticlesByTagId(Long tagId, Integer page, Integer size);

    /**
     * 获取文章的标签列表
     * @param articleId 文章ID
     * @return 标签列表
     */
    List<TagVO> getTagsByArticleId(Long articleId);

    /**
     * 创建标签
     * @param dto 创建标签请求
     * @return 创建的标签
     */
    TagVO createTag(CreateTagDTO dto);

    /**
     * 更新标签
     * @param id 标签ID
     * @param dto 更新标签请求
     * @return 更新后的标签
     */
    TagVO updateTag(Long id, CreateTagDTO dto);

    /**
     * 删除标签
     * @param id 标签ID
     */
    void deleteTag(Long id);
} 