package com.modelbolt.blog.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * AI对话历史实体
 */
@Data
@Accessors(chain = true)
@TableName("ai_chat_history")
@Schema(description = "AI对话历史实体")
public class AiChatHistory {
    @TableId(type = IdType.AUTO)
    @Schema(description = "ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "会话ID")
    private String sessionId;

    @Schema(description = "角色:user,assistant")
    private String role;

    @Schema(description = "对话内容")
    private String content;

    @Schema(description = "token数量")
    private Integer tokens;

    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
} 