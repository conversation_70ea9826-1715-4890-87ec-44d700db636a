<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

interface DataItem {
  type: string
  count: number
}

const props = defineProps<{
  loading?: boolean
  data: DataItem[]
  title?: string
}>()

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

const initChart = () => {
  if (!chartRef.value) return
  
  chart = echarts.init(chartRef.value)
  updateChart()
}

const updateChart = () => {
  if (!chart) return

  console.log('Updating pie chart with data:', props.data)

  const option = {
    title: {
      text: props.title,
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle'
    },
    series: [
      {
        name: props.title || '分类统计',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '20',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: Array.isArray(props.data) ? props.data.map(item => {
          console.log('Processing pie chart item:', item)
          return {
            name: item.type || '未分类',
            value: item.count || 0
          }
        }) : []
      }
    ]
  }

  console.log('Setting pie chart option:', option)
  chart.setOption(option, true) // 使用 true 参数来完全刷新图表
}

// 监听数据变化
watch(
  () => props.data,
  (newData) => {
    console.log('Pie chart data changed:', newData)
    if (!props.loading && chart) {
      updateChart()
    }
  },
  { deep: true, immediate: true }
)

// 监听加载状态变化
watch(
  () => props.loading,
  (isLoading) => {
    console.log('Pie chart loading state changed:', isLoading)
    if (!isLoading && chart) {
      updateChart()
    }
  }
)

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
  window.removeEventListener('resize', handleResize)
})

const handleResize = () => {
  if (chart) {
    chart.resize()
  }
}
</script>

<template>
  <div ref="chartRef" class="w-full h-full min-h-[300px]"></div>
</template> 