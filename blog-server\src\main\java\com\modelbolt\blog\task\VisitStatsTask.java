package com.modelbolt.blog.task;

import com.modelbolt.blog.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

@Slf4j
@Component
@EnableScheduling
public class VisitStatsTask {
    
    private static final String TODAY_VISITS_KEY = "stats:today_visits";
    
    @Autowired
    private RedisUtils redisUtils;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    /**
     * 每天零点执行
     * 保存昨天的访问记录并重置今天的计数器
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void resetTodayVisits() {
        try {
            // 获取昨天的访问量
            Object count = redisUtils.get(TODAY_VISITS_KEY);
            int visitCount = count == null ? 0 : Integer.parseInt(count.toString());
            LocalDate yesterday = LocalDate.now().minusDays(1);
            
            // 保存到历史记录表
            jdbcTemplate.update(
                "INSERT INTO visit_history (visit_date, visit_count) VALUES (?, ?) " +
                "ON DUPLICATE KEY UPDATE visit_count = ?",
                yesterday, visitCount, visitCount
            );
            
            log.info("已保存{}的访问记录：{}次访问", yesterday, visitCount);
            
            // 重置今天的计数器
            redisUtils.delete(TODAY_VISITS_KEY);
            log.info("已重置访问计数器");
        } catch (Exception e) {
            log.error("保存访问记录或重置计数器时发生错误", e);
        }
    }
} 