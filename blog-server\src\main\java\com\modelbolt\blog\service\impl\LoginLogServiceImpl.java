package com.modelbolt.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.modelbolt.blog.model.entity.LoginLog;
import com.modelbolt.blog.mapper.LoginLogMapper;
import com.modelbolt.blog.service.LoginLogService;
import com.modelbolt.blog.utils.UserAgentUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class LoginLogServiceImpl extends ServiceImpl<LoginLogMapper, LoginLog> implements LoginLogService {

    @Async
    @Override
    public void recordLoginLog(Long userId, String username, Integer status, String msg, HttpServletRequest request) {
        LoginLog loginLog = new LoginLog();
        loginLog.setUserId(userId);
        loginLog.setUsername(username);
        loginLog.setStatus(status);
        loginLog.setMsg(msg);
        
        // 获取IP地址
        String ip = getIpAddress(request);
        loginLog.setIpAddress(ip);
        
        // 获取浏览器和操作系统信息
        String userAgent = request.getHeader("User-Agent");
        loginLog.setBrowser(UserAgentUtil.getBrowser(userAgent));
        loginLog.setOs(UserAgentUtil.getOs(userAgent));
        
        // 设置登录时间
        loginLog.setLoginTime(LocalDateTime.now());
        
        // 异步保存日志
        save(loginLog);
    }

    @Override
    public int cleanLoginLog(int days) {
        LocalDateTime deadline = LocalDateTime.now().minusDays(days);
        LambdaQueryWrapper<LoginLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(LoginLog::getLoginTime, deadline);
        return baseMapper.delete(wrapper);
    }

    private String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
} 