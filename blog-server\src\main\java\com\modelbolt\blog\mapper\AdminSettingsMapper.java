package com.modelbolt.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.modelbolt.blog.model.entity.AdminSettings;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 管理员设置 Mapper 接口
 */
@Mapper
public interface AdminSettingsMapper extends BaseMapper<AdminSettings> {
    
    @Select("SELECT * FROM admin_settings WHERE admin_id = #{adminId}")
    AdminSettings selectByAdminId(Long adminId);
} 