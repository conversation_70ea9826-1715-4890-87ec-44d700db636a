<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { User, Message, Lock, Key } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

interface RegisterForm {
  username: string
  email: string
  verificationCode: string
  password: string
  confirmPassword: string
  agreeTerms: boolean
}

const registerForm = reactive<RegisterForm>({
  username: '',
  email: '',
  verificationCode: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false
})

const registerFormRef = ref<FormInstance>()
const loading = ref(false)
const sendingCode = ref(false)
const countdown = ref(0)

const validatePass = (rule: any, value: string, callback: Function) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== registerForm.password) {
    callback(new Error('两次输入密码不一致!'))
  } else {
    callback()
  }
}

const validateEmail = (rule: any, value: string, callback: Function) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (value === '') {
    callback(new Error('请输入邮箱地址'))
  } else if (!emailRegex.test(value)) {
    callback(new Error('请输入正确的邮箱地址'))
  } else {
    callback()
  }
}

const rules = reactive<FormRules>({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, trigger: 'blur', validator: validateEmail }
  ],
  verificationCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码长度为6位', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, trigger: 'blur', validator: validatePass }
  ],
  agreeTerms: [
    { type: 'enum', enum: [true], message: '请同意服务条款', trigger: 'change' }
  ]
})

const startCountdown = () => {
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

const handleSendCode = async () => {
  try {
    await registerFormRef.value?.validateField('email')
    sendingCode.value = true
    await userStore.sendVerificationCode(registerForm.email)
    ElMessage.success('验证码已发送，请查收邮件')
    startCountdown()
  } catch (error: any) {
    ElMessage.error(error.message || '发送验证码失败')
  } finally {
    sendingCode.value = false
  }
}

const handleRegister = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  
  await formEl.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const success = await userStore.register({
          username: registerForm.username,
          email: registerForm.email,
          password: registerForm.password,
          verificationCode: registerForm.verificationCode,
          agreeTerms: registerForm.agreeTerms
        })
        if (success) {
          ElMessage.success('注册成功')
          router.push('/home')
        }
      } catch (error: any) {
        ElMessage.error(error.message || '注册失败')
      } finally {
        loading.value = false
      }
    }
  })
}

const goToLogin = () => {
  router.push('/auth/login')
}
</script>

<template>
  <div class="auth-page">
    <div class="auth-container">
      <div class="auth-card">
        <div class="auth-header">
          <img src="@/assets/logo.png" alt="Logo" class="auth-logo">
          <h2 class="auth-title">注册账号</h2>
          <p class="auth-subtitle">创建一个新账号开始使用</p>
        </div>

        <el-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="rules"
          class="auth-form"
        >
          <el-form-item prop="username">
            <el-input
              v-model="registerForm.username"
              placeholder="用户名"
              :prefix-icon="User"
              class="auth-input"
            />
          </el-form-item>

          <el-form-item prop="email">
            <el-input
              v-model="registerForm.email"
              placeholder="邮箱"
              :prefix-icon="Message"
              class="auth-input"
            >
              <template #append>
                <el-button
                  :loading="sendingCode"
                  :disabled="countdown > 0"
                  @click="handleSendCode"
                  class="verify-button"
                >
                  {{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
                </el-button>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="verificationCode">
            <el-input
              v-model="registerForm.verificationCode"
              placeholder="验证码"
              :prefix-icon="Key"
              class="auth-input"
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              placeholder="密码"
              :prefix-icon="Lock"
              class="auth-input"
            />
          </el-form-item>

          <el-form-item prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="确认密码"
              :prefix-icon="Lock"
              class="auth-input"
            />
          </el-form-item>

          <el-form-item prop="agreeTerms" class="agreement-item">
            <el-checkbox v-model="registerForm.agreeTerms">
              我已阅读并同意
              <a href="#" class="agreement-link">服务条款</a>
              和
              <a href="#" class="agreement-link">隐私政策</a>
            </el-checkbox>
          </el-form-item>

          <el-button
            type="primary"
            class="register-button"
            :loading="loading"
            @click="handleRegister(registerFormRef)"
          >
            注册
          </el-button>

          <div class="login-link">
            <span>已有账号？</span>
            <router-link to="/auth/login">
              立即登录
            </router-link>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f6f8ff 0%, #f0f4ff 100%);
  padding: 20px;
}

.auth-container {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
}

.auth-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  padding: 32px;
}

.auth-header {
  text-align: center;
  margin-bottom: 32px;
}

.auth-logo {
  width: 64px;
  height: 64px;
  margin-bottom: 24px;
}

.auth-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.auth-subtitle {
  font-size: 14px;
  color: #666;
}

.auth-form {
  .el-form-item {
    margin-bottom: 24px;
  }
}

.auth-input {
  :deep(.el-input__wrapper) {
    border-radius: 8px;
    height: 44px;
    
    &.is-focus {
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }
  }
}

.verification-item {
  display: flex;
  gap: 12px;

  .verification-input {
    flex: 1;
  }

  .verification-button {
    width: 120px;
    height: 44px;
    border-radius: 8px;
    font-size: 14px;
  }
}

.agreement-item {
  margin-bottom: 24px;
  
  :deep(.el-checkbox__label) {
    color: #666;
    font-size: 14px;
  }
}

.agreement-link {
  color: var(--primary-color);
  text-decoration: none;
  
  &:hover {
    color: var(--primary-color-hover);
  }
}

.register-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  background: var(--primary-color);
  border: none;
  border-radius: 8px;
  margin-bottom: 24px;
  
  &:hover {
    background: var(--primary-color-hover);
    transform: translateY(-1px);
  }
}

.login-link {
  text-align: center;
  font-size: 14px;
  
  span {
    color: #666;
  }
  
  a {
    color: var(--primary-color);
    text-decoration: none;
    margin-left: 4px;
    
    &:hover {
      color: var(--primary-color-hover);
    }
  }
}

.verify-button {
  margin: -1px;
  height: 44px;
  padding: 0 15px;
  border: none;
  background: var(--primary-color);
  color: white;
  font-size: 14px;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  white-space: nowrap;
  
  &:hover:not(:disabled) {
    background: var(--primary-color-hover);
  }
  
  &:disabled {
    background: var(--el-button-disabled-bg-color);
    color: var(--el-button-disabled-text-color);
    cursor: not-allowed;
  }
}
</style> 