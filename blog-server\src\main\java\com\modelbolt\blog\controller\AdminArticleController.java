package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.article.ArticleVO;
import com.modelbolt.blog.model.dto.article.CreateArticleDTO;
import com.modelbolt.blog.model.dto.article.UpdateArticleDTO;
import com.modelbolt.blog.service.ArticleService;
import com.modelbolt.blog.service.FileService;
import com.modelbolt.blog.annotation.OperationLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/admin/article")
@RequiredArgsConstructor
@Tag(name = "管理后台-文章管理", description = "管理后台文章管理相关接口")
@PreAuthorize("hasRole('ADMIN')")
public class AdminArticleController {

    private final ArticleService articleService;
    private final FileService fileService;

    @Operation(summary = "获取文章列表")
    @GetMapping("/list")
    public ApiResponse<PageResult<ArticleVO>> getArticleList(
            @Parameter(description = "关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "状态：0-草稿，1-已发布，2-已下架") @RequestParam(required = false) Integer status,
            @Parameter(description = "分类ID") @RequestParam(required = false) Long categoryId,
            @Parameter(description = "标签ID") @RequestParam(required = false) Long tagId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer size) {
        return ApiResponse.success(articleService.getAdminArticleList(keyword, status, categoryId, tagId, current, size));
    }

    @Operation(summary = "获取文章详情")
    @GetMapping("/{id}")
    public ApiResponse<ArticleVO> getArticleDetail(@PathVariable Long id) {
        return ApiResponse.success(articleService.getArticleDetail(id));
    }

    @Operation(summary = "获取文章编辑信息")
    @GetMapping("/{id}/edit")
    public ApiResponse<ArticleVO> getArticleForEdit(@PathVariable Long id) {
        return ApiResponse.success(articleService.getArticleForEdit(id));
    }

    @Operation(summary = "创建文章")
    @PostMapping
    @OperationLog("创建文章")
    public ApiResponse<ArticleVO> createArticle(@RequestBody @Valid CreateArticleDTO dto) {
        Long articleId = articleService.createArticle(dto);
        return ApiResponse.success(articleService.getArticleDetail(articleId));
    }

    @Operation(summary = "更新文章")
    @PutMapping("/{id}")
    @OperationLog("更新文章")
    public ApiResponse<ArticleVO> updateArticle(
            @PathVariable Long id,
            @RequestBody @Valid UpdateArticleDTO dto) {
        articleService.updateArticle(id, dto);
        return ApiResponse.success(articleService.getArticleDetail(id));
    }

    @Operation(summary = "删除文章")
    @DeleteMapping("/{id}")
    @OperationLog("删除文章")
    public ApiResponse<Void> deleteArticle(@PathVariable Long id) {
        articleService.deleteArticle(id);
        return ApiResponse.success();
    }

    @Operation(summary = "更新文章状态")
    @PutMapping("/{id}/status")
    @OperationLog("更新文章状态")
    public ApiResponse<Void> updateArticleStatus(
            @PathVariable Long id,
            @RequestParam Integer status) {
        articleService.updateArticleStatus(id, status);
        return ApiResponse.success();
    }

    @Operation(summary = "更新文章置顶状态")
    @PutMapping("/{id}/top")
    @OperationLog("更新文章置顶状态")
    public ApiResponse<Void> updateArticleTop(
            @PathVariable Long id,
            @RequestParam Boolean isTop) {
        articleService.updateArticleTop(id, isTop);
        return ApiResponse.success();
    }

    @Operation(summary = "上传文章封面")
    @PostMapping("/cover")
    @OperationLog("上传文章封面")
    public ApiResponse<String> uploadArticleCover(@RequestParam("cover") MultipartFile file) {
        log.debug("Uploading article cover");
        String coverUrl = fileService.uploadImage(file, "article/cover");
        return ApiResponse.success(coverUrl);
    }
} 