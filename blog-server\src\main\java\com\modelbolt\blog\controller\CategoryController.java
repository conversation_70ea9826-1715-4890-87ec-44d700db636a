package com.modelbolt.blog.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.model.dto.article.ArticleVO;
import com.modelbolt.blog.model.dto.category.CategoryVO;
import com.modelbolt.blog.service.CategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "分类接口", description = "分类相关接口")
@RestController
@RequestMapping("/categories")
@RequiredArgsConstructor
public class CategoryController {

    private final CategoryService categoryService;

    @Operation(summary = "获取分类列表")
    @GetMapping
    public ApiResponse<List<CategoryVO>> getCategories() {
        return ApiResponse.success(categoryService.getCategories());
    }

    @Operation(summary = "获取分类详情")
    @GetMapping("/{id}")
    public ApiResponse<CategoryVO> getCategoryById(
        @Parameter(description = "分类ID") @PathVariable Long id
    ) {
        return ApiResponse.success(categoryService.getCategoryById(id));
    }

    @Operation(summary = "获取分类下的文章列表")
    @GetMapping("/{id}/articles")
    public ApiResponse<IPage<ArticleVO>> getArticlesByCategoryId(
        @Parameter(description = "分类ID") @PathVariable Long id,
        @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
        @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size
    ) {
        return ApiResponse.success(categoryService.getArticlesByCategoryId(id, page, size));
    }

    @Operation(summary = "获取热门分类")
    @GetMapping("/hot")
    public ApiResponse<List<CategoryVO>> getHotCategories(
        @Parameter(description = "获取数量") @RequestParam(defaultValue = "10") Integer limit
    ) {
        return ApiResponse.success(categoryService.getHotCategories(limit));
    }
} 