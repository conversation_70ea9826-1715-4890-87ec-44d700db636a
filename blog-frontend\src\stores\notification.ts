import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useUserStore } from './user'
import { markNotificationRead, markAllNotificationsRead, getUnreadCount } from '@/api/userCenter'
import { ElMessage } from 'element-plus'
import type { R } from '@/types/common'
import { useSettingsStore } from '@/stores/settings'

// 开发环境下的调试日志
const DEBUG = import.meta.env.DEV
function log(...args: any[]) {
  if (DEBUG) {
    console.log('[NotificationStore]', ...args)
  }
}

function error(...args: any[]) {
  if (DEBUG) {
    console.error('[NotificationStore]', ...args)
  }
}

export const useNotificationStore = defineStore('notification', () => {
  const userStore = useUserStore()
  const settingsStore = useSettingsStore()
  
  // 状态定义
  const notifications = ref<any[]>([]) // 通知列表
  const unreadCount = ref(0) // 未读消息数量
  const webSocket = ref<WebSocket | null>(null) // WebSocket连接实例
  const isConnected = ref(false) // WebSocket连接状态
  const reconnectAttempts = ref(0) // 重连尝试次数
  const maxReconnectAttempts = 5 // 最大重连次数

  // 计算属性：是否有未读消息
  const hasUnread = computed(() => unreadCount.value > 0)

  // 刷新未读消息数量
  async function refreshUnreadCount() {
    try {
      log('正在获取未读消息数量...')
      const response = await getUnreadCount()
      log('获取未读消息数量响应:', response)
      
      if (typeof response.data === 'number') {
        unreadCount.value = response.data
        log('未读消息数量更新为:', unreadCount.value)
      } else {
        error('获取未读消息数量响应格式错误:', response)
      }
    } catch (err: any) {
      error('获取未读消息数量出错:', err)
    }
  }

  // 初始化WebSocket连接
  function initWebSocket() {
    if (!settingsStore.settings.notificationEnabled) {
      log('通知功能已关闭，不初始化WebSocket')
      return
    }

    if (!userStore.token) {
      log('初始化WebSocket失败: 未找到用户token')
      return
    }

    if (webSocket.value?.readyState === WebSocket.OPEN) {
      log('WebSocket已连接，无需重新初始化')
      return
    }

    // 构建WebSocket URL，使用相对路径以便使用Vite的代理
    const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/api/ws/notifications?token=${userStore.token}`
    log('正在连接WebSocket:', wsUrl)
    
    try {
      webSocket.value = new WebSocket(wsUrl)
      log('WebSocket实例创建成功')

      webSocket.value.onopen = () => {
        log('WebSocket连接成功')
        isConnected.value = true
        reconnectAttempts.value = 0
        
        // 连接成功后，立即获取未读消息数量
        refreshUnreadCount().catch(err => {
          error('连接后获取未读消息数量失败:', err)
        })
      }

      webSocket.value.onmessage = (event) => {
        log('收到原始WebSocket消息:', event.data)
        try {
          const data = JSON.parse(event.data)
          log('解析后的WebSocket消息:', data)
          handleWebSocketMessage(data)
        } catch (err) {
          error('解析WebSocket消息失败:', err)
        }
      }

      webSocket.value.onclose = (event: CloseEvent) => {
        log('WebSocket连接断开', {
          code: event.code,
          reason: event.reason,
          wasClean: event.wasClean
        })
        isConnected.value = false
        handleReconnect()
      }

      webSocket.value.onerror = (event: Event) => {
        error('WebSocket连接错误:', {
          event,
          readyState: webSocket.value?.readyState,
          url: wsUrl
        })
        isConnected.value = false
      }
    } catch (err) {
      error('创建WebSocket实例失败:', err)
      isConnected.value = false
    }
  }

  // 处理WebSocket消息
  function handleWebSocketMessage(data: any) {
    log('处理WebSocket消息:', data)
    switch (data.type) {
      case 'unread_count':
        log('更新未读消息数量:', data.count)
        unreadCount.value = data.count
        break
      case 'notification':
        log('收到新通知:', data.notification)
        notifications.value.unshift(data.notification)
        unreadCount.value++
        // 显示通知提醒
        ElMessage({
          message: data.notification.content,
          type: 'info',
          duration: 3000
        })
        break
      default:
        error('未知的通知类型:', data.type)
    }
  }

  // 处理WebSocket重连
  function handleReconnect() {
    if (!settingsStore.settings.notificationEnabled) {
      log('通知功能已关闭，取消重连')
      return
    }

    if (reconnectAttempts.value >= maxReconnectAttempts) {
      error('达到最大重连次数:', maxReconnectAttempts)
      return
    }

    const timeout = Math.min(1000 * Math.pow(2, reconnectAttempts.value), 10000)
    reconnectAttempts.value++

    log(`计划重连 (${reconnectAttempts.value}/${maxReconnectAttempts}), 等待时间: ${timeout}ms`)
    setTimeout(() => {
      if (settingsStore.settings.notificationEnabled) {
        log(`开始第 ${reconnectAttempts.value} 次重连尝试`)
        initWebSocket()
      } else {
        log('通知功能已关闭，取消重连')
      }
    }, timeout)
  }

  // 关闭WebSocket连接
  function closeWebSocket() {
    if (webSocket.value) {
      log('正在关闭WebSocket连接')
      try {
        webSocket.value.close()
      } catch (err) {
        error('关闭WebSocket连接时出错:', err)
      } finally {
        webSocket.value = null
        isConnected.value = false
      }
    }
  }

  // 标记单条消息为已读
  async function markAsRead(notificationId: number) {
    try {
      log('正在标记消息已读:', notificationId)
      const { data } = await markNotificationRead(notificationId)
      log('标记已读响应:', data)
      
      if (data.code === 200) {
        // 更新本地状态
        const notification = notifications.value.find(n => n.id === notificationId)
        if (notification && !notification.isRead) {
          notification.isRead = true
          unreadCount.value = Math.max(0, unreadCount.value - 1)
          log('消息已标记为已读, 当前未读数量:', unreadCount.value)
        }
        ElMessage.success('已标记为已读')
      } else {
        error('标记已读失败:', data)
        ElMessage.error('标记已读失败，请重试')
      }
    } catch (err: any) {
      error('标记已读请求失败:', err)
      ElMessage.error('标记已读失败，请重试')
    }
  }

  // 标记所有消息为已读
  async function markAllAsRead() {
    try {
      log('正在标记所有消息为已读')
      const { data } = await markAllNotificationsRead()
      log('标记全部已读响应:', data)
      
      if (data.code === 200) {
        // 更新本地状态
        notifications.value.forEach(notification => {
          notification.isRead = true
        })
        unreadCount.value = 0
        log('所有消息已标记为已读')
        ElMessage.success('已全部标记为已读')
      } else {
        error('标记全部已读失败:', data)
        ElMessage.error('操作失败，请重试')
      }
    } catch (err: any) {
      error('标记全部已读请求失败:', err)
      ElMessage.error('操作失败，请重试')
    }
  }

  return {
    notifications,
    unreadCount,
    isConnected,
    hasUnread,
    initWebSocket,
    closeWebSocket,
    markAsRead,
    markAllAsRead,
    refreshUnreadCount
  }
}) 