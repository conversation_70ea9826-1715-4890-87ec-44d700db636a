package com.modelbolt.blog.exception.business;

public class AuthException extends BusinessException {
    
    private AuthException(int code, String message) {
        super(code, message);
    }
    
    public static class UserNotFound extends AuthException {
        public UserNotFound() {
            super(401, "用户不存在");
        }
    }
    
    public static class UserDisabled extends AuthException {
        public UserDisabled() {
            super(403, "账号已被禁用");
        }
    }
    
    public static class TokenExpired extends AuthException {
        public TokenExpired() {
            super(401, "登录已过期，请重新登录");
        }
    }
    
    public static class InvalidToken extends AuthException {
        public InvalidToken() {
            super(401, "无效的令牌");
        }
    }
    
    public static class EmailNotVerified extends AuthException {
        public EmailNotVerified() {
            super(403, "邮箱未验证");
        }
    }
} 