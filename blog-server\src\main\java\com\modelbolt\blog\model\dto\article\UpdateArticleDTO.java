package com.modelbolt.blog.model.dto.article;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "更新文章请求")
public class UpdateArticleDTO {

    @Schema(description = "文章标题")
    @NotBlank(message = "标题不能为空")
    @Size(max = 200, message = "标题长度不能超过200个字符")
    private String title;

    @Schema(description = "文章内容")
    @NotBlank(message = "内容不能为空")
    private String content;

    @Schema(description = "文章摘要")
    @Size(max = 500, message = "摘要长度不能超过500个字符")
    private String summary;

    @Schema(description = "封面图片URL")
    private String cover;

    @Schema(description = "分类ID列表")
    private List<Long> categoryIds;

    @Schema(description = "标签ID列表")
    private List<Long> tagIds;

    @Schema(description = "状态：draft-草稿，published-已发布")
    private String status;
} 