package com.modelbolt.blog.service;

import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.favorite.UserFavoriteVO;

public interface UserFavoriteService {
    /**
     * 获取用户收藏列表
     *
     * @param userId 用户ID
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页数量
     * @return 收藏分页结果
     */
    PageResult<UserFavoriteVO> getUserFavorites(Long userId, String keyword, Integer page, Integer size);
}