/**
 * API响应格式
 */
export interface ApiResponse<T> {
  /** 状态码 */
  code: number
  /** 消息 */
  message: string
  /** 数据 */
  data: T
}

/**
 * 分页结果
 */
export interface PageResult<T> {
  /** 记录列表 */
  records: T[]
  /** 总记录数 */
  total: number
  /** 每页大小 */
  size: number
  /** 当前页码 */
  current: number
  /** 总页数 */
  pages: number
}

/**
 * 分页参数
 */
export interface PageParams {
  /** 页码 */
  current: number
  /** 每页大小 */
  size: number
  /** 关键词 */
  keyword?: string
  /** 排序字段 */
  sortField?: string
  /** 排序方式 */
  sortOrder?: 'ascend' | 'descend'
}

// 导出类型别名，保持向后兼容
export type R<T> = ApiResponse<T> 