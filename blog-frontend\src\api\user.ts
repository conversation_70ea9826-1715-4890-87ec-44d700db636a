import { http } from '@/utils/request'
import type { R } from '@/types/common'
import type { UserInfo, AuthorInfo } from '@/types/user'

export function getUserInfo() {
  return http<R<UserInfo>>({
    url: '/user/info',
    method: 'GET'
  })
}

// 获取作者详细信息
export function getAuthorInfo(id: number) {
  return http<R<AuthorInfo>>({
    url: `/user/${id}/author`,
    method: 'GET'
  })
}

/**
 * 关注用户
 * @param userId 用户ID
 */
export function followUser(id: number) {
  return http<R<void>>({
    url: `/user/${id}/follow`,
    method: 'POST'
  })
}

/**
 * 取消关注用户
 * @param userId 用户ID
 */
export function unfollowUser(id: number) {
  return http<R<void>>({
    url: `/user/${id}/follow`,
    method: 'DELETE'
  })
}

/**
 * 检查是否已关注用户
 * @param userId 用户ID
 */
export function isFollowing(id: number) {
  return http<R<boolean>>({
    url: `/user/${id}/following`,
    method: 'GET'
  })
} 