package com.modelbolt.blog.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 分类实体
 */
@Data
@Accessors(chain = true)
@TableName("category")
@Schema(description = "分类实体")
public class Category {
    @TableId(type = IdType.AUTO)
    @Schema(description = "分类ID")
    private Long id;

    @Schema(description = "分类名称")
    private String name;

    @TableField(value = "parent_id", updateStrategy = FieldStrategy.IGNORED)
    @Schema(description = "父分类ID")
    private Long parentId;

    @TableField(value = "order_num", updateStrategy = FieldStrategy.NOT_NULL)
    @Schema(description = "排序")
    private Integer orderNum;

    @TableField(exist = false)
    @Schema(description = "子分类列表")
    private List<Category> children;

    @TableField(exist = false)
    @Schema(description = "文章数量")
    private Integer articleCount;
} 