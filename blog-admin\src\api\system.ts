import { http } from '@/utils/request'
import type { SystemConfig, MonitoringData } from '@/types/system'
import type { R } from '@/types/common'
import type { AxiosRequestConfig } from 'axios'

interface ExportRequestConfig extends AxiosRequestConfig {
  _isExport?: boolean
}

export const systemApi = {
  // 获取系统配置
  getConfig: () => http.get<R<SystemConfig>>('/system/config'),

  // 保存系统配置
  updateConfig: (config: SystemConfig) => http.post<R<void>>('/system/config', config),

  // 测试邮件配置
  testEmailConfig(): Promise<R<void>> {
    return http.post('/system/config/test-email')
  },

  // 测试AI配置
  testAiConfig(): Promise<R<void>> {
    return http.post('/system/config/test-ai')
  },

  // 获取监控数据
  getMonitoring: () => http.get<R<MonitoringData>>('/system/monitoring'),

  // 导出操作日志
  exportOperationLogs: () => http.get('/system/operation-logs/export', { 
    responseType: 'blob',
    headers: {
      'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    },
    _isExport: true
  } as ExportRequestConfig)
} 