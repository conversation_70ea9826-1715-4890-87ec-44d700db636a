package com.modelbolt.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.favorite.UserFavoriteVO;
import com.modelbolt.blog.model.entity.Article;
import com.modelbolt.blog.model.entity.UserFavorite;
import com.modelbolt.blog.mapper.ArticleMapper;
import com.modelbolt.blog.mapper.UserFavoriteMapper;
import com.modelbolt.blog.mapper.UserMapper;
import com.modelbolt.blog.service.ArticleService;
import com.modelbolt.blog.service.UserFavoriteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserFavoriteServiceImpl implements UserFavoriteService {

    private final UserFavoriteMapper userFavoriteMapper;
    private final ArticleMapper articleMapper;
    private final UserMapper userMapper;
    private final ArticleService articleService;

    @Override
    public PageResult<UserFavoriteVO> getUserFavorites(Long userId, String keyword, Integer page, Integer size) {
        log.debug("Getting user favorites, userId: {}, keyword: {}, page: {}, size: {}", userId, keyword, page, size);

        // 构建查询条件
        Page<UserFavorite> favoritePage = new Page<>(page, size);
        LambdaQueryWrapper<UserFavorite> wrapper = new LambdaQueryWrapper<UserFavorite>()
            .eq(UserFavorite::getUserId, userId)
            .orderByDesc(UserFavorite::getCreatedAt);

        // 执行分页查询
        Page<UserFavorite> pageResult = userFavoriteMapper.selectPage(favoritePage, wrapper);

        // 转换为VO
        List<UserFavoriteVO> records = pageResult.getRecords().stream()
            .map(favorite -> {
                UserFavoriteVO vo = new UserFavoriteVO();
                vo.setFavoriteTime(favorite.getCreatedAt());

                // 获取文章信息
                Article article = articleMapper.selectById(favorite.getArticleId());
                if (article != null) {
                    vo.setArticleId(article.getId());
                    vo.setArticleTitle(article.getTitle());
                    vo.setArticleSummary(article.getSummary());
                    vo.setArticleCover(article.getCover());
                    vo.setViewCount(article.getViewCount());
                    vo.setCommentCount(article.getCommentCount());

                    // 获取作者信息
                    String authorName = userMapper.selectUsernameById(article.getAuthorId());
                    vo.setAuthorName(authorName);
                }

                return vo;
            })
            .filter(vo -> !StringUtils.isNotBlank(keyword) || 
                         vo.getArticleTitle().toLowerCase().contains(keyword.toLowerCase()) ||
                         vo.getArticleSummary().toLowerCase().contains(keyword.toLowerCase()))
            .collect(Collectors.toList());

        // 如果有关键词，需要重新计算总数
        long total = keyword != null ? records.size() : pageResult.getTotal();

        return new PageResult<>(records, total, page, size);
    }
}