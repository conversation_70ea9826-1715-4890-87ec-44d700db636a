package com.modelbolt.blog.model.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "用户资料DTO")
public class UserProfileDTO {
    @Schema(description = "用户名")
    private String username;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "个人简介")
    private String bio;

    @Schema(description = "主题设置")
    private String theme;

    @Schema(description = "通知开关")
    private Boolean notificationEnabled;

    @Schema(description = "语言设置")
    private String language;
} 