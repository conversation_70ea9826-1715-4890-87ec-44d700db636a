package com.modelbolt.blog.exception.business;

import com.modelbolt.blog.exception.BusinessException;

/**
 * 分类相关异常类
 */
public class CategoryException extends BusinessException {
    
    private CategoryException(int code, String message) {
        super(code, message);
    }
    
    public static class DuplicateOrderNum extends CategoryException {
        public DuplicateOrderNum() {
            super(400, "同级分类下已存在相同的排序号");
        }
    }
    
    public static class CategoryNotFound extends CategoryException {
        public CategoryNotFound() {
            super(404, "分类不存在");
        }
    }
    
    public static class DuplicateName extends CategoryException {
        public DuplicateName() {
            super(400, "分类名称已存在，请使用其他名称");
        }
    }
    
    public static class CircularReference extends CategoryException {
        public CircularReference() {
            super(400, "不能选择自己或子分类作为父级分类");
        }
    }
    
    public static class HasChildren extends CategoryException {
        public HasChildren() {
            super(400, "请先删除子分类");
        }
    }
    
    public static class HasArticles extends CategoryException {
        public HasArticles() {
            super(400, "该分类下还有关联的文章，无法删除");
        }
    }

    public static class InvalidOrderNum extends CategoryException {
        public InvalidOrderNum() {
            super(400, "排序号必须大于0");
        }
    }
} 