<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted } from 'vue'
import { systemApi } from '@/api/system'
import type { SystemConfig, MonitoringData } from '@/types/system'
import { message } from 'ant-design-vue'
import type { EChartsOption } from 'echarts'
import VChart from 'vue-echarts'

// 状态定义
const activeTab = ref('basic')
const loading = ref(false)
const config = ref<SystemConfig>({
  siteName: '',
  siteDescription: '',
  siteKeywords: '',
  siteUrl: '',
  smtpHost: '',
  smtpPort: 587,
  smtpUsername: '',
  smtpPassword: '',
  smtpFrom: '',
  aiModel: 'DeepSeek',
  aiApiKey: '',
  aiApiEndpoint: '',
  aiMaxTokens: 2000,
  aiTemperature: 0.7,
  aiRequestLimit: 1000,
  aiTokenLimit: 100000,
  securityLoginAttempts: 5,
  securityLockDuration: 30
})

// 初始化监控数据
const monitoringData = ref<MonitoringData>({
  aiUsage: {
    totalCalls: 0,
    totalTokens: 0,
    successRate: 0,
    averageLatency: 0,
    dailyStats: []
  },
  performance: {
    cpuUsage: 0,
    memoryUsage: 0,
    responseTime: 0,
    concurrentUsers: 0,
    errorRate: 0
  },
  userBehavior: {
    activeUsers: 0,
    newUsers: 0,
    retention: 0,
    averageSessionTime: 0,
    topPages: []
  },
  contentQuality: {
    averageScore: 0,
    totalArticles: 0,
    qualityDistribution: [],
    topAuthors: <AUTHORS>
  }
})

// 获取配置
const fetchConfig = async () => {
  try {
    loading.value = true
    const { data } = await systemApi.getConfig()
    config.value = data
  } catch (error) {
    console.error('获取系统配置失败:', error)
    message.error('获取系统配置失败')
  } finally {
    loading.value = false
  }
}

// 获取监控数据
const fetchMonitoringData = async () => {
  try {
    const { data } = await systemApi.getMonitoring()
    if (data) {
      monitoringData.value = {
        ...monitoringData.value,
        ...data
      }
    }
  } catch (error) {
    console.error('获取监控数据失败:', error)
    message.error('获取监控数据失败')
  }
}

// 保存配置
const handleSubmit = async () => {
  try {
    loading.value = true
    await systemApi.updateConfig(config.value)
    message.success('保存成功')
  } catch (error) {
    console.error('保存系统配置失败:', error)
  } finally {
    loading.value = false
  }
}

// 测试邮件配置
const testEmailConfig = async () => {
  try {
    await systemApi.testEmailConfig()
    message.success('测试邮件发送成功')
  } catch (error) {
    console.error('测试邮件发送失败:', error)
  }
}

// 测试AI配置
const testAiConfig = async () => {
  try {
    await systemApi.testAiConfig()
    message.success('AI配置测试成功')
  } catch (error) {
    console.error('AI配置测试失败:', error)
  }
}

// 导出操作日志
const handleExportLogs = async () => {
  try {
    loading.value = true
    const response = await systemApi.exportOperationLogs()
    
    // 解码文件名并处理编码问题
    const decodeFilename = (header: string | undefined) => {
      if (!header) return '操作日志.xlsx'
      
      try {
        const filenamePart = header.split('filename*=utf-8\'\'')[1]
        const encodedName = filenamePart.replace(/["']/g, '')
        return decodeURIComponent(encodedName)
      } catch (e) {
        console.warn('文件名解析失败:', e)
        return '操作日志.xlsx'
      }
    }

    const filename = decodeFilename(response.headers['content-disposition'])
    
    // 创建 Blob 对象
    const blob = new Blob([response.data], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(link)
    message.success('导出成功')
  } catch (error) {
    console.error('导出操作日志失败:', error)
    message.error('导出失败')
  } finally {
    loading.value = false
  }
}

// 图表配置
const getAiUsageChartOption = computed((): EChartsOption => {
  const dailyStats = monitoringData.value?.aiUsage?.dailyStats || []
  
  return {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['调用次数', 'Token数']
    },
    xAxis: {
      type: 'category',
      data: dailyStats.map(item => item.date)
    },
    yAxis: [
      {
        type: 'value',
        name: '调用次数'
      },
      {
        type: 'value',
        name: 'Token数'
      }
    ],
    series: [
      {
        name: '调用次数',
        type: 'line',
        data: dailyStats.map(item => item.calls)
      },
      {
        name: 'Token数',
        type: 'line',
        yAxisIndex: 1,
        data: dailyStats.map(item => item.tokens)
      }
    ]
  }
})

// 初始化
onMounted(() => {
  fetchConfig()
  fetchMonitoringData()
  // 设置定时刷新监控数据
  const timer = setInterval(fetchMonitoringData, 60000)
  
  onUnmounted(() => {
    if (timer) {
      clearInterval(timer)
    }
  })
})
</script>

<template>
  <div class="setting-page">
    <a-tabs v-model:activeKey="activeTab">
      <!-- 基础配置 -->
      <a-tab-pane key="basic" tab="基础配置">
        <a-card :loading="loading">
          <a-form :model="config" layout="vertical">
            <a-form-item label="网站名称">
              <a-input v-model:value="config.siteName" />
            </a-form-item>
            <a-form-item label="网站描述">
              <a-textarea v-model:value="config.siteDescription" :rows="4" />
            </a-form-item>
            <a-form-item label="网站关键词">
              <a-input v-model:value="config.siteKeywords" />
            </a-form-item>
            <a-form-item label="网站地址">
              <a-input v-model:value="config.siteUrl" />
            </a-form-item>
          </a-form>
        </a-card>
      </a-tab-pane>

      <!-- 邮件配置 -->
      <a-tab-pane key="email" tab="邮件配置">
        <a-card :loading="loading">
          <a-form :model="config" layout="vertical">
            <a-form-item label="SMTP服务器" name="smtpHost">
              <a-input v-model:value="config.smtpHost" />
            </a-form-item>
            <a-form-item label="SMTP端口" name="smtpPort">
              <a-input-number v-model:value="config.smtpPort" />
            </a-form-item>
            <a-form-item label="SMTP用户名" name="smtpUsername">
              <a-input v-model:value="config.smtpUsername" />
            </a-form-item>
            <a-form-item label="SMTP密码" name="smtpPassword">
              <a-input-password v-model:value="config.smtpPassword" />
            </a-form-item>
            <a-form-item label="发件人地址" name="smtpFrom">
              <a-input v-model:value="config.smtpFrom" />
            </a-form-item>
            <a-form-item>
              <a-button @click="testEmailConfig">测试邮件配置</a-button>
            </a-form-item>
          </a-form>
        </a-card>
      </a-tab-pane>

      <!-- AI模型配置 -->
      <a-tab-pane key="ai" tab="AI模型配置">
        <a-card :loading="loading">
          <a-form :model="config" layout="vertical">
            <a-form-item label="AI模型" name="aiModel">
              <a-select v-model:value="config.aiModel">
                <a-select-option value="DeepSeek">DeepSeek</a-select-option>
                <a-select-option value="GPT-4">GPT-4</a-select-option>
                <a-select-option value="Claude">Claude</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="API密钥" name="aiApiKey">
              <a-input-password v-model:value="config.aiApiKey" />
            </a-form-item>
            
            <a-form-item label="API地址" name="aiApiEndpoint">
              <a-input v-model:value="config.aiApiEndpoint" />
            </a-form-item>
            
            <a-form-item label="最大Token数" name="aiMaxTokens">
              <a-input-number v-model:value="config.aiMaxTokens" :min="1" />
            </a-form-item>
            
            <a-form-item label="温度" name="aiTemperature">
              <a-slider v-model:value="config.aiTemperature" :min="0" :max="1" :step="0.1" />
            </a-form-item>
            
            <a-form-item label="每日请求限制" name="aiRequestLimit">
              <a-input-number v-model:value="config.aiRequestLimit" :min="0" />
            </a-form-item>
            
            <a-form-item label="每日Token限制" name="aiTokenLimit">
              <a-input-number v-model:value="config.aiTokenLimit" :min="0" />
            </a-form-item>

            <a-form-item>
              <a-button @click="testAiConfig">测试AI配置</a-button>
            </a-form-item>
          </a-form>
        </a-card>
      </a-tab-pane>

      <!-- 安全配置 -->
      <a-tab-pane key="security" tab="安全配置">
        <a-card :loading="loading">
          <a-form :model="config" layout="vertical">
            <a-form-item label="登录失败次数限制" name="securityLoginAttempts">
              <a-input-number v-model:value="config.securityLoginAttempts" :min="1" />
            </a-form-item>
            <a-form-item label="账号锁定时间(分钟)" name="securityLockDuration">
              <a-input-number v-model:value="config.securityLockDuration" :min="1" />
            </a-form-item>
          </a-form>
        </a-card>
      </a-tab-pane>

      <!-- 监控与统计 -->
      <a-tab-pane key="monitoring" tab="监控与统计">
        <a-row :gutter="16">
          <!-- AI调用统计 -->
          <a-col :span="12">
            <a-card title="AI调用统计">
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-statistic
                    title="总调用次数"
                    :value="monitoringData.aiUsage.totalCalls"
                    :precision="0"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="总Token数"
                    :value="monitoringData.aiUsage.totalTokens"
                    :precision="0"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="成功率"
                    :value="monitoringData.aiUsage.successRate"
                    :precision="2"
                    suffix="%"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="平均延迟"
                    :value="monitoringData.aiUsage.averageLatency"
                    :precision="0"
                    suffix="ms"
                  />
                </a-col>
              </a-row>
              <v-chart :option="getAiUsageChartOption" style="height: 300px" />
            </a-card>
          </a-col>

          <!-- 性能监控 -->
          <a-col :span="12">
            <a-card title="性能监控">
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-statistic
                    title="CPU使用率"
                    :value="monitoringData.performance.cpuUsage"
                    :precision="2"
                    suffix="%"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="内存使用率"
                    :value="monitoringData.performance.memoryUsage"
                    :precision="2"
                    suffix="%"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="响应时间"
                    :value="monitoringData.performance.responseTime"
                    :precision="0"
                    suffix="ms"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="并发用户数"
                    :value="monitoringData.performance.concurrentUsers"
                    :precision="0"
                  />
                </a-col>
              </a-row>
              <a-row :gutter="16" style="margin-top: 16px">
                <a-col :span="6">
                  <a-statistic
                    title="错误率"
                    :value="monitoringData.performance.errorRate"
                    :precision="2"
                    suffix="%"
                  />
                </a-col>
              </a-row>
            </a-card>
          </a-col>

          <!-- 用户行为分析 -->
          <a-col :span="12">
            <a-card title="用户行为分析" style="margin-top: 16px">
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-statistic
                    title="活跃用户数"
                    :value="monitoringData.userBehavior.activeUsers"
                    :precision="0"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="新增用户数"
                    :value="monitoringData.userBehavior.newUsers"
                    :precision="0"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="用户留存率"
                    :value="monitoringData.userBehavior.retention"
                    :precision="2"
                    suffix="%"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="平均会话时长"
                    :value="monitoringData.userBehavior.averageSessionTime"
                    :precision="0"
                    suffix="分钟"
                  />
                </a-col>
              </a-row>
              <a-table
                :columns="[
                  { title: '页面', dataIndex: 'page' },
                  { title: '访问量', dataIndex: 'views' }
                ]"
                :data-source="monitoringData.userBehavior.topPages"
                size="small"
                :pagination="false"
              />
            </a-card>
          </a-col>

          <!-- 内容质量分析 -->
          <a-col :span="12">
            <a-card title="内容质量分析" style="margin-top: 16px">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-statistic
                    title="平均质量分"
                    :value="monitoringData.contentQuality.averageScore"
                    :precision="2"
                  />
                </a-col>
                <a-col :span="12">
                  <a-statistic
                    title="文章总数"
                    :value="monitoringData.contentQuality.totalArticles"
                    :precision="0"
                  />
                </a-col>
              </a-row>
              <a-table
                :columns="[
                  { title: '作者', dataIndex: 'authorName' },
                  { 
                    title: '质量分', 
                    dataIndex: 'score',
                    customRender: ({ text }) => Number(text).toFixed(2)
                  }
                ]"
                :data-source="monitoringData.contentQuality.topAuthors"
                size="small"
                :pagination="false"
              />
            </a-card>
          </a-col>
        </a-row>

        <!-- 操作日志导出 -->
        <a-card title="操作日志" style="margin-top: 16px">
          <a-button type="primary" @click="handleExportLogs">
            导出操作日志
          </a-button>
        </a-card>
      </a-tab-pane>
    </a-tabs>

    <!-- 保存按钮 - 只在非监控统计页面显示 -->
    <div class="setting-actions" v-if="activeTab !== 'monitoring'">
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        保存设置
      </a-button>
    </div>
  </div>
</template>

<style scoped>
.setting-page {
  padding: 24px;
}

.setting-actions {
  margin-top: 24px;
  text-align: center;
}

:deep(.ant-card) {
  margin-bottom: 24px;
}

:deep(.ant-form-item:last-child) {
  margin-bottom: 0;
}

:deep(.ant-statistic) {
  margin-right: 16px;
}

:deep(.ant-card-head) {
  min-height: 48px;
}

:deep(.ant-card-head-title) {
  padding: 8px 0;
}
</style> 