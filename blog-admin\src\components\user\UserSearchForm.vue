<script setup lang="ts">
import { ref } from 'vue'
import type { UserQuery } from '@/types/user'
import { UserRole, UserStatus } from '@/types/user'

const props = defineProps<{
  loading?: boolean
  username?: string
  email?: string
  role?: string
  status?: number
}>()

const emit = defineEmits<{
  (e: 'update:username', value: string): void
  (e: 'update:email', value: string): void
  (e: 'update:role', value: string): void
  (e: 'update:status', value: number): void
  (e: 'search'): void
  (e: 'reset'): void
}>()

const formRef = ref()
const formState = ref<UserQuery>({
  username: '',
  email: '',
  role: undefined,
  status: undefined
})

const roleOptions = [
  { label: '管理员', value: UserRole.ADMIN },
  { label: '普通用户', value: UserRole.USER }
]

const statusOptions = [
  { label: '启用', value: UserStatus.ENABLED },
  { label: '禁用', value: UserStatus.DISABLED }
]

const handleUsernameChange = (value: string) => {
  emit('update:username', value)
}

const handleEmailChange = (value: string) => {
  emit('update:email', value)
}

const handleRoleChange = (value: string) => {
  emit('update:role', value)
}

const handleStatusChange = (value: number) => {
  emit('update:status', value)
}

const handleSearch = () => {
  emit('update:username', formState.value.username)
  emit('update:email', formState.value.email)
  emit('update:role', formState.value.role)
  emit('update:status', formState.value.status)
  emit('search')
}

const handleReset = () => {
  formRef.value?.resetFields()
  emit('reset')
}
</script>

<template>
  <a-form
    ref="formRef"
    :model="formState"
    layout="inline"
    class="mb-4"
  >
    <a-form-item name="username">
      <a-input
        v-model:value="formState.username"
        placeholder="用户名"
        allow-clear
      />
    </a-form-item>

    <a-form-item name="email">
      <a-input
        v-model:value="formState.email"
        placeholder="邮箱"
        allow-clear
      />
    </a-form-item>

    <a-form-item name="role">
      <a-select
        v-model:value="formState.role"
        placeholder="角色"
        allow-clear
        style="min-width: 100px"
      >
        <a-select-option
          v-for="option in roleOptions"
          :key="option.value"
          :value="option.value"
        >
          {{ option.label }}
        </a-select-option>
      </a-select>
    </a-form-item>

    <a-form-item name="status">
      <a-select
        v-model:value="formState.status"
        placeholder="状态"
        allow-clear
        style="min-width: 100px"
      >
        <a-select-option
          v-for="option in statusOptions"
          :key="option.value"
          :value="option.value"
        >
          {{ option.label }}
        </a-select-option>
      </a-select>
    </a-form-item>

    <a-form-item>
      <a-space>
        <a-button
          type="primary"
          :loading="loading"
          @click="handleSearch"
        >
          搜索
        </a-button>
        <a-button @click="handleReset">重置</a-button>
      </a-space>
    </a-form-item>
  </a-form>
</template> 