package com.modelbolt.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.article.ArticleVO;
import com.modelbolt.blog.model.dto.article.CreateArticleDTO;
import com.modelbolt.blog.model.dto.article.UpdateArticleDTO;
import com.modelbolt.blog.model.dto.article.ArticleEditVO;
import com.modelbolt.blog.model.dto.comment.CommentVO;
import com.modelbolt.blog.model.dto.tag.TagVO;
import com.modelbolt.blog.model.dto.category.CategoryVO;
import com.modelbolt.blog.model.entity.*;
import com.modelbolt.blog.exception.business.ArticleException;
import com.modelbolt.blog.exception.business.AuthException;
import com.modelbolt.blog.exception.business.ArticleOperationException;
import com.modelbolt.blog.mapper.ArticleMapper;
import com.modelbolt.blog.mapper.CommentMapper;
import com.modelbolt.blog.mapper.TagMapper;
import com.modelbolt.blog.mapper.UserFavoriteMapper;
import com.modelbolt.blog.mapper.UserLikeMapper;
import com.modelbolt.blog.mapper.UserMapper;
import com.modelbolt.blog.service.ArticleService;
import com.modelbolt.blog.service.CategoryService;
import com.modelbolt.blog.service.NotificationService;
import com.modelbolt.blog.service.TagService;
import com.modelbolt.blog.service.UserService;
import com.modelbolt.blog.utils.JwtUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.Arrays;

@Slf4j
@Service
@RequiredArgsConstructor
public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> implements ArticleService {

    private final ArticleMapper articleMapper;
    private final UserMapper userMapper;
    private final TagMapper tagMapper;
    private final CommentMapper commentMapper;
    private final RedisTemplate<String, Object> redisTemplate;
    private final JwtUtils jwtUtils;
    private final UserService userService;
    private final UserLikeMapper userLikeMapper;
    private final UserFavoriteMapper userFavoriteMapper;
    private final NotificationService notificationService;
    private final CategoryService categoryService;
    private final TagService tagService;

    // Redis 缓存相关常量
    private static final String HOT_ARTICLES_CACHE_KEY = "hot_articles:%d";  // %d为limit参数
    private static final String ARTICLE_VIEWS_CACHE_KEY = "article_views:%d";  // %d为文章ID
    private static final String USER_ARTICLE_LIKE_KEY = "user_article_like:%d:%d";  // 第一个%d为用户ID，第二个%d为文章ID
    private static final String USER_ARTICLE_FAVORITE_KEY = "user_article_favorite:%d:%d";
    private static final long CACHE_EXPIRE_TIME = 3600L; // 缓存过期时间：1小时

    /**
     * 获取当前登录用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() &&
                !(authentication instanceof AnonymousAuthenticationToken)) {
            try {
                String username = authentication.getName();
                // 通过用户名查询用户ID
                User user = userMapper.selectOne(
                        new LambdaQueryWrapper<User>()
                                .eq(User::getUsername, username)
                );
                if (user != null) {
                    return user.getId();
                }
            } catch (Exception e) {
                log.warn("Failed to get user id for username: {}", authentication.getName(), e);
            }
            log.warn("Unable to extract userId from authentication");
        }
        return null;
    }

    @Override
    public List<ArticleVO> getFeaturedArticles() {
        // 获取置顶文章
        LambdaQueryWrapper<Article> wrapper = new LambdaQueryWrapper<Article>()
            .eq(Article::getStatus, 1)  // 已发布
            .eq(Article::getIsTop, true)
            .orderByDesc(Article::getCreatedAt)
            .last("LIMIT 5");  // 最多返回5篇

        return articleMapper.selectList(wrapper).stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
    }

    @Override
    public PageResult<ArticleVO> getArticles(Long categoryId, String sort, Integer page, Integer size) {
        // 构建查询条件
        Page<Article> pageResult;
        if (categoryId != null) {
            // 如果有分类ID，使用关联查询
            pageResult = articleMapper.selectPageWithCategory(new Page<>(page, size), categoryId, sort);
        } else {
            // 没有分类ID，使用普通查询
            LambdaQueryWrapper<Article> wrapper = new LambdaQueryWrapper<Article>()
                .eq(Article::getStatus, 1);  // 已发布

            // 添加排序
            switch (sort) {
                case "hottest":
                    wrapper.orderByDesc(Article::getViewCount);
                    break;
                case "recommended":
                    wrapper.orderByDesc(Article::getLikeCount);
                    break;
                default:  // newest
                    wrapper.orderByDesc(Article::getCreatedAt);
            }
            
            pageResult = articleMapper.selectPage(new Page<>(page, size), wrapper);
        }

        // 转换结果
        List<ArticleVO> records = pageResult.getRecords().stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());

        return new PageResult<>(records, pageResult.getTotal(), page, size);
    }

    @Override
    public List<ArticleVO> getHotArticles(Integer limit) {
        String cacheKey = String.format(HOT_ARTICLES_CACHE_KEY, limit);
        
        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        List<ArticleVO> cachedArticles = (List<ArticleVO>) redisTemplate.opsForValue().get(cacheKey);
        if (cachedArticles != null) {
            log.debug("从缓存获取热门文章, limit: {}", limit);
            return cachedArticles;
        }

        // 缓存未命中，从数据库查询
        log.debug("从数据库获取热门文章, limit: {}", limit);
        LambdaQueryWrapper<Article> wrapper = new LambdaQueryWrapper<Article>()
            .eq(Article::getStatus, 1)  // 已发布
            .orderByDesc(Article::getViewCount)
            .last("LIMIT " + limit);

        List<ArticleVO> hotArticles = articleMapper.selectList(wrapper).stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());

        // 存入缓存
        try {
            redisTemplate.opsForValue().set(cacheKey, hotArticles, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
            log.debug("热门文章已缓存, limit: {}", limit);
        } catch (Exception e) {
            log.error("缓存热门文章失败: {}", e.getMessage());
        }

        return hotArticles;
    }

    /**
     * 清除热门文章缓存
     * 在文章更新、删除、新增时调用
     */
    public void clearHotArticlesCache() {
        try {
            String pattern = HOT_ARTICLES_CACHE_KEY.replace("%d", "*");
            redisTemplate.keys(pattern).forEach(key -> redisTemplate.delete(key));
            log.debug("热门文章缓存已清除");
        } catch (Exception e) {
            log.error("清除热门文章缓存失败: {}", e.getMessage());
        }
    }

    @Override
    public ArticleVO getArticleDetail(Long id) {
        // 获取文章详情
        Article article = articleMapper.selectById(id);
        if (article == null) {
            throw new ArticleException.ArticleNotFound();
        }

        // 转换为VO对象
        ArticleVO vo = convertToVO(article);

        // 获取当前用户ID
        Long userId = getCurrentUserId();
        if (userId != null) {
            // 首先检查数据库中的状态
            vo.setLiked(isArticleLiked(id, userId));
            vo.setFavorited(isArticleFavorited(id, userId));

            // 如果数据库中没有记录，则检查Redis中的状态
            if (!vo.getLiked()) {
                String likeKey = String.format(USER_ARTICLE_LIKE_KEY, userId, id);
                vo.setLiked(Boolean.TRUE.equals(redisTemplate.hasKey(likeKey)));
            }
            if (!vo.getFavorited()) {
                String favoriteKey = String.format(USER_ARTICLE_FAVORITE_KEY, userId, id);
                vo.setFavorited(Boolean.TRUE.equals(redisTemplate.hasKey(favoriteKey)));
            }
        } else {
            // 未登录用户默认未点赞和未收藏
            vo.setLiked(false);
            vo.setFavorited(false);
        }

        return vo;
    }

    @Override
    public List<ArticleVO> getRelatedArticles(Long id, Integer limit) {
        return articleMapper.selectRelatedArticles(id, limit).stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
    }

    @Override
    public PageResult<CommentVO> getArticleComments(Long id, Integer page, Integer size) {
        log.debug("Getting comments for article: {}, page: {}, size: {}", id, page, size);
        
        // 构建分页查询条件
        Page<Comment> commentPage = new Page<>(page, size);
        LambdaQueryWrapper<Comment> wrapper = new LambdaQueryWrapper<Comment>()
            .eq(Comment::getArticleId, id)
            .eq(Comment::getStatus, 1)  // 已审核的评论
            .isNull(Comment::getParentId)  // 只查询根评论
            .orderByDesc(Comment::getCreatedAt);

        // 执行分页查询
        Page<Comment> pageResult = commentMapper.selectPage(commentPage, wrapper);
        log.debug("Found {} comments for article {}", pageResult.getTotal(), id);

        // 转换为VO对象
        List<CommentVO> records = pageResult.getRecords().stream()
            .map(this::convertCommentToVO)
            .collect(Collectors.toList());

        return new PageResult<>(records, pageResult.getTotal(), page, size);
    }

    @Override
    @Transactional
    public void updateArticleViews(Long id) {
        String cacheKey = String.format(ARTICLE_VIEWS_CACHE_KEY, id);
        
        // 直接更新数据库
        articleMapper.incrementViewCount(id);
        
        // 更新缓存
        Long viewCount = redisTemplate.opsForValue().increment(cacheKey);
        if (viewCount == null) {
            // 如果缓存不存在，从数据库获取最新值
            Article article = articleMapper.selectById(id);
            if (article != null) {
                redisTemplate.opsForValue().set(cacheKey, article.getViewCount());
            }
        }
        redisTemplate.expire(cacheKey, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void likeArticle(Long id) {
        Long userId = getCurrentUserId();
        if (userId == null) {
            throw new AuthException.UserNotFound();
        }

        // 检查文章是否存在
        Article article = getById(id);
        if (article == null) {
            throw new ArticleException.ArticleNotFound();
        }

        // 检查是否已点赞
        if (isArticleLiked(id, userId)) {
            throw new ArticleException.AlreadyLiked();
        }

        // 添加点赞记录
        articleMapper.insertLike(id, userId);

        // 更新文章点赞数
        if (articleMapper.incrementLikeCount(id) != 1) {
            throw new ArticleException.UpdateFailed();
        }

        // 如果点赞者不是文章作者，创建点赞通知
        if (!userId.equals(article.getAuthorId())) {
            notificationService.createNotification(
                article.getAuthorId(),    // 接收者是文章作者
                userId,                 // 发送者是点赞者
                NotificationType.LIKE,  // 通知类型是点赞
                id,                     // 目标是文章
                NotificationTargetType.ARTICLE,
                "点赞了你的文章"
            );
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unlikeArticle(Long id) {
        Long userId = getCurrentUserId();
        if (userId == null) {
            throw new AuthException.UserNotFound();
        }

        // 使用数据库锁确保并发安全
        synchronized (String.valueOf(id).intern()) {
            // 检查是否已点赞
            if (!isArticleLiked(id, userId)) {
                throw new ArticleException.NotLikedYet();
            }

            // 删除点赞记录
            userLikeMapper.deleteByUserIdAndArticleId(userId, id);

            // 更新文章点赞数
            if (articleMapper.decrementLikeCount(id) != 1) {
                throw new ArticleException.UpdateFailed();
            }

            // 删除Redis缓存
            String likeKey = String.format(USER_ARTICLE_LIKE_KEY, userId, id);
            redisTemplate.delete(likeKey);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void favoriteArticle(Long id) {
        Long userId = getCurrentUserId();
        if (userId == null) {
            throw new AuthException.UserNotFound();
        }

        // 检查文章是否存在
        Article article = getById(id);
        if (article == null) {
            throw new ArticleException.ArticleNotFound();
        }

        // 使用数据库锁确保并发安全
        synchronized (String.valueOf(id).intern()) {
            // 检查是否已收藏
            if (isArticleFavorited(id, userId)) {
                throw new ArticleException.AlreadyFavorited();
            }

            // 添加收藏记录
            UserFavorite userFavorite = UserFavorite.builder()
                    .userId(userId)
                    .articleId(id)
                    .build();
            userFavoriteMapper.insert(userFavorite);

            // 更新文章收藏数
            if (articleMapper.incrementFavoriteCount(id) != 1) {
                throw new ArticleException.UpdateFailed();
            }

            // 更新Redis缓存
            String favoriteKey = String.format(USER_ARTICLE_FAVORITE_KEY, userId, id);
            redisTemplate.opsForValue().set(favoriteKey, true, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unfavoriteArticle(Long id) {
        Long userId = getCurrentUserId();
        if (userId == null) {
            throw new AuthException.UserNotFound();
        }

        // 使用数据库锁确保并发安全
        synchronized (String.valueOf(id).intern()) {
            // 检查是否已收藏
            if (!isArticleFavorited(id, userId)) {
                throw new ArticleException.NotFavoritedYet();
            }

            // 删除收藏记录
            userFavoriteMapper.deleteByUserIdAndArticleId(userId, id);

            // 更新文章收藏数
            if (articleMapper.decrementFavoriteCount(id) != 1) {
                throw new ArticleException.UpdateFailed();
            }

            // 删除Redis缓存
            String favoriteKey = String.format(USER_ARTICLE_FAVORITE_KEY, userId, id);
            redisTemplate.delete(favoriteKey);
        }
    }

    @Override
    public boolean isArticleLiked(Long articleId, Long userId) {
        if (userId == null) {
            return false;
        }
        return userLikeMapper.existsByUserIdAndArticleId(userId, articleId);
    }

    @Override
    public boolean isArticleFavorited(Long articleId, Long userId) {
        if (userId == null) {
            return false;
        }
        return userFavoriteMapper.existsByUserIdAndArticleId(userId, articleId);
    }

    /**
     * 将Article实体转换为ArticleVO
     */
    private ArticleVO convertToVO(Article article) {
        ArticleVO vo = new ArticleVO();
        BeanUtils.copyProperties(article, vo);

        // 设置作者信息
        vo.setAuthorId(article.getAuthorId());
        String authorName = userMapper.selectUsernameById(article.getAuthorId());
        String authorAvatar = userMapper.selectUserAvatarById(article.getAuthorId());
        vo.setAuthorName(authorName);
        vo.setAuthorAvatar(authorAvatar);

        // 设置标签列表
        List<TagVO> tags = tagMapper.selectTagsByArticleId(article.getId());
        vo.setTags(tags);

        // 获取当前用户ID
        Long userId = getCurrentUserId();
        if (userId != null) {
            // 首先检查数据库中的状态
            vo.setLiked(isArticleLiked(article.getId(), userId));
            vo.setFavorited(isArticleFavorited(article.getId(), userId));

            // 如果数据库中没有记录，则检查Redis中的状态
            if (!vo.getLiked()) {
                String likeKey = String.format(USER_ARTICLE_LIKE_KEY, userId, article.getId());
                vo.setLiked(Boolean.TRUE.equals(redisTemplate.hasKey(likeKey)));
            }
            if (!vo.getFavorited()) {
                String favoriteKey = String.format(USER_ARTICLE_FAVORITE_KEY, userId, article.getId());
                vo.setFavorited(Boolean.TRUE.equals(redisTemplate.hasKey(favoriteKey)));
            }
        } else {
            // 未登录用户默认未点赞和未收藏
            vo.setLiked(false);
            vo.setFavorited(false);
        }

        return vo;
    }

    /**
     * 将评论实体转换为VO对象
     */
    private CommentVO convertCommentToVO(Comment comment) {
        log.debug("Converting comment to VO: {}", comment.getId());
        CommentVO vo = new CommentVO();
        BeanUtils.copyProperties(comment, vo);

        // 设置用户信息
        String username = userMapper.selectUsernameById(comment.getUserId());
        String userAvatar = userMapper.selectUserAvatarById(comment.getUserId());
        vo.setUserName(username);
        vo.setUserAvatar(userAvatar);
        log.debug("Set user info for comment {}: username={}, avatar={}", comment.getId(), username, userAvatar);

        // 如果是根评论，查询回复数和子评论
        if (comment.getParentId() == null) {
            // 查询回复数
            LambdaQueryWrapper<Comment> countWrapper = new LambdaQueryWrapper<Comment>()
                .eq(Comment::getRootId, comment.getId())
                .eq(Comment::getStatus, 1);
            Long replyCount = commentMapper.selectCount(countWrapper);
            vo.setReplyCount(replyCount.intValue());
            log.debug("Comment {} has {} replies", comment.getId(), replyCount);

            // 查询子评论（回复）
            if (replyCount > 0) {
                LambdaQueryWrapper<Comment> replyWrapper = new LambdaQueryWrapper<Comment>()
                    .eq(Comment::getRootId, comment.getId())
                    .eq(Comment::getStatus, 1)
                    .orderByDesc(Comment::getCreatedAt)
                    .last("LIMIT 3"); // 只取最新的3条回复
                List<Comment> replies = commentMapper.selectList(replyWrapper);
                vo.setChildren(replies.stream()
                    .map(this::convertCommentToVO)
                    .collect(Collectors.toList()));
                log.debug("Added {} child comments for comment {}", replies.size(), comment.getId());
            }
        }

        return vo;
    }

    @Override
    public boolean updateCommentCount(Long articleId) {
        // 获取文章的评论数
        int commentCount = commentMapper.selectCount(
            new LambdaQueryWrapper<Comment>()
                .eq(Comment::getArticleId, articleId)
                .eq(Comment::getStatus, 1)  // 只统计已审核的评论
        ).intValue();
        
        // 更新文章的评论数
        Article article = new Article();
        article.setId(articleId);
        article.setCommentCount(commentCount);
        return updateById(article);
    }

    /**
     * 获取用户文章列表
     */
    @Override
    public PageResult<ArticleVO> getUserArticles(String keyword, String status, Integer page, Integer size) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        if (userId == null) {
            throw new AuthException.UserNotFound();
        }
        
        // 构建查询条件
        LambdaQueryWrapper<Article> wrapper = new LambdaQueryWrapper<Article>()
            .eq(Article::getAuthorId, userId)
            .eq(org.apache.commons.lang3.StringUtils.isNotBlank(status), Article::getStatus, status)
            .and(org.apache.commons.lang3.StringUtils.isNotBlank(keyword), w -> w
                .like(Article::getTitle, keyword)
                .or()
                .like(Article::getContent, keyword)
            )
            .orderByDesc(Article::getCreatedAt);

        // 执行分页查询
        Page<Article> articlePage = page(new Page<>(page, size), wrapper);
        
        // 转换为VO
        List<ArticleVO> records = articlePage.getRecords().stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());

        return new PageResult<>(records, articlePage.getTotal(), page, size);
    }

    @Override
    public PageResult<ArticleVO> getUserPublicArticles(Long userId, Integer page, Integer size) {
        // 检查用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new AuthException.UserNotFound();
        }
        
        // 构建查询条件，只查询已发布的文章
        LambdaQueryWrapper<Article> wrapper = new LambdaQueryWrapper<Article>()
            .eq(Article::getAuthorId, userId)
            .eq(Article::getStatus, 1)  // 1表示已发布状态
            .orderByDesc(Article::getCreatedAt);

        // 执行分页查询
        Page<Article> articlePage = page(new Page<>(page, size), wrapper);
        
        // 转换为VO
        List<ArticleVO> records = articlePage.getRecords().stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());

        return new PageResult<>(records, articlePage.getTotal(), page, size);
    }

    /**
     * 将状态字符串或数字转换为数据库中的状态值
     */
    private Integer convertStatus(String status) {
        // 如果是数字字符串，直接转换
        if (status.matches("\\d+")) {
            int statusNum = Integer.parseInt(status);
            if (statusNum >= 0 && statusNum <= 2) {
                return statusNum;
            }
        }
        
        // 如果是字符串状态，进行映射
        return switch (status.toLowerCase()) {
            case "draft" -> 0;    // 草稿
            case "published" -> 1; // 发布
            case "disabled", "unpublished" -> 2; // 下架
            default -> throw new ArticleOperationException.InvalidStatus("无效的文章状态: " + status);
        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createArticle(CreateArticleDTO dto) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        if (userId == null) {
            throw new AuthException.UserNotFound();
        }
        
        // 构建文章实体
        Article article = new Article();
        article.setTitle(dto.getTitle());
        article.setContent(dto.getContent());
        article.setSummary(dto.getSummary());
        article.setCover(dto.getCover());
        article.setAuthorId(userId);
        article.setStatus(convertStatus(dto.getStatus())); // 使用状态转换方法
        article.setViewCount(0);
        article.setLikeCount(0);
        article.setFavoriteCount(0);
        article.setCommentCount(0);
        article.setIsTop(false);
        
        // 保存文章
        save(article);
        
        // 保存分类关联
        if (!CollectionUtils.isEmpty(dto.getCategoryIds())) {
            articleMapper.insertArticleCategories(article.getId(), dto.getCategoryIds());
        }
        
        // 保存标签关联
        if (!CollectionUtils.isEmpty(dto.getTagIds())) {
            articleMapper.insertArticleTags(article.getId(), dto.getTagIds());
        }
        
        return article.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateArticle(Long id, UpdateArticleDTO dto) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        if (userId == null) {
            throw new AuthException.UserNotFound();
        }
        
        // 检查文章是否存在
        Article article = getById(id);
        if (article == null) {
            throw new ArticleException.ArticleNotFound();
        }

        // 获取当前用户角色
        User currentUser = userMapper.selectById(userId);
        if (currentUser == null) {
            throw new AuthException.UserNotFound();
        }

        // 检查文章是否属于当前用户，如果不是管理员则需要检查权限
        if (!"ADMIN".equals(currentUser.getRole()) && !article.getAuthorId().equals(userId)) {
            throw new ArticleOperationException.NoPermission();
        }
        
        // 更新文章基本信息
        article.setTitle(dto.getTitle());
        article.setContent(dto.getContent());
        article.setSummary(dto.getSummary());
        article.setCover(dto.getCover());
        article.setStatus(convertStatus(dto.getStatus())); // 使用状态转换方法
        
        updateById(article);
        
        // 更新分类关联
        articleMapper.deleteArticleCategories(id);
        if (!CollectionUtils.isEmpty(dto.getCategoryIds())) {
            articleMapper.insertArticleCategories(id, dto.getCategoryIds());
        }
        
        // 更新标签关联
        articleMapper.deleteArticleTags(id);
        if (!CollectionUtils.isEmpty(dto.getTagIds())) {
            articleMapper.insertArticleTags(id, dto.getTagIds());
        }
    }

    /**
     * 删除文章
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteArticle(Long id) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        if (userId == null) {
            throw new AuthException.UserNotFound();
        }
        
        // 检查文章是否存在且属于当前用户
        Article article = getById(id);
        if (article == null) {
            throw new ArticleException.ArticleNotFound();
        }
        if (!article.getAuthorId().equals(userId)) {
            throw new ArticleOperationException.NoPermission();
        }
        
        try {
            // 删除文章分类关联
            articleMapper.deleteArticleCategories(id);
            
            // 删除文章标签关联
            articleMapper.deleteArticleTags(id);
            
            // 删除文章
            removeById(id);
            
            // 清除相关缓存
            clearHotArticlesCache();
            String viewsKey = String.format(ARTICLE_VIEWS_CACHE_KEY, id);
            redisTemplate.delete(viewsKey);
            
        } catch (Exception e) {
            log.error("删除文章失败: {}", e.getMessage());
            throw new ArticleOperationException.DeleteFailed("删除文章及其关联数据时发生错误");
        }
    }

    @Override
    public ArticleVO getArticleForEdit(Long id) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        if (userId == null) {
            throw new AuthException.UserNotFound();
        }

        // 获取文章编辑信息
        ArticleEditVO articleEdit = articleMapper.selectArticleForEdit(id);
        if (articleEdit == null) {
            throw new ArticleException.ArticleNotFound();
        }

        // 获取当前用户角色
        User currentUser = userMapper.selectById(userId);
        if (currentUser == null) {
            throw new AuthException.UserNotFound();
        }

        // 检查文章是否属于当前用户，如果不是管理员则需要检查权限
        if (!"ADMIN".equals(currentUser.getRole()) && !articleEdit.getAuthorId().equals(userId)) {
            throw new ArticleOperationException.NoPermission();
        }

        // 如果有分类ID字符串，转换为List<Long>
        if (articleEdit.getCategoryIdsStr() != null) {
            articleEdit.setCategoryIds(Arrays.stream(articleEdit.getCategoryIdsStr().split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList()));
        }

        // 如果有标签ID字符串，转换为List<Long>
        if (articleEdit.getTagIdsStr() != null) {
            articleEdit.setTagIds(Arrays.stream(articleEdit.getTagIdsStr().split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList()));
        }

        return articleEdit;
    }

    @Override
    public boolean decrementFavoriteCount(Long articleId) {
        return lambdaUpdate()
                .eq(Article::getId, articleId)
                .setSql("favorite_count = favorite_count - 1")
                .update();
    }

    @Override
    public PageResult<ArticleVO> getAdminArticleList(String keyword, Integer status, Long categoryId, Long tagId, Integer current, Integer size) {
        // 构建查询条件
        LambdaQueryWrapper<Article> wrapper = new LambdaQueryWrapper<>();
        
        // 关键词搜索
        if (StringUtils.hasText(keyword)) {
            wrapper.like(Article::getTitle, keyword)
                  .or()
                  .like(Article::getContent, keyword);
        }
        
        // 状态筛选
        if (status != null) {
            wrapper.eq(Article::getStatus, status);
        }
        
        // 分类筛选
        if (categoryId != null) {
            wrapper.inSql(Article::getId, 
                "SELECT article_id FROM article_category WHERE category_id = " + categoryId);
        }
        
        // 标签筛选
        if (tagId != null) {
            wrapper.inSql(Article::getId, 
                "SELECT article_id FROM article_tag WHERE tag_id = " + tagId);
        }
        
        // 按创建时间倒序
        wrapper.orderByDesc(Article::getIsTop)
               .orderByDesc(Article::getCreatedAt);
        
        // 分页查询
        Page<Article> page = new Page<>(current, size);
        Page<Article> articlePage = page(page, wrapper);
        
        // 转换为VO
        List<ArticleVO> articleVOList = articlePage.getRecords().stream()
            .map(article -> {
                ArticleVO vo = new ArticleVO();
                BeanUtils.copyProperties(article, vo);
                
                // 获取作者信息
                User author = userMapper.selectById(article.getAuthorId());
                if (author != null) {
                    vo.setAuthorName(author.getUsername());
                    vo.setAuthorAvatar(author.getAvatar());
                }
                
                // 获取分类信息
                List<CategoryVO> categories = categoryService.getCategoriesByArticleId(article.getId());
                
                // 获取标签信息
                List<TagVO> tags = tagService.getTagsByArticleId(article.getId());
                vo.setTags(tags);
                
                return vo;
            })
            .collect(Collectors.toList());
        
        return new PageResult<>(articleVOList, articlePage.getTotal(), current, size);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateArticleStatus(Long id, Integer status) {
        // 检查文章是否存在
        Article article = getById(id);
        if (article == null) {
            throw new ArticleException.ArticleNotFound();
        }
        
        // 检查状态值是否有效
        if (status < 0 || status > 2) {
            throw new ArticleOperationException.InvalidStatus("无效的文章状态: " + status);
        }
        
        // 更新状态
        Article updateArticle = new Article();
        updateArticle.setId(id);
        updateArticle.setStatus(status);
        updateById(updateArticle);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateArticleTop(Long id, Boolean isTop) {
        // 检查文章是否存在
        Article article = getById(id);
        if (article == null) {
            throw new ArticleException.ArticleNotFound();
        }
        
        // 更新置顶状态
        Article updateArticle = new Article();
        updateArticle.setId(id);
        updateArticle.setIsTop(isTop);
        updateById(updateArticle);
    }
} 