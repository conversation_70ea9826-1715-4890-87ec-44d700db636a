<template>
  <div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-sm p-6">
      <!-- 文章管理工具栏 -->
      <div class="flex justify-between items-center mb-6">
        <div class="flex items-center space-x-4">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索文章"
            class="w-64"
            :prefix-icon="Search"
            @input="handleSearch"
          />
          <el-select v-model="currentStatus" placeholder="文章状态" class="w-32" @change="handleStatusChange">
            <el-option label="全部" value="" />
            <el-option
              v-for="option in ARTICLE_STATUS_OPTIONS"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>
        <el-button type="primary" :icon="Edit" @click="handleCreateArticle">写文章</el-button>
      </div>

      <!-- 文章列表 -->
      <el-table :data="articles" v-loading="loading" style="width: 100%">
        <el-table-column prop="title" label="标题" min-width="300">
          <template #default="{ row }">
            <div class="flex items-center gap-4">
              <el-image
                v-if="row.cover"
                :src="getCoverUrl(row.cover)"
                class="w-16 h-12 object-cover rounded"
                fit="cover"
              />
              <span class="text-blue-600 hover:text-blue-800 cursor-pointer truncate" @click="handleEdit(row)">
                {{ row.title }}
              </span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="viewCount" label="浏览" width="80" align="center" />
        <el-table-column prop="commentCount" label="评论" width="80" align="center" />
        
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="ARTICLE_STATUS_TAG_TYPE[row.status]" size="small">
              {{ ARTICLE_STATUS_TEXT[row.status] }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createdAt" label="发布时间" width="160" align="center" />
        
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <div class="flex justify-center gap-2">
              <el-button
                v-if="row.status !== ARTICLE_STATUS.DISABLED"
                type="primary"
                size="small"
                plain
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                v-if="row.status === ARTICLE_STATUS.PUBLISHED"
                type="warning"
                size="small"
                plain
                @click="handleDisable(row)"
              >
                下架
              </el-button>
              <el-button
                type="danger"
                size="small"
                plain
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-between items-center mt-4">
        <span class="text-gray-600">
          共 {{ total }} 条
        </span>
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50]"
          layout="sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Edit } from '@element-plus/icons-vue'
import { getUserArticles, deleteArticle, updateArticle } from '@/api/article'
import { getCoverUrl } from '@/utils/image'
import { ARTICLE_STATUS, ARTICLE_STATUS_OPTIONS, ARTICLE_STATUS_TAG_TYPE, ARTICLE_STATUS_TEXT } from '@/constants/article'

const router = useRouter()

// 列表数据
const articles = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchKeyword = ref('')
const currentStatus = ref('')

// 加载文章列表
const loadArticles = async () => {
  loading.value = true
  try {
    const { data } = await getUserArticles({
      keyword: searchKeyword.value,
      status: currentStatus.value !== undefined ? currentStatus.value.toString() : '',
      page: currentPage.value,
      size: pageSize.value
    })
    articles.value = data.records
    total.value = data.total
  } catch (error) {
    console.error('加载文章列表失败:', error)
    ElMessage.error('加载文章列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  loadArticles()
}

// 状态筛选
const handleStatusChange = () => {
  currentPage.value = 1
  loadArticles()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadArticles()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadArticles()
}

// 创建文章
const handleCreateArticle = () => {
  router.push('/editor')
}

// 编辑文章
const handleEdit = (row: any) => {
  router.push(`/editor/${row.id}`)
}

// 下架文章
const handleDisable = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要下架这篇文章吗？', '提示', {
      type: 'warning'
    })
    
    await updateArticle(row.id, {
      ...row,
      status: ARTICLE_STATUS.DISABLED
    })
    
    ElMessage.success('文章已下架')
    loadArticles()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('下架文章失败:', error)
      ElMessage.error('下架文章失败')
    }
  }
}

// 删除文章
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这篇文章吗？', '提示', {
      type: 'warning'
    })
    
    await deleteArticle(row.id)
    ElMessage.success('删除成功')
    loadArticles()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文章失败:', error)
      ElMessage.error('删除文章失败')
    }
  }
}

onMounted(() => {
  loadArticles()
})
</script> 