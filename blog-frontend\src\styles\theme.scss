// 主题颜色变量
:root {
  // 浅色主题变量
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --text-primary: #1f2937;
  --text-secondary: #4b5563;
  --text-light: #9ca3af;
  --border-color: #e5e7eb;
  --shadow-color: rgba(0, 0, 0, 0.1);
}

// 深色主题变量
.dark {
  --primary-color: #60a5fa;
  --primary-hover: #3b82f6;
  --bg-primary: #1f2937;
  --bg-secondary: #111827;
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-light: #9ca3af;
  --border-color: #374151;
  --shadow-color: rgba(0, 0, 0, 0.3);

  // Element Plus 深色主题覆盖
  --el-bg-color: var(--bg-primary);
  --el-bg-color-overlay: var(--bg-secondary);
  --el-text-color-primary: var(--text-primary);
  --el-text-color-regular: var(--text-secondary);
  --el-border-color: var(--border-color);
  --el-border-color-light: var(--border-color);
  --el-fill-color-blank: var(--bg-primary);
}

// 字体大小
.text-small {
  --font-size-base: 14px;
  --font-size-sm: 12px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
}

.text-medium {
  --font-size-base: 16px;
  --font-size-sm: 14px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
}

.text-large {
  --font-size-base: 18px;
  --font-size-sm: 16px;
  --font-size-lg: 20px;
  --font-size-xl: 24px;
  --font-size-2xl: 28px;
}

// 应用字体大小
body {
  font-size: var(--font-size-base);
}

h1 { font-size: var(--font-size-2xl); }
h2 { font-size: var(--font-size-xl); }
h3 { font-size: var(--font-size-lg); }
h4 { font-size: var(--font-size-base); }
small { font-size: var(--font-size-sm); }

// 深色模式下的组件样式调整
.dark {
  .bg-white { background-color: var(--bg-primary); }
  .bg-gray-50 { background-color: var(--bg-secondary); }
  .text-gray-600 { color: var(--text-secondary); }
  .text-gray-500 { color: var(--text-light); }
  .border-gray-200 { border-color: var(--border-color); }
  
  .shadow-sm {
    box-shadow: 0 1px 2px 0 var(--shadow-color);
  }
  
  // Element Plus 组件深色模式调整
  .el-input__wrapper {
    background-color: var(--bg-secondary) !important;
  }
  
  .el-dropdown-menu {
    background-color: var(--bg-primary);
    border-color: var(--border-color);
  }
  
  .el-dropdown-menu__item:hover {
    background-color: var(--bg-secondary);
  }
} 