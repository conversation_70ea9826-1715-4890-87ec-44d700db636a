package com.modelbolt.blog.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.web.client.RestTemplate;
import javax.annotation.PostConstruct;
import org.springframework.context.annotation.DependsOn;

@Slf4j
@Configuration
public class RedisConfig {

    @Value("${spring.data.redis.host:localhost}")
    private String redisHost;

    @Value("${spring.data.redis.port:6379}")
    private int redisPort;

    @Value("${spring.data.redis.database:0}")
    private int database;

    @Value("${spring.data.redis.clear-on-start:true}")
    private boolean clearOnStart;

    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration(redisHost, redisPort);
        config.setDatabase(database);
        LettuceConnectionFactory factory = new LettuceConnectionFactory(config);
        factory.afterPropertiesSet(); // 初始化连接

        // 测试Redis连接
        try {
            factory.getConnection().ping();
            log.info("✅ Redis连接成功! 主机: {}, 端口: {}, 数据库: {}", redisHost, redisPort, database);

            // 如果需要清空数据库，在这里执行
            if (clearOnStart) {
                try {
                    RedisConnection connection = factory.getConnection();
                    connection.flushDb();
                    connection.close();
                    log.info("✅ Redis数据库{}已清空", database);
                } catch (Exception e) {
                    log.error("❌ Redis数据库清空失败: {}", e.getMessage());
                }
            }
        } catch (Exception e) {
            String errorMsg = String.format("❌ Redis连接失败! 主机: %s, 端口: %d, 数据库: %d, 错误: %s", 
                redisHost, redisPort, database, e.getMessage());
            log.error(errorMsg);
            throw new RuntimeException(errorMsg, e);
        }

        return factory;
    }

    @Bean
    @DependsOn("redisConnectionFactory")
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        try {
            RedisTemplate<String, Object> template = new RedisTemplate<>();
            template.setConnectionFactory(connectionFactory);

            // 使用Jackson2JsonRedisSerializer来序列化和反序列化redis的value值
            Jackson2JsonRedisSerializer<Object> serializer = new Jackson2JsonRedisSerializer<>(Object.class);

            ObjectMapper mapper = new ObjectMapper();
            // 指定要序列化的域，field,get和set,以及修饰符范围，ANY是都有包括private和public
            mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
            // 指定序列化输入的类型，类必须是非final修饰的，final修饰的类会报异常
            mapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
            // 添加JavaTimeModule来处理Java 8日期时间类型
            mapper.registerModule(new JavaTimeModule());

            serializer.setObjectMapper(mapper);

            // 值采用json序列化
            template.setValueSerializer(serializer);
            // 使用StringRedisSerializer来序列化和反序列化redis的key值
            template.setKeySerializer(new StringRedisSerializer());

            // 设置hash key 和value序列化模式
            template.setHashKeySerializer(new StringRedisSerializer());
            template.setHashValueSerializer(serializer);
            template.afterPropertiesSet();

            // 测试RedisTemplate
            template.opsForValue().get("test");
            log.info("✅ RedisTemplate 配置成功!");
            
            return template;
        } catch (Exception e) {
            String errorMsg = "❌ RedisTemplate 配置失败: " + e.getMessage();
            log.error(errorMsg);
            throw new RuntimeException(errorMsg, e);
        }
    }

    /**
     * 检查Redis连接状态的方法
     */
    public static boolean checkRedisConnection(RedisConnectionFactory connectionFactory) {
        try (RedisConnection connection = connectionFactory.getConnection()) {
            connection.ping();
            return true;
        } catch (Exception e) {
            log.error("❌ Redis连接检查失败: {}", e.getMessage());
            return false;
        }
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}