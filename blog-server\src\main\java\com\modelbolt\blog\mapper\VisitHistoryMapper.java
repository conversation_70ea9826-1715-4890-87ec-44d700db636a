package com.modelbolt.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.modelbolt.blog.model.entity.VisitHistory;
import com.modelbolt.blog.model.vo.MonitoringData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface VisitHistoryMapper extends BaseMapper<VisitHistory> {
    
    @Select("SELECT COALESCE(SUM(visit_count), 0) FROM visit_history " +
            "WHERE visit_date >= #{startDate} AND visit_date <= #{endDate}")
    Long sumVisitsByDateRange(@Param("startDate") LocalDate startDate, 
                            @Param("endDate") LocalDate endDate);
    
    @Select("SELECT * FROM visit_history " +
            "WHERE visit_date >= #{startDate} AND visit_date <= #{endDate} " +
            "ORDER BY visit_date DESC")
    List<VisitHistory> findByDateRange(@Param("startDate") LocalDate startDate, 
                                      @Param("endDate") LocalDate endDate);
    
    @Select("SELECT * FROM visit_history WHERE visit_date = #{date}")
    VisitHistory findByDate(@Param("date") LocalDate date);
    
    @Select("SELECT DATE_FORMAT(visit_date, '%Y-%m-%d') as page, SUM(visit_count) as views " +
            "FROM visit_history " +
            "WHERE visit_date >= #{startDate} AND visit_date <= #{endDate} " +
            "GROUP BY visit_date " +
            "ORDER BY views DESC " +
            "LIMIT #{limit}")
    List<MonitoringData.UserBehavior.PageView> findTopPages(@Param("startDate") LocalDate startDate,
                                                           @Param("endDate") LocalDate endDate,
                                                           @Param("limit") int limit);
} 