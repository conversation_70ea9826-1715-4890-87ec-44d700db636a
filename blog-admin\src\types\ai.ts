/**
 * AI写作请求类型
 */
export enum WritingType {
  ARTICLE_GENERATION = 'ARTICLE_GENERATION',
  ARTICLE_EDITING = 'ARTICLE_EDITING'
}

/**
 * AI写作请求参数
 */
export interface AiWritingRequest {
  type: WritingType
  keywords: string[]
  targetAudience?: string
  articleType?: string
  articleId?: number
  originalContent?: string
  step?: number
  selectedOutlineIndex?: number | null
}

/**
 * 文章创意
 */
export interface ArticleIdea {
  title: string
  outline: string
  summary: string
  keywords: string[]
}

/**
 * 文章
 */
export interface Article {
  title: string
  content: string
  summary: string
}

/**
 * AI写作响应
 */
export interface AiWritingResponse {
  type: WritingType
  ideas?: ArticleIdea[]
  article?: Article
  hasChanges?: boolean
  updates?: {
    title: string
    content: string
    summary: string
    diff?: string
  }
}

/**
 * 聊天消息
 */
export interface ChatMessage {
  role: 'user' | 'assistant'
  content: string
}

/**
 * 聊天请求
 */
export interface ChatRequest {
  message: string
  articleId?: number
  context: ChatMessage[]
  originalContent?: string
  originalTitle?: string
  originalSummary?: string
}

/**
 * 聊天响应
 */
export interface ChatResponse {
  content: string
  hasChanges?: boolean
  updates?: {
    title: string
    content: string
    summary: string
    diff?: string
  }
} 