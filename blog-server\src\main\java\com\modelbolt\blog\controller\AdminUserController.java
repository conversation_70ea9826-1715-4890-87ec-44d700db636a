package com.modelbolt.blog.controller;

import com.modelbolt.blog.common.ApiResponse;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.dto.user.AdminUserQuery;
import com.modelbolt.blog.model.dto.user.AdminUserVO;
import com.modelbolt.blog.model.dto.user.UpdateUserDTO;
import com.modelbolt.blog.model.dto.user.UpdateUserStatusDTO;
import com.modelbolt.blog.service.UserService;
import com.modelbolt.blog.annotation.OperationLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/admin/users")
@RequiredArgsConstructor
@Tag(name = "管理后台-用户管理", description = "管理后台用户管理相关接口")
@PreAuthorize("hasRole('ADMIN')")
public class AdminUserController {

    private final UserService userService;

    @Operation(summary = "获取用户列表")
    @GetMapping
    public ApiResponse<PageResult<AdminUserVO>> getUsers(AdminUserQuery query) {
        log.debug("Getting user list with query: {}", query);
        return ApiResponse.success(userService.getAdminUserList(query));
    }

    @Operation(summary = "更新用户信息")
    @PutMapping("/{id}")
    @OperationLog("更新用户信息")
    public ApiResponse<Void> updateUser(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @Valid @RequestBody UpdateUserDTO dto
    ) {
        log.debug("Updating user {}: {}", id, dto);
        userService.updateUserInfo(id, dto);
        return ApiResponse.success();
    }

    @Operation(summary = "更新用户状态")
    @PutMapping("/{id}/status")
    @OperationLog("更新用户状态")
    public ApiResponse<Void> updateUserStatus(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @Valid @RequestBody UpdateUserStatusDTO dto
    ) {
        log.debug("Updating user status {}: {}", id, dto);
        userService.updateUserStatus(id, dto.getStatus());
        return ApiResponse.success();
    }

    @Operation(summary = "导出用户数据")
    @GetMapping("/export")
    @OperationLog("导出用户数据")
    public void exportUsers(AdminUserQuery query, HttpServletResponse response) {
        log.debug("Starting user export with query: {}", query);
        try {
            userService.exportUsers(query, response);
            log.debug("User export completed successfully");
        } catch (Exception e) {
            log.error("Failed to export users. Error: {}", e.getMessage(), e);
            throw e;
        }
    }
} 