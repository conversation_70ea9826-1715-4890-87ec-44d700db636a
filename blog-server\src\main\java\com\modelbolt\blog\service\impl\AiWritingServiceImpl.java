package com.modelbolt.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.modelbolt.blog.exception.BusinessException;
import com.modelbolt.blog.model.entity.AiChatHistory;
import com.modelbolt.blog.model.entity.AiUsageStats;
import com.modelbolt.blog.model.entity.User;
import com.modelbolt.blog.mapper.AiChatHistoryMapper;
import com.modelbolt.blog.mapper.AiUsageStatsMapper;
import com.modelbolt.blog.mapper.UserMapper;
import com.modelbolt.blog.model.dto.ai.*;
import com.modelbolt.blog.service.AiWritingService;
import com.modelbolt.blog.service.SystemConfigService;
import com.modelbolt.blog.utils.RedisCache;
import com.modelbolt.blog.config.AiConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
@RequiredArgsConstructor
public class AiWritingServiceImpl implements AiWritingService {

    private final SystemConfigService systemConfigService;
    private final AiChatHistoryMapper aiChatHistoryMapper;
    private final AiUsageStatsMapper aiUsageStatsMapper;
    private final UserMapper userMapper;
    private final RedisCache redisCache;
    private final RestTemplate restTemplate;
    private final AiConfig aiConfig;

    private static final String CACHE_PREFIX = "ai:writing:";
    private static final int CACHE_DURATION = 3600; // 1小时

    @Override
    public AiWritingResponse processWritingRequest(AiWritingRequest request) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        if (userId == null) {
            throw new BusinessException("用户未登录");
        }

        // 获取AI配置
        validateAiConfig();

        // 生成会话ID
        String sessionId = generateSessionId(request);
        log.info("处理AI写作请求 | 用户ID: {} | 会话ID: {} | 步骤: {} | 类型: {}", 
                userId, sessionId, request.getStep(), request.getType());

        // 检查缓存
        String cacheKey = CACHE_PREFIX + sessionId;
        AiWritingResponse cachedResponse = redisCache.getCacheObject(cacheKey);
        
        // 第二步时不使用缓存，直接生成新内容
        if (request.getStep() == 2) {
            log.info("第二步：生成完整文章 | 选中大纲索引: {}", request.getSelectedOutlineIndex());
            cachedResponse = null;
        } else if (cachedResponse != null) {
            log.info("找到缓存的响应 | 会话ID: {} | 响应类型: {}", sessionId, cachedResponse.getType());
            return cachedResponse;
        }

        // 构建提示词
        String prompt = buildPrompt(request);
        log.debug("生成的提示词: {}", prompt);

        try {
            // 构建请求体
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", prompt);

            List<Map<String, Object>> messages = new ArrayList<>();
            messages.add(message);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", aiConfig.getModel());
            requestBody.put("messages", messages);
            requestBody.put("temperature", aiConfig.getTemperature());
            requestBody.put("max_tokens", aiConfig.getMaxTokens());
            requestBody.put("stream", false);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(aiConfig.getApiKey());

            // 发送请求
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            log.info("调用AI服务 | 端点: {} | 模型: {}", aiConfig.getApiEndpoint(), aiConfig.getModel());
            
            String apiResponse = restTemplate.postForObject(
                aiConfig.getApiEndpoint(),
                requestEntity,
                String.class
            );
            
            log.debug("AI服务响应: {}", apiResponse);

            // 解析响应
            AiWritingResponse response = parseResponse(request.getType(), apiResponse, request);
            
            // 只缓存第一步的响应
            if (request.getStep() == 1) {
                redisCache.setCacheObject(cacheKey, response, CACHE_DURATION);
                log.info("缓存第一步响应 | 会话ID: {}", sessionId);
            }

            // 记录对话历史
            recordChatHistory(userId, sessionId, prompt, apiResponse);
            log.info("AI响应处理完成 | 会话ID: {} | 步骤: {}", sessionId, request.getStep());

            return response;
        } catch (Exception e) {
            log.error("AI服务调用失败 | 错误: {} | 会话ID: {} | 步骤: {}", 
                    e.getMessage(), sessionId, request.getStep(), e);
            throw new BusinessException("AI服务调用失败: " + e.getMessage());
        }
    }

    @Override
    public boolean validateUserQuota(Long userId) {
        LocalDate today = LocalDate.now();

        // 获取今日使用统计
        AiUsageStats stats = aiUsageStatsMapper.findByUserIdAndDate(userId, today);
        if (stats == null) {
            return true;
        }

        // 检查请求次数和token限制
        return stats.getTotalRequests() < aiConfig.getRequestLimit() &&
               stats.getTotalTokens() < aiConfig.getTokenLimit();
    }

    @Override
    public void recordUsage(Long userId, int tokensUsed) {
        LocalDate today = LocalDate.now();
        AiUsageStats stats = aiUsageStatsMapper.findByUserIdAndDate(userId, today);

        if (stats == null) {
            // 创建新的统计记录
            stats = new AiUsageStats();
            stats.setUserId(userId);
            stats.setDate(today);
            stats.setTotalTokens(tokensUsed);
            stats.setTotalRequests(1);
            aiUsageStatsMapper.insert(stats);
        } else {
            // 更新现有统计记录
            stats.setTotalTokens(stats.getTotalTokens() + tokensUsed);
            stats.setTotalRequests(stats.getTotalRequests() + 1);
            aiUsageStatsMapper.updateById(stats);
        }
    }

    private void validateAiConfig() {
        if (aiConfig.getApiKey() == null || aiConfig.getApiKey().isEmpty()) {
            throw new BusinessException("AI API密钥未配置");
        }
        if (aiConfig.getApiEndpoint() == null || aiConfig.getApiEndpoint().isEmpty()) {
            throw new BusinessException("AI API地址未配置");
        }
    }

    private String generateSessionId(AiWritingRequest request) {
        return switch (request.getType()) {
            case ARTICLE_GENERATION -> {
                String key = String.join("_", request.getKeywords());
                if (request.getTargetAudience() != null) {
                    key += "_" + request.getTargetAudience();
                }
                if (request.getArticleType() != null) {
                    key += "_" + request.getArticleType();
                }
                yield "gen_" + key.hashCode();
            }
            case ARTICLE_EDITING -> "edit_" + request.getArticleId();
        };
    }

    private String buildPrompt(AiWritingRequest request) {
        return switch (request.getType()) {
            case ARTICLE_GENERATION -> buildArticleGenerationPrompt(request);
            case ARTICLE_EDITING -> buildArticleEditingPrompt(request);
        };
    }

    private String buildArticleGenerationPrompt(AiWritingRequest request) {
        StringBuilder prompt = new StringBuilder();
        
        if (request.getStep() == 1) {
            prompt.append("请根据以下信息生成3个文章创意大纲：\n");
            prompt.append("关键词：").append(String.join("、", request.getKeywords())).append("\n");
            
            if (request.getTargetAudience() != null) {
                prompt.append("目标受众：").append(request.getTargetAudience()).append("\n");
            }
            if (request.getArticleType() != null) {
                prompt.append("文章类型：").append(request.getArticleType()).append("\n");
            }

            prompt.append("\n请为每个创意提供以下信息，使用Markdown格式：\n");
            prompt.append("### 创意1\n");
            prompt.append("**标题**：[标题文本]\n");
            prompt.append("**大纲**：[详细的文章大纲，包含主要章节和要点]\n");
            prompt.append("**摘要**：[100字左右的文章摘要]\n");
            prompt.append("**关键词**：[3-5个关键词]\n\n");
            prompt.append("对创意2和3重复相同格式。\n");
        } else if (request.getStep() == 2 && request.getSelectedOutlineIndex() != null) {
            // 获取选中的创意
            String cacheKey = CACHE_PREFIX + generateSessionId(request);
            AiWritingResponse cachedResponse = redisCache.getCacheObject(cacheKey);
            if (cachedResponse == null || cachedResponse.getIdeas() == null || 
                request.getSelectedOutlineIndex() >= cachedResponse.getIdeas().size()) {
                throw new BusinessException("未找到选中的创意大纲");
            }
            
            AiWritingResponse.ArticleIdea selectedIdea = cachedResponse.getIdeas().get(request.getSelectedOutlineIndex());
            
            prompt.append("请根据以下大纲生成一篇完整的Markdown格式文章。\n\n");
            prompt.append("标题：").append(selectedIdea.getTitle()).append("\n\n");
            prompt.append("大纲：\n").append(selectedIdea.getOutline()).append("\n\n");
            prompt.append("目标受众：").append(request.getTargetAudience()).append("\n");
            
            prompt.append("要求：\n");
            prompt.append("1. 严格按照大纲展开，每个章节都要有充分的内容\n");
            prompt.append("2. 使用Markdown格式，包含适当的标题层级(#)和格式标记\n");
            prompt.append("3. 保持专业性的同时确保可读性\n");
            prompt.append("4. 适当使用示例和实际案例来解释概念\n");
            prompt.append("5. 每个部分的内容要详实、准确\n");
            prompt.append("6. 文章最后要有总结或小结\n\n");
            
            prompt.append("请按以下格式返回：\n");
            prompt.append("# [文章标题]\n\n");
            prompt.append("[文章完整内容，使用Markdown格式]\n\n");
            prompt.append("---\n");
            prompt.append("摘要：[100字左右的文章摘要]");
        }
        
        return prompt.toString();
    }

    private String buildArticleEditingPrompt(AiWritingRequest request) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("你是一个专业的文章编辑助手。请根据用户的要求，帮助调整和优化文章内容。\n\n");
        
        if (request.getOriginalContent() != null) {
            prompt.append("当前文章内容：\n\n");
            prompt.append(request.getOriginalContent()).append("\n\n");
            prompt.append("请根据用户的要求对文章进行调整。调整后的内容请使用以下格式：\n");
            prompt.append("===更新内容===\n");
            prompt.append("标题：[新标题]\n");
            prompt.append("正文：[新正文]\n");
            prompt.append("摘要：[新摘要]\n");
            prompt.append("===差异说明===\n");
            prompt.append("[请说明主要修改的内容]\n\n");
        }
        
        return prompt.toString();
    }

    private AiWritingResponse parseResponse(AiWritingRequest.WritingType type, String apiResponse, AiWritingRequest request) {
        log.debug("开始解析AI响应 | 类型: {} | 步骤: {}", type, request.getStep());
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(apiResponse);
            
            if (root.has("error")) {
                JsonNode error = root.get("error");
                String errorMessage = error.get("message").asText();
                log.error("AI服务返回错误 | 错误信息: {}", errorMessage);
                throw new BusinessException("AI服务调用失败: " + errorMessage);
            }
            
            String generatedText = root.get("choices").get(0).get("message").get("content").asText();
            log.debug("生成的文本内容: {}", generatedText);
            
            AiWritingResponse response = new AiWritingResponse();
            response.setType(type);
            
            switch (type) {
                case ARTICLE_GENERATION:
                    if (request.getStep() == 1) {
                        response.setIdeas(parseIdeaGeneration(generatedText));
                        log.info("解析创意生成结果 | 创意数量: {}", response.getIdeas().size());
                    } else if (request.getStep() == 2) {
                        response.setArticle(parseGeneratedArticle(generatedText));
                        log.info("解析生成的文章 | 标题: {}", response.getArticle().getTitle());
                    }
                    break;
                case ARTICLE_EDITING:
                    // 检查是否包含更新内容标记
                    if (generatedText.contains("===更新内容===")) {
                        response.setHasChanges(true);
                        AiWritingResponse.Updates updates = new AiWritingResponse.Updates();
                        
                        // 解析标题
                        Matcher titleMatcher = Pattern.compile("标题：(.*?)(?=\\n|$)").matcher(generatedText);
                        if (titleMatcher.find()) {
                            updates.setTitle(titleMatcher.group(1).trim());
                        }
                        
                        // 解析正文
                        Matcher contentMatcher = Pattern.compile("正文：(.*?)(?=\\n摘要：|===差异说明===|$)", Pattern.DOTALL).matcher(generatedText);
                        if (contentMatcher.find()) {
                            updates.setContent(contentMatcher.group(1).trim());
                        }
                        
                        // 解析摘要
                        Matcher summaryMatcher = Pattern.compile("摘要：(.*?)(?=\\n===差异说明===|$)", Pattern.DOTALL).matcher(generatedText);
                        if (summaryMatcher.find()) {
                            updates.setSummary(summaryMatcher.group(1).trim());
                        }
                        
                        // 解析差异说明
                        Matcher diffMatcher = Pattern.compile("===差异说明===\\n(.*?)(?=\\n===|$)", Pattern.DOTALL).matcher(generatedText);
                        if (diffMatcher.find()) {
                            updates.setDiff(diffMatcher.group(1).trim());
                        }
                        
                        response.setUpdates(updates);
                    } else {
                        // 如果没有更新内容标记，则作为普通响应处理
                        response.setHasChanges(false);
                        response.setArticle(parseGeneratedArticle(generatedText));
                    }
                    break;
            }
            
            return response;
        } catch (Exception e) {
            log.error("解析AI响应失败: {}", e.getMessage(), e);
            throw new BusinessException("解析AI响应失败: " + e.getMessage());
        }
    }

    private List<AiWritingResponse.ArticleIdea> parseIdeaGeneration(String text) {
        List<AiWritingResponse.ArticleIdea> ideas = new ArrayList<>();
        
        // 按创意分割文本
        String[] sections = text.split("(?=###\\s*创意\\d+)");
        
        for (String section : sections) {
            if (section.trim().isEmpty()) continue;
            
            AiWritingResponse.ArticleIdea idea = new AiWritingResponse.ArticleIdea();
            
            // 解析标题
            Matcher titleMatcher = Pattern.compile("\\*\\*标题\\*\\*[：:](.*?)(?=\\*\\*|$)", Pattern.DOTALL).matcher(section);
            if (titleMatcher.find()) {
                idea.setTitle(titleMatcher.group(1).trim().replaceAll("[《》]", ""));
            }
            
            // 解析大纲
            Matcher outlineMatcher = Pattern.compile("\\*\\*大纲\\*\\*[：:](.*?)(?=\\*\\*摘要|$)", Pattern.DOTALL).matcher(section);
            if (outlineMatcher.find()) {
                idea.setOutline(outlineMatcher.group(1).trim());
            }
            
            // 解析摘要
            Matcher summaryMatcher = Pattern.compile("\\*\\*摘要\\*\\*[：:](.*?)(?=\\*\\*关键词|$)", Pattern.DOTALL).matcher(section);
            if (summaryMatcher.find()) {
                idea.setSummary(summaryMatcher.group(1).trim());
            }
            
            // 解析关键词
            Matcher keywordsMatcher = Pattern.compile("\\*\\*关键词\\*\\*[：:](.*?)(?=\\*\\*|$)", Pattern.DOTALL).matcher(section);
            if (keywordsMatcher.find()) {
                String[] keywords = keywordsMatcher.group(1).trim().split("[,，、]");
                idea.setKeywords(Arrays.asList(keywords));
            }
            
            if (idea.getTitle() != null) {  // 只添加成功解析了标题的创意
                ideas.add(idea);
            }
        }
        
        return ideas;
    }

    private AiWritingResponse.Article parseGeneratedArticle(String text) {
        AiWritingResponse.Article article = new AiWritingResponse.Article();
        
        try {
            // 解析标题 (匹配 # 开头的第一行)
            Matcher titleMatcher = Pattern.compile("^#\\s+(.+?)\\s*$", Pattern.MULTILINE).matcher(text);
            if (titleMatcher.find()) {
                article.setTitle(titleMatcher.group(1).trim());
            }
            
            // 解析正文 (从第一个标题后到摘要前的所有内容)
            Matcher contentMatcher = Pattern.compile("^#\\s+.+?\\n(.+?)\\n---\\n", Pattern.DOTALL).matcher(text);
            if (contentMatcher.find()) {
                article.setContent(contentMatcher.group(1).trim());
            }
            
            // 解析摘要 (在 --- 分隔符后的内容)
            Matcher summaryMatcher = Pattern.compile("---\\n摘要：(.+?)$", Pattern.DOTALL).matcher(text);
            if (summaryMatcher.find()) {
                article.setSummary(summaryMatcher.group(1).trim());
            }
            
            // 如果没有找到内容，记录警告日志
            if (article.getTitle() == null || article.getContent() == null || article.getSummary() == null) {
                log.warn("部分内容解析失败 | 标题: {} | 内容: {} | 摘要: {}", 
                    article.getTitle() != null, 
                    article.getContent() != null, 
                    article.getSummary() != null);
            }
        } catch (Exception e) {
            log.error("解析文章内容时发生错误", e);
        }
        
        return article;
    }

    private void recordChatHistory(Long userId, String sessionId, String prompt, String response) {
        AiChatHistory userMessage = new AiChatHistory();
        userMessage.setUserId(userId);
        userMessage.setSessionId(sessionId);
        userMessage.setRole("user");
        userMessage.setContent(prompt);
        userMessage.setCreatedAt(LocalDateTime.now());
        aiChatHistoryMapper.insert(userMessage);

        AiChatHistory assistantMessage = new AiChatHistory();
        assistantMessage.setUserId(userId);
        assistantMessage.setSessionId(sessionId);
        assistantMessage.setRole("assistant");
        assistantMessage.setContent(response);
        assistantMessage.setCreatedAt(LocalDateTime.now());
        aiChatHistoryMapper.insert(assistantMessage);
    }

    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() && 
            !(authentication instanceof AnonymousAuthenticationToken)) {
            try {
                String username = authentication.getName();
                // 通过用户名查询用户ID
                User user = userMapper.selectOne(
                    new LambdaQueryWrapper<User>()
                        .eq(User::getUsername, username)
                );
                if (user != null) {
                    return user.getId();
                }
            } catch (Exception e) {
                log.warn("Failed to get user id for username: {}", authentication.getName(), e);
            }
            log.warn("Unable to extract userId from authentication");
        }
        return null;
    }

    @Override
    public ChatResponse processChat(ChatRequest request) {
        // 获取当前用户ID
        Long userId = getCurrentUserId();
        if (userId == null) {
            throw new BusinessException("用户未登录");
        }

        // 获取AI配置
        validateAiConfig();

        try {
            // 构建提示词
            StringBuilder prompt = new StringBuilder();
            prompt.append("你是一个专业的文章编辑助手。请根据用户的要求，帮助调整和优化文章内容。\n\n");
            
            // 添加文章上下文
            if (request.getOriginalContent() != null) {
                log.debug("添加原始文章内容到提示词 | 标题长度: {} | 内容长度: {} | 摘要长度: {}", 
                    request.getOriginalTitle() != null ? request.getOriginalTitle().length() : 0,
                    request.getOriginalContent().length(),
                    request.getOriginalSummary() != null ? request.getOriginalSummary().length() : 0);

                prompt.append("当前文章内容：\n");
                if (request.getOriginalTitle() != null) {
                    prompt.append("标题：").append(request.getOriginalTitle()).append("\n\n");
                }
                prompt.append("正文：\n").append(request.getOriginalContent()).append("\n\n");
                if (request.getOriginalSummary() != null) {
                    prompt.append("摘要：").append(request.getOriginalSummary()).append("\n\n");
                }
                prompt.append("请根据以下要求对文章进行调整。调整后的内容请使用以下格式：\n");
                prompt.append("===更新内容===\n");
                prompt.append("标题：[新标题]\n");
                prompt.append("正文：[新正文]\n");
                prompt.append("摘要：[新摘要]\n");
                prompt.append("===差异说明===\n");
                prompt.append("[请说明主要修改的内容]\n\n");
            } else {
                log.warn("未收到原始文章内容");
                prompt.append("请根据用户的要求提供建议。\n\n");
            }
            
            // 添加历史对话上下文
            if (request.getContext() != null && !request.getContext().isEmpty()) {
                for (ChatMessage message : request.getContext()) {
                    prompt.append(message.getRole()).append(": ").append(message.getContent()).append("\n");
                }
            }
            
            // 添加用户的新消息
            prompt.append("user: ").append(request.getMessage()).append("\n");
            prompt.append("assistant: ");

            // 构建请求体
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", prompt.toString());

            List<Map<String, Object>> messages = new ArrayList<>();
            messages.add(message);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", aiConfig.getModel());
            requestBody.put("messages", messages);
            requestBody.put("temperature", 0.7);
            requestBody.put("max_tokens", 2000); // 增加token限制以处理更长的文章
            requestBody.put("stream", false);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(aiConfig.getApiKey());

            // 发送请求
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            String apiResponse = restTemplate.postForObject(
                aiConfig.getApiEndpoint(),
                requestEntity,
                String.class
            );

            // 解析响应
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(apiResponse);
            
            if (root.has("error")) {
                JsonNode error = root.get("error");
                String errorMessage = error.get("message").asText();
                throw new BusinessException("AI服务调用失败: " + errorMessage);
            }
            
            String content = root.get("choices").get(0).get("message").get("content").asText();

            // 生成会话ID
            String sessionId = generateChatSessionId(request);
            
            // 记录对话历史
            recordChatHistory(userId, sessionId, request.getMessage(), content);

            // 解析响应
            ChatResponse response = new ChatResponse();
            response.setContent(content);

            // 检查是否包含内容更新标记
            if (content.contains("===更新内容===")) {
                ChatResponse.Updates updates = new ChatResponse.Updates();
                response.setHasChanges(true);
                
                // 解析标题更新
                Matcher titleMatcher = Pattern.compile("标题：(.*?)(?=\\n|$)").matcher(content);
                if (titleMatcher.find()) {
                    updates.setTitle(titleMatcher.group(1).trim());
                }
                
                // 解析正文更新
                Matcher contentMatcher = Pattern.compile("正文：(.*?)(?=\\n摘要：|===差异说明===|$)", Pattern.DOTALL).matcher(content);
                if (contentMatcher.find()) {
                    updates.setContent(contentMatcher.group(1).trim());
                }
                
                // 解析摘要更新
                Matcher summaryMatcher = Pattern.compile("摘要：(.*?)(?=\\n===差异说明===|$)", Pattern.DOTALL).matcher(content);
                if (summaryMatcher.find()) {
                    updates.setSummary(summaryMatcher.group(1).trim());
                }

                // 解析差异说明
                Matcher diffMatcher = Pattern.compile("===差异说明===\\n(.*?)(?=\\n===|$)", Pattern.DOTALL).matcher(content);
                if (diffMatcher.find()) {
                    updates.setDiff(diffMatcher.group(1).trim());
                }
                
                response.setUpdates(updates);
            } else {
                response.setHasChanges(false);
            }

            return response;
        } catch (Exception e) {
            log.error("处理聊天请求失败", e);
            throw new BusinessException("处理聊天请求失败: " + e.getMessage());
        }
    }

    // 生成聊天会话ID的辅助方法
    private String generateChatSessionId(ChatRequest request) {
        // 如果有文章ID，使用文章ID作为会话ID的一部分
        if (request.getArticleId() != null) {
            return "chat_" + request.getArticleId();
        }
        // 如果没有文章ID，使用时间戳生成唯一会话ID
        return "chat_" + System.currentTimeMillis();
    }
} 