package com.modelbolt.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.modelbolt.blog.model.dto.comment.CommentQuery;
import com.modelbolt.blog.model.dto.comment.CommentVO;
import com.modelbolt.blog.model.entity.Comment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface CommentMapper extends BaseMapper<Comment> {
    @Select("SELECT COUNT(*) FROM comment")
    int count();

    @Select("SELECT COUNT(*) FROM comment WHERE DATE(created_at) = #{date}")
    int getCommentCountByDate(LocalDate date);

    @Update("UPDATE comment SET reply_count = reply_count + 1 WHERE id = #{id}")
    int incrementReplyCount(Long id);

    @Update("UPDATE comment SET reply_count = reply_count - 1 WHERE id = #{id}")
    int decrementReplyCount(Long id);

    @Update("UPDATE comment SET like_count = like_count + 1 WHERE id = #{id}")
    int incrementLikeCount(Long id);

    @Update("UPDATE comment SET like_count = like_count - 1 WHERE id = #{id}")
    int decrementLikeCount(Long id);

    /**
     * 分页查询评论列表
     */
    @Select("<script>" +
            "SELECT c.*, a.title as articleTitle, u.username as userName, u.avatar as userAvatar " +
            "FROM comment c " +
            "LEFT JOIN article a ON c.article_id = a.id " +
            "LEFT JOIN user u ON c.user_id = u.id " +
            "WHERE 1=1 " +
            "<if test='query.status != null'> AND c.status = #{query.status} </if> " +
            "<if test='query.keyword != null and query.keyword != \"\"'> " +
            "   AND (c.content LIKE CONCAT('%', #{query.keyword}, '%') " +
            "   OR a.title LIKE CONCAT('%', #{query.keyword}, '%')) " +
            "</if> " +
            "<if test='query.articleId != null'> AND c.article_id = #{query.articleId} </if> " +
            "<if test='query.userId != null'> AND c.user_id = #{query.userId} </if> " +
            "ORDER BY c.created_at DESC " +
            "LIMIT #{offset}, #{size}" +
            "</script>")
    List<CommentVO> selectCommentPage(@Param("query") CommentQuery query, @Param("offset") long offset, @Param("size") long size);

    /**
     * 统计符合条件的评论总数
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM comment c " +
            "LEFT JOIN article a ON c.article_id = a.id " +
            "LEFT JOIN user u ON c.user_id = u.id " +
            "WHERE 1=1 " +
            "<if test='query.status != null'> AND c.status = #{query.status} </if> " +
            "<if test='query.keyword != null and query.keyword != \"\"'> " +
            "   AND (c.content LIKE CONCAT('%', #{query.keyword}, '%') " +
            "   OR a.title LIKE CONCAT('%', #{query.keyword}, '%')) " +
            "</if> " +
            "<if test='query.articleId != null'> AND c.article_id = #{query.articleId} </if> " +
            "<if test='query.userId != null'> AND c.user_id = #{query.userId} </if> " +
            "</script>")
    Long selectCommentCount(@Param("query") CommentQuery query);

    /**
     * 更新评论状态
     */
    @Update("UPDATE comment SET status = #{status} WHERE id = #{id}")
    void updateStatus(@Param("id") Long id, @Param("status") Integer status);
} 