<template>
  <div class="min-h-screen flex flex-col bg-gray-50">
    <!-- 顶部导航 -->
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50 shadow-sm">
      <div class="container mx-auto px-4">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <router-link to="/" class="flex items-center">
            <span class="text-2xl font-bold text-blue-600">ModelBolt</span>
          </router-link>

          <!-- 导航菜单 -->
          <nav class="hidden md:flex space-x-8">
            <router-link
              v-for="item in navItems"
              :key="item.path"
              :to="item.path"
              class="relative px-3 py-2 text-sm font-medium transition-all duration-300 hover:text-blue-600 group"
              :class="[
                (item.path === '/' && ($route.path === '/' || $route.path === '/home')) || 
                (item.path !== '/' && $route.path.startsWith(item.path))
                  ? 'text-blue-600 font-semibold' 
                  : 'text-gray-600'
              ]"
            >
              {{ item.name }}
              <!-- 活动指示器 -->
              <div 
                class="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600 transform origin-left transition-all duration-300"
                :class="[
                  (item.path === '/' && ($route.path === '/' || $route.path === '/home')) || 
                  (item.path !== '/' && $route.path.startsWith(item.path))
                    ? 'scale-x-100 opacity-100' 
                    : 'scale-x-0 opacity-0 group-hover:scale-x-100 group-hover:opacity-50'
                ]"
              ></div>
            </router-link>
          </nav>

          <!-- 用户菜单 -->
          <div class="flex items-center space-x-4">
            <!-- PC端搜索框 -->
            <div class="relative hidden md:block">
              <el-autocomplete
                v-model="searchStore.keyword"
                :fetch-suggestions="handleSearch"
                placeholder="搜索文章..."
                :trigger-on-focus="false"
                class="custom-search-input"
                @select="handleSelect"
                @focus="showSearchPanel = true"
                @blur="handleSearchBlur"
                @keyup.enter="handleSearch"
                :value-key="'title'"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
                
                <template #default="{ item }">
                  <div class="suggestion-item">
                    <span v-html="highlightKeyword(item.title || item.name, searchStore.keyword)"></span>
                    <span class="text-xs text-gray-400 ml-2">{{ item.type === 'article' ? '文章' : item.type === 'tag' ? '标签' : '分类' }}</span>
                  </div>
                </template>
              </el-autocomplete>
              
              <!-- 搜索面板 -->
              <div v-show="showSearchPanel" class="search-panel">
                <!-- 搜索历史 -->
                <div v-if="searchStore.history.length" class="p-4">
                  <div class="flex justify-between items-center mb-2">
                    <span class="text-sm text-gray-500">搜索历史</span>
                    <el-button type="primary" link @click="searchStore.clearHistory">清除</el-button>
                  </div>
                  <div class="flex flex-wrap gap-2">
                    <el-tag
                      v-for="item in searchStore.history"
                      :key="item.timestamp"
                      size="small"
                      class="cursor-pointer"
                      @click="handleHistoryClick(item.keyword)"
                    >
                      {{ item.keyword }}
                    </el-tag>
                  </div>
                </div>
                
                <!-- 热门搜索 -->
                <div class="p-4" :class="{ 'border-t': searchStore.history.length }">
                  <div class="text-sm text-gray-500 mb-2">热门搜索</div>
                  <div class="flex flex-wrap gap-2">
                    <el-tag
                      v-for="(item, index) in searchStore.hotSearches"
                      :key="item.keyword"
                      :type="index === 0 ? 'danger' : index === 1 ? 'warning' : index === 2 ? 'success' : 'info'"
                      size="small"
                      class="cursor-pointer hover:scale-105 transition-transform"
                      @click="handleHotSearchClick(item.keyword)"
                    >
                      <template #prefix>
                        <span v-if="index < 3" class="mr-1 font-bold">{{ index + 1 }}</span>
                      </template>
                      {{ item.keyword }}
                      <template #suffix>
                        <span class="ml-1 text-xs opacity-75">{{ item.count }}</span>
                      </template>
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>

            <template v-if="userStore.isAuthenticated && userStore.userInfo">
              <!-- 通知图标 - 根据设置显示/隐藏 -->
              <router-link 
                v-if="settingsStore.settings.notificationEnabled"
                to="/user/messages" 
                class="text-gray-400 hover:text-gray-600 relative"
                @click="handleNotificationClick"
              >
                <el-icon class="text-xl"><Bell /></el-icon>
                <span 
                  v-if="notificationStore.hasUnread"
                  class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full text-white text-xs flex items-center justify-center"
                >
                  {{ notificationStore.unreadCount }}
                </span>
              </router-link>

              <el-dropdown trigger="click" :teleported="false" :role="null">
                <div class="flex items-center space-x-2 cursor-pointer" tabindex="0" role="button">
                  <el-avatar 
                    :size="32"
                    :src="getRefreshedImageUrl(getAvatarUrl(userStore.userInfo?.avatar))"
                    @error="imgError"
                  >
                    {{ userStore.userInfo?.username?.charAt(0)?.toUpperCase() }}
                  </el-avatar>
                  <span class="text-sm text-gray-700">{{ userStore.userInfo?.username }}</span>
                </div>
                <template #dropdown>
                  <el-dropdown-menu :role="null" class="user-dropdown-menu">
                    <el-dropdown-item :role="null">
                      <router-link to="/user/profile" class="flex items-center">
                        <el-icon class="mr-2"><User /></el-icon>个人资料
                      </router-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <router-link to="/user/articles" class="flex items-center">
                        <el-icon class="mr-2"><Document /></el-icon>我的文章
                      </router-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <router-link to="/user/comments" class="flex items-center">
                        <el-icon class="mr-2"><ChatDotRound /></el-icon>我的评论
                      </router-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <router-link to="/user/favorites" class="flex items-center">
                        <el-icon class="mr-2"><Star /></el-icon>我的收藏
                      </router-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <router-link to="/user/follows" class="flex items-center">
                        <el-icon class="mr-2"><UserFilled /></el-icon>关注管理
                      </router-link>
                    </el-dropdown-item>
                    <el-dropdown-item v-if="settingsStore.settings.notificationEnabled">
                      <router-link to="/user/messages" class="flex items-center">
                        <el-icon class="mr-2"><Bell /></el-icon>消息通知
                      </router-link>
                    </el-dropdown-item>
                    <el-dropdown-item divided>
                      <button @click="handleLogout" class="flex items-center text-red-600 w-full">
                        <el-icon class="mr-2"><SwitchButton /></el-icon>退出登录
                      </button>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>

              <!-- 主题切换按钮 -->
              <el-button
                class="theme-button"
                circle
                @click="handleThemeClick"
              >
                <el-icon>
                  <component :is="settingsStore.settings.theme === 'dark' ? Moon : Sunny" />
                </el-icon>
              </el-button>
            </template>
            <template v-else>
              <router-link
                to="/auth/login"
                class="text-gray-600 hover:text-blue-600 text-sm font-medium"
              >
                登录
              </router-link>
              <router-link
                to="/auth/register"
                class="bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-full text-sm font-medium"
              >
                注册
              </router-link>
            </template>
          </div>

          <!-- 移动端菜单按钮 -->
          <button 
            class="md:hidden text-gray-500 hover:text-gray-600"
            @click="isMobileMenuOpen = !isMobileMenuOpen"
          >
            <i :class="isMobileMenuOpen ? 'el-icon-close' : 'el-icon-menu'" class="text-xl"></i>
          </button>
        </div>

        <!-- 移动端菜单 -->
        <div 
          v-show="isMobileMenuOpen"
          class="md:hidden py-2 space-y-1"
        >
          <!-- 移动端搜索框 -->
          <div class="px-3 py-2">
            <div class="relative">
              <input
                type="text"
                placeholder="搜索文章..."
                class="w-full pl-10 pr-4 py-2 text-sm bg-gray-50 border border-gray-200 rounded-full focus:outline-none focus:border-blue-300 focus:ring-2 focus:ring-blue-100"
              />
              <i class="el-icon-search absolute left-3 top-2.5 text-gray-400"></i>
            </div>
          </div>

          <router-link
            v-for="item in navItems"
            :key="item.path"
            :to="item.path"
            class="block px-4 py-2 text-base font-medium transition-all duration-300"
            :class="[
              (item.path === '/' && ($route.path === '/' || $route.path === '/home')) || 
              (item.path !== '/' && $route.path.startsWith(item.path))
                ? 'text-blue-600 bg-blue-50 border-l-4 border-blue-600' 
                : 'text-gray-600 hover:text-blue-600 hover:bg-gray-50'
            ]"
          >
            {{ item.name }}
          </router-link>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="flex-1 bg-gray-50">
      <router-view />
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-200">
      <div class="container mx-auto px-4 py-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <!-- 关于博主 -->
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">关于博主</h3>
            <div class="flex items-center gap-3 mb-4">
              <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                <span class="text-xl font-bold text-blue-600">大</span>
              </div>
              <div>
                <div class="font-medium">大东喔</div>
                <div class="text-sm text-gray-500">分享技术，记录生活</div>
              </div>
            </div>
            <p class="text-gray-600 text-sm leading-relaxed">
              热爱编程，专注于全栈开发。致力于分享技术知识和开发经验，欢迎交流学习。
            </p>
          </div>

          <!-- 快速导航 -->
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">快速导航</h3>
            <ul class="space-y-3">
              <li>
                <router-link to="/" class="text-gray-600 hover:text-blue-600 text-sm flex items-center">
                  <el-icon class="mr-2"><HomeFilled /></el-icon>首页
                </router-link>
              </li>
              <li>
                <router-link to="/articles" class="text-gray-600 hover:text-blue-600 text-sm flex items-center">
                  <el-icon class="mr-2"><Document /></el-icon>文章列表
                </router-link>
              </li>
              <li>
                <router-link to="/categories" class="text-gray-600 hover:text-blue-600 text-sm flex items-center">
                  <el-icon class="mr-2"><Folder /></el-icon>文章分类
                </router-link>
              </li>
              <li>
                <router-link to="/tags" class="text-gray-600 hover:text-blue-600 text-sm flex items-center">
                  <el-icon class="mr-2"><Collection /></el-icon>标签云
                </router-link>
              </li>
            </ul>
          </div>

          <!-- 技术栈 -->
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">技术栈</h3>
            <ul class="space-y-3">
              <li>
                <a 
                  href="https://cn.vuejs.org" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  class="text-gray-600 hover:text-blue-600 text-sm flex items-center"
                >
                  <el-icon class="mr-2"><ElementPlus /></el-icon>Vue.js
                </a>
              </li>
              <li>
                <a 
                  href="https://element-plus.org" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  class="text-gray-600 hover:text-blue-600 text-sm flex items-center"
                >
                  <el-icon class="mr-2"><ElementPlus /></el-icon>Element Plus
                </a>
              </li>
              <li>
                <a 
                  href="https://tailwindcss.com" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  class="text-gray-600 hover:text-blue-600 text-sm flex items-center"
                >
                  <el-icon class="mr-2"><Brush /></el-icon>TailwindCSS
                </a>
              </li>
              <li>
                <a 
                  href="https://spring.io/projects/spring-boot" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  class="text-gray-600 hover:text-blue-600 text-sm flex items-center"
                >
                  <el-icon class="mr-2"><Platform /></el-icon>Spring Boot
                </a>
              </li>
            </ul>
          </div>

          <!-- 联系方式 -->
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">联系方式</h3>
            <ul class="space-y-3">
              <li>
                <a 
                  href="mailto:<EMAIL>" 
                  class="text-gray-600 hover:text-blue-600 text-sm flex items-center"
                >
                  <el-icon class="mr-2"><Message /></el-icon>
                  <EMAIL>
                </a>
              </li>
              <li class="flex items-center text-gray-600 text-sm">
                <el-icon class="mr-2"><ChatDotRound /></el-icon>
                QQ: 2480097631
              </li>
              <li>
                <a 
                  href="https://github.com/dadongwo" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  class="text-gray-600 hover:text-blue-600 text-sm flex items-center"
                >
                  <el-icon class="mr-2"><Platform /></el-icon>
                  GitHub
                </a>
              </li>
              <li class="flex items-center text-gray-600 text-sm">
                <el-icon class="mr-2"><Location /></el-icon>
                中国，北京市
              </li>
            </ul>
          </div>
        </div>
        
        <!-- 版权信息 -->
        <div class="mt-8 pt-8 border-t border-gray-200">
          <div class="text-center text-gray-500 text-sm">
            <p>© {{ new Date().getFullYear() }} 大东喔的个人博客. All rights reserved.</p>
            <p class="mt-2">
              <a 
                href="https://beian.miit.gov.cn" 
                target="_blank" 
                rel="noopener noreferrer"
                class="hover:text-blue-600"
              >
                京ICP备XXXXXXXX号-1
              </a>
            </p>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { Platform, Message, ChatDotRound, Location, HomeFilled, Document, Folder, Collection, ElementPlus, Brush, Search, Moon, Sunny, User, Star, Setting, SwitchButton, UserFilled } from '@element-plus/icons-vue'
import { useSearchStore } from '@/stores/search'
import { ElMessage } from 'element-plus'
import { useThemeStore } from '@/stores/theme'
import { getAvatarUrl, useImageFallback, clearImageFailedCache, getRefreshedImageUrl } from '@/utils/image'
import { useNotificationStore } from '@/stores/notification'
import { useSettingsStore } from '@/stores/settings'

const route = useRoute()
const userStore = useUserStore()
const isMobileMenuOpen = ref(false)
const isSearchFocused = ref(false)
const searchStore = useSearchStore()
const router = useRouter()
const showSearchPanel = ref(false)
const themeStore = useThemeStore()
const notificationStore = useNotificationStore()
const settingsStore = useSettingsStore()

const { imgError } = useImageFallback()

// 导航菜单项
const navItems = [
  { name: '首页', path: '/' },
  { name: '文章', path: '/articles' },
  { name: '标签', path: '/tags' },
  { name: '分类', path: '/categories' },
  { name: '归档', path: '/archive' }
]

// 友情链接
const friendlyLinks = [
  { name: 'Vue.js', url: 'https://vuejs.org' },
  { name: 'Element Plus', url: 'https://element-plus.org' },
  { name: 'TailwindCSS', url: 'https://tailwindcss.com' },
  { name: 'TypeScript', url: 'https://www.typescriptlang.org' }
]

// 处理退出登录
const handleLogout = async () => {
  try {
    notificationStore.closeWebSocket()
    // 调用 logout 方法
    userStore.logout()
    ElMessage.success('退出登录成功')
    
    // 直接跳转到登录页，并记录当前页面路径
    const currentPath = route.path
    if (!currentPath.startsWith('/auth/')) {
      router.push({
        path: '/auth/login',
        query: { redirect: currentPath }
      })
    } else {
      router.push('/auth/login')
    }
  } catch (error) {
    ElMessage.error('退出登录失败')
  }
}

// 处理搜索建议
const handleSearch = async (keyword: string, cb?: Function) => {
  // 如果是回车搜索，直接跳转到搜索页面
  if (!cb) {
    if (!searchStore.keyword) return
    
    const trimmedKeyword = searchStore.keyword.trim()
    if (trimmedKeyword) {
      searchStore.addHistory(trimmedKeyword)
      router.push(`/search?keyword=${encodeURIComponent(trimmedKeyword)}`)
    }
    return
  }

  // 如果是获取搜索建议
  if (!keyword || keyword.length < 1) {
    cb([])
    return
  }
  try {
    const { data } = await searchStore.getSuggestions(keyword)
    // 确保每个建议项都有 title 属性
    const suggestions = data.map(item => ({
      ...item,
      title: item.title || item.name, // 使用 title 或 name 作为显示文本
      value: item.title || item.name  // 使用 title 或 name 作为值
    }))
    cb(suggestions)
  } catch (error) {
    console.error('获取搜索建议失败:', error)
    cb([])
  }
}

// 处理选择搜索建议
const handleSelect = (item: any) => {
  if (!item || (!item.title && !item.name)) return
  
  const keyword = item.title || item.name
  searchStore.addHistory(keyword)
  
  // 根据类型跳转到不同页面
  switch (item.type) {
    case 'article':
      router.push(`/articles/${item.id}`)
      break
    case 'tag':
      router.push(`/tags/${item.id}`)
      break
    case 'category':
      router.push(`/categories/${item.id}`)
      break
    default:
      router.push(`/search?keyword=${encodeURIComponent(keyword)}`)
  }
}

// 处理搜索框失焦
const handleSearchBlur = () => {
  setTimeout(() => {
    showSearchPanel.value = false
  }, 200)
}

// 处理历史记录点击
const handleHistoryClick = (keyword: string) => {
  if (!keyword) return
  
  searchStore.keyword = keyword
  searchStore.addHistory(keyword)
  router.push(`/search?keyword=${encodeURIComponent(keyword)}`)
}

// 高亮关键词
const highlightKeyword = (text: string, keyword: string) => {
  if (!keyword || !text) return text
  // 先转义文本中的HTML特殊字符
  const escapeHtml = (str: string) => {
    return str
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;')
  }
  
  const escapedText = escapeHtml(text)
  const escapedKeyword = escapeHtml(keyword)
  const reg = new RegExp(escapedKeyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi')
  return escapedText.replace(reg, match => `<span class="text-blue-600">${match}</span>`)
}

// 处理主题切换
const handleThemeClick = async () => {
  const newTheme = settingsStore.settings.theme === 'dark' ? 'light' : 'dark'
  await settingsStore.updateSettings({ theme: newTheme })
}

// 处理字体大小切换
const handleFontSizeChange = (size: string) => {
  document.documentElement.setAttribute('data-font-size', size)
  localStorage.setItem('fontSize', size)
}

// 处理通知图标点击
const handleNotificationClick = () => {
  // 点击通知图标时刷新未读消息数量
  notificationStore.refreshUnreadCount()
}

// 监听路由变化
watch(() => route.path, (newPath) => {
  // 当进入消息页面时，刷新未读消息数量
  if (newPath === '/user/messages') {
    notificationStore.refreshUnreadCount()
  }
})

// 初始化时刷新未读消息数量和加载热门搜索
onMounted(async () => {
  if (userStore.isLoggedIn) {
    await settingsStore.loadSettings()
    notificationStore.refreshUnreadCount()
  }
  // 加载热门搜索
  await searchStore.loadHotSearches()
})

// 监听用户登录状态
watch(() => userStore.isAuthenticated, async (isAuthenticated) => {
  if (isAuthenticated) {
    // 登录成功后，确保设置已加载
    await settingsStore.loadSettings()
    // 刷新未读消息数量
    notificationStore.refreshUnreadCount()
  }
}, { immediate: true })

// 监听用户信息变化
watch(() => userStore.userInfo?.avatar, (newAvatar, oldAvatar) => {
  if (newAvatar && newAvatar !== oldAvatar) {
    // 清除旧头像的失败缓存
    clearImageFailedCache(oldAvatar)
    // 强制重新渲染头像
    nextTick(() => {
      // 这里可以添加其他需要在头像更新后执行的逻辑
    })
  }
}, { immediate: true })

// 添加处理热门搜索点击的方法
const handleHotSearchClick = (keyword: string) => {
  searchStore.keyword = keyword
  searchStore.addHistory(keyword)
  router.push(`/search?keyword=${encodeURIComponent(keyword)}`)
  showSearchPanel.value = false
}
</script>

<style lang="scss" scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 确保移动端菜单平滑过渡 */
.md\:hidden {
  transition: all 0.3s ease-in-out;
}

/* 自定义下拉菜单样式 */
:deep(.el-dropdown-menu) {
  --el-dropdown-menuItem-hover-fill: theme('colors.gray.100');
  --el-dropdown-menuItem-hover-color: theme('colors.blue.600');
}

/* 搜索框动画 */
input {
  transition: all 0.3s ease;
}

input:focus {
  width: 300px;
}

.search-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 4px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  z-index: 50;
}

.suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

:deep(.el-input__wrapper) {
  background-color: var(--el-fill-color-blank);
  box-shadow: 0 0 0 1px var(--el-border-color) inset;
  border-radius: 9999px;
  padding-left: 12px;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

:deep(.el-input__inner) {
  height: 36px;
  font-size: 14px;
}

:deep(.el-autocomplete-suggestion) {
  border-radius: 8px;
}

/* 热门搜索标签样式 */
:deep(.el-tag) {
  margin: 4px;
  transition: all 0.3s ease;
}

:deep(.el-tag:hover) {
  transform: scale(1.05);
  cursor: pointer;
}

/* 热门搜索排名样式 */
.hot-rank {
  font-size: 12px;
  margin-right: 4px;
  font-weight: bold;
}

.hot-rank-1 { color: #f56c6c; }
.hot-rank-2 { color: #e6a23c; }
.hot-rank-3 { color: #67c23a; }

/* 搜索面板样式优化 */
.search-panel {
  min-width: 300px;
  max-height: 400px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
}

.search-panel::-webkit-scrollbar {
  width: 6px;
}

.search-panel::-webkit-scrollbar-track {
  background: transparent;
}

.search-panel::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

/* 主题按钮样式 */
.theme-button {
  margin-left: 8px;
  background: transparent;
  border: none;
  color: var(--text-secondary);
}

.theme-button:hover {
  color: var(--primary-color);
  background: var(--bg-secondary);
}

/* 主题设置弹出框样式 */
:deep(.theme-popover) {
  padding: 0;
  border-radius: 8px;
}

:deep(.el-radio-group) {
  display: flex;
  width: 100%;
}

:deep(.el-radio-button) {
  flex: 1;
}

:deep(.el-radio-button__inner) {
  width: 100%;
}

.user-dropdown-menu {
  :deep(.el-dropdown-menu__item) {
    padding: 0;
    
    a, button {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      width: 100%;
      color: inherit;
      text-decoration: none;
      
      &:hover {
        background-color: var(--el-dropdown-menuItem-hover-fill);
        color: var(--el-dropdown-menuItem-hover-color);
      }
    }
  }
}
</style> 