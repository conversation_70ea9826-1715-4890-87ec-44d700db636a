package com.modelbolt.blog.exception.business;

/**
 * 文章操作异常类
 * 用于处理文章的创建、更新、删除等操作过程中的异常
 */
public class ArticleOperationException extends BusinessException {
    
    private ArticleOperationException(int code, String message) {
        super(code, message);
    }
    
    public static class NoPermission extends ArticleOperationException {
        public NoPermission() {
            super(403, "无权操作此文章");
        }
    }
    
    public static class CreateFailed extends ArticleOperationException {
        public CreateFailed(String message) {
            super(500, "创建文章失败: " + message);
        }
    }
    
    public static class UpdateFailed extends ArticleOperationException {
        public UpdateFailed(String message) {
            super(500, "更新文章失败: " + message);
        }
    }
    
    public static class DeleteFailed extends ArticleOperationException {
        public DeleteFailed(String message) {
            super(500, "删除文章失败: " + message);
        }
    }

    public static class InvalidStatus extends ArticleOperationException {
        public InvalidStatus(String message) {
            super(400, message);
        }
    }
} 