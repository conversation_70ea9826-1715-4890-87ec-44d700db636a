<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 分类列表 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
      <h1 class="text-2xl font-bold mb-8 text-gray-900 dark:text-gray-100">
        文章分类
        <span class="text-sm font-normal text-gray-500 dark:text-gray-400 ml-2">
          共 {{ categories.length }} 个分类
        </span>
      </h1>
      
      <!-- 分类搜索 -->
      <div class="mb-8">
        <el-input
          v-model="searchQuery"
          placeholder="搜索分类..."
          class="max-w-md transition-all duration-300 hover:shadow-md"
          clearable
        >
          <template #prefix>
            <el-icon class="text-gray-400"><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 分类网格 -->
      <div v-loading="loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <router-link
          v-for="category in filteredCategories"
          :key="category.id"
          :to="'/categories/' + category.id"
          class="block group"
        >
          <div 
            class="relative bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 
                   rounded-lg p-6 transition-all duration-300 
                   hover:shadow-lg hover:scale-102 hover:bg-blue-50 dark:hover:bg-blue-900
                   border border-gray-100 dark:border-gray-700 hover:border-blue-200 dark:hover:border-blue-700"
          >
            <!-- 分类图标 -->
            <div class="absolute top-4 right-4 opacity-10 group-hover:opacity-20 transition-opacity">
              <el-icon :size="40"><Folder /></el-icon>
            </div>

            <!-- 分类信息 -->
            <div class="relative">
              <h3 class="text-xl font-bold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 mb-2 transition-colors">
                {{ category.name }}
              </h3>
              
              <!-- 父分类信息 -->
              <div v-if="category.parentId" class="text-sm text-gray-500 dark:text-gray-400 mb-3 flex items-center">
                <el-icon class="mr-1"><FolderOpened /></el-icon>
                <span>所属: {{ getCategoryName(category.parentId) }}</span>
              </div>

              <!-- 文章统计 -->
              <div class="flex items-center space-x-4">
                <div class="flex items-center text-blue-600 dark:text-blue-400">
                  <el-icon class="mr-1"><Document /></el-icon>
                  <span class="font-medium">{{ category.articleCount }}</span>
                  <span class="text-sm text-gray-500 dark:text-gray-400 ml-1">篇文章</span>
                </div>
                
                <div v-if="getChildCategories(category.id).length > 0" class="flex items-center text-green-600 dark:text-green-400">
                  <el-icon class="mr-1"><FolderAdd /></el-icon>
                  <span class="font-medium">{{ getChildCategories(category.id).length }}</span>
                  <span class="text-sm text-gray-500 dark:text-gray-400 ml-1">个子分类</span>
                </div>
              </div>

              <!-- 子分类标签 -->
              <div v-if="getChildCategories(category.id).length > 0" class="mt-4 flex flex-wrap gap-2">
                <span 
                  v-for="child in getChildCategories(category.id)" 
                  :key="child.id"
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs
                        bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300
                        group-hover:bg-blue-100 group-hover:text-blue-600
                        dark:group-hover:bg-blue-800 dark:group-hover:text-blue-300
                        transition-colors duration-200"
                >
                  {{ child.name }}
                  <span class="ml-1 text-gray-400">({{ child.articleCount }})</span>
                </span>
              </div>
            </div>
          </div>
        </router-link>
      </div>

      <!-- 无数据提示 -->
      <el-empty 
        v-if="!loading && filteredCategories.length === 0" 
        description="暂无分类数据"
        class="mt-8" 
      />
    </div>

    <!-- 最新文章 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mt-8">
      <h2 class="text-xl font-bold mb-6 text-gray-900 dark:text-gray-100 flex items-center">
        <el-icon class="mr-2"><Timer /></el-icon>
        最新文章
      </h2>
      <div v-loading="articlesLoading" class="space-y-6">
        <router-link
          v-for="article in latestArticles"
          :key="article.id"
          :to="'/articles/' + article.id"
          class="block group"
        >
          <div class="flex gap-6 p-4 rounded-lg transition-all duration-300
                      hover:bg-gray-50 dark:hover:bg-gray-700">
            <div class="w-48 h-32 rounded-lg overflow-hidden flex-shrink-0">
              <img
                :src="getCoverUrl(article.cover)"
                :alt="article.title"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              >
            </div>
            <div class="flex-1">
              <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100
                         group-hover:text-blue-600 dark:group-hover:text-blue-400
                         transition-colors duration-300 mb-2">
                {{ article.title }}
              </h3>
              <p class="text-gray-600 dark:text-gray-400 text-sm line-clamp-2 mb-4">
                {{ article.summary }}
              </p>
              <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 space-x-4">
                <span class="flex items-center">
                  <el-icon class="mr-1"><Timer /></el-icon>
                  {{ formatDate(article.createdAt) }}
                </span>
                <span class="flex items-center">
                  <el-icon class="mr-1"><View /></el-icon>
                  {{ article.viewCount }}
                </span>
                <span class="flex items-center">
                  <el-icon class="mr-1"><ChatDotRound /></el-icon>
                  {{ article.commentCount }}
                </span>
              </div>
            </div>
          </div>
        </router-link>

        <!-- 无数据提示 -->
        <el-empty 
          v-if="!articlesLoading && latestArticles.length === 0" 
          description="暂无文章数据"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Search, Timer, View, ChatDotRound, Folder, FolderOpened, FolderAdd, Document } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/date'
import type { CategoryVO, ArticleVO } from '@/types/article'
import { getAllCategories } from '@/api/category'
import { getArticles } from '@/api/article'
import { ElMessage } from 'element-plus'
import { getCoverUrl } from '@/utils/image'

// 搜索关键词
const searchQuery = ref('')

// 加载状态
const loading = ref(false)
const articlesLoading = ref(false)

// 分类数据
const categories = ref<CategoryVO[]>([])

// 过滤分类
const filteredCategories = computed(() => {
  if (!searchQuery.value) return categories.value
  const query = searchQuery.value.toLowerCase()
  return categories.value.filter(category => 
    category.name.toLowerCase().includes(query)
  )
})

// 获取分类名称
const getCategoryName = (id: number) => {
  const category = categories.value.find(c => c.id === id)
  return category ? category.name : ''
}

// 获取子分类
const getChildCategories = (parentId: number) => {
  return categories.value.filter(c => c.parentId === parentId)
}

// 最新文章数据
const latestArticles = ref<ArticleVO[]>([])

// 获取分类数据
const fetchCategories = async () => {
  loading.value = true
  try {
    const { data } = await getAllCategories()
    categories.value = data
  } catch (error) {
    ElMessage.error('获取分类数据失败')
    console.error('获取分类数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取最新文章
const fetchLatestArticles = async () => {
  articlesLoading.value = true
  try {
    const { data } = await getArticles({
      page: 1,
      size: 3,
      sort: 'newest'
    })
    latestArticles.value = data.records
  } catch (error) {
    ElMessage.error('获取最新文章失败')
    console.error('获取最新文章失败:', error)
  } finally {
    articlesLoading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchCategories()
  fetchLatestArticles()
})
</script>

<style scoped>
.scale-102 {
  transform: scale(1.02);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 添加暗色模式支持 */
:deep(.dark) {
  .el-input__inner {
    background-color: #1f2937;
    border-color: #374151;
    color: #e5e7eb;
  }
  
  .el-input__inner:focus {
    border-color: #60a5fa;
  }
}

/* 添加渐变动画 */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.bg-gradient-animate {
  background-size: 200% 200%;
  animation: gradient 15s ease infinite;
}
</style> 