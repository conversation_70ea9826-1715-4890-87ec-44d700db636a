package com.modelbolt.blog.interceptor;

import com.modelbolt.blog.utils.RedisUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

@Component
public class WebsiteVisitInterceptor implements HandlerInterceptor {
    
    private static final String TODAY_VISITS_KEY = "stats:today_visits";
    
    @Autowired
    private RedisUtils redisUtils;
    
    private static final Logger log = LoggerFactory.getLogger(WebsiteVisitInterceptor.class);
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 添加请求日志
        log.info("Intercepted request - Method: {}, URI: {}", request.getMethod(), request.getRequestURI());
        
        // 只统计GET请求
        if (request.getMethod().equals("GET")) {
            String uri = request.getRequestURI();
            log.info("Processing GET request: {}", uri);
            
            // 统计前端的实际业务API访问（排除静态资源和其他API）
            if (uri.contains("/api/articles") || 
                uri.contains("/api/categories") || 
                uri.contains("/api/tags") ||
                uri.contains("/api/search")) {
                    
                String key = TODAY_VISITS_KEY;
                // 增加访问计数并返回新值
                Long newCount = redisUtils.increment(key, 1);
                // 设置过期时间为48小时
                redisUtils.expire(key, 48, TimeUnit.HOURS);
                log.info("Visit counted - URI: {}, New count: {}", uri, newCount);
            } else {
                log.debug("Skipped counting for non-business API: {}", uri);
            }
        }
        return true;
    }
} 