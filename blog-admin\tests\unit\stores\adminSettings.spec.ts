import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useAdminSettingsStore } from '../../../src/stores/adminSettings'

// 模拟API模块
vi.mock('../../../src/api/adminSettings', () => ({
  getSettings: vi.fn(),
  updateSettings: vi.fn()
}))

// 导入模拟的API模块
import * as adminSettingsApi from '../../../src/api/adminSettings'

describe('管理员设置存储模块测试', () => {
  beforeEach(() => {
    // 创建新的pinia实例并激活
    setActivePinia(createPinia())
    
    // 重置所有模拟
    vi.resetAllMocks()
  })
  
  describe('loadSettings', () => {
    it('成功加载设置时应更新状态', async () => {
      // 模拟API响应
      const mockSettings = {
        emailVerificationEnabled: true
      }
      
      vi.mocked(adminSettingsApi.getSettings).mockResolvedValue({
        code: 200,
        message: '操作成功',
        data: mockSettings
      })
      
      // 获取store实例
      const store = useAdminSettingsStore()
      
      // 调用loadSettings方法
      const result = await store.loadSettings()
      
      // 验证结果
      expect(result).toBe(true)
      expect(store.settings).toEqual(mockSettings)
      expect(adminSettingsApi.getSettings).toHaveBeenCalledTimes(1)
    })
    
    it('加载设置失败时应返回false', async () => {
      // 模拟API错误响应
      vi.mocked(adminSettingsApi.getSettings).mockResolvedValue({
        code: 500,
        message: '服务器错误',
        data: null
      })
      
      // 获取store实例
      const store = useAdminSettingsStore()
      
      // 调用loadSettings方法
      const result = await store.loadSettings()
      
      // 验证结果
      expect(result).toBe(false)
      expect(adminSettingsApi.getSettings).toHaveBeenCalledTimes(1)
    })
    
    it('API抛出异常时应捕获并返回false', async () => {
      // 模拟API抛出异常
      vi.mocked(adminSettingsApi.getSettings).mockRejectedValue(new Error('网络错误'))
      
      // 获取store实例
      const store = useAdminSettingsStore()
      
      // 调用loadSettings方法
      const result = await store.loadSettings()
      
      // 验证结果
      expect(result).toBe(false)
      expect(adminSettingsApi.getSettings).toHaveBeenCalledTimes(1)
    })
  })
  
  describe('updateSettings', () => {
    it('成功更新设置时应合并新旧设置', async () => {
      // 模拟API响应
      vi.mocked(adminSettingsApi.updateSettings).mockResolvedValue({
        code: 200,
        message: '操作成功',
        data: null
      })
      
      // 获取store实例
      const store = useAdminSettingsStore()
      
      // 设置初始状态
      store.settings = {
        emailVerificationEnabled: true
      }
      
      // 准备更新数据
      const newSettings = {
        emailVerificationEnabled: false
      }
      
      // 调用updateSettings方法
      const result = await store.updateSettings(newSettings)
      
      // 验证结果
      expect(result).toBe(true)
      expect(store.settings).toEqual(newSettings)
      expect(adminSettingsApi.updateSettings).toHaveBeenCalledWith(newSettings)
    })
    
    it('更新设置失败时应返回false且不更新状态', async () => {
      // 模拟API错误响应
      vi.mocked(adminSettingsApi.updateSettings).mockResolvedValue({
        code: 500,
        message: '服务器错误',
        data: null
      })
      
      // 获取store实例
      const store = useAdminSettingsStore()
      
      // 设置初始状态
      const initialSettings = {
        emailVerificationEnabled: true
      }
      store.settings = { ...initialSettings }
      
      // 准备更新数据
      const newSettings = {
        emailVerificationEnabled: false
      }
      
      // 调用updateSettings方法
      const result = await store.updateSettings(newSettings)
      
      // 验证结果
      expect(result).toBe(false)
      expect(store.settings).toEqual(initialSettings) // 状态不应改变
      expect(adminSettingsApi.updateSettings).toHaveBeenCalledWith(newSettings)
    })
  })
}) 