<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import type { LoginParams } from '@/types/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const loginForm = reactive<LoginParams>({
  username: '',
  password: '',
  rememberMe: false
})

const loginFormRef = ref<FormInstance>()

const rules = reactive<FormRules>({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ]
})

const loading = ref(false)

const handleLogin = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  
  await formEl.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const success = await userStore.login(loginForm)
        if (success) {
          // 登录成功提示
          ElMessage.success('登录成功')
          
          // 登录成功后不再强制刷新页面，而是让路由跳转自然完成
          // 登录后的路由跳转已经在 userStore.login 中处理
        }
      } catch (error: any) {
        console.error('Login error:', error)
        ElMessage.error(error.response?.data?.message || error.message || '登录失败')
      } finally {
        loading.value = false
      }
    }
  })
}

const goToRegister = () => {
  router.push('/auth/register')
}

const goToForgotPassword = () => {
  router.push('/auth/forgot-password')
}
</script>

<template>
  <div class="auth-page">
    <div class="auth-container">
      <div class="auth-card">
        <div class="auth-header">
          <img src="@/assets/logo.png" alt="Logo" class="auth-logo">
          <h2 class="auth-title">登录账号</h2>
          <p class="auth-subtitle">欢迎回来，请登录您的账号</p>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="rules"
          class="auth-form"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="用户名"
              :prefix-icon="User"
              class="auth-input"
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              :prefix-icon="Lock"
              class="auth-input"
              @keyup.enter="handleLogin(loginFormRef)"
            />
          </el-form-item>

          <div class="form-footer">
            <el-checkbox v-model="loginForm.rememberMe">记住我</el-checkbox>
            <router-link to="/auth/forgot-password" class="forgot-link">
              忘记密码？
            </router-link>
          </div>

          <el-button
            type="primary"
            class="login-button"
            :loading="loading"
            @click="handleLogin(loginFormRef)"
          >
            登录
          </el-button>

          <div class="register-link">
            <span>还没有账号？</span>
            <router-link to="/auth/register">
              立即注册
            </router-link>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f6f8ff 0%, #f0f4ff 100%);
  padding: 20px;
}

.auth-container {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
}

.auth-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  padding: 32px;
}

.auth-header {
  text-align: center;
  margin-bottom: 32px;
}

.auth-logo {
  width: 64px;
  height: 64px;
  margin-bottom: 24px;
}

.auth-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.auth-subtitle {
  font-size: 14px;
  color: #666;
}

.auth-form {
  .el-form-item {
    margin-bottom: 24px;
  }
}

.auth-input {
  :deep(.el-input__wrapper) {
    border-radius: 8px;
    height: 44px;
    
    &.is-focus {
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }
  }
}

.form-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.forgot-link {
  font-size: 14px;
  color: var(--primary-color);
  text-decoration: none;
  
  &:hover {
    color: var(--primary-color-hover);
  }
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  margin-bottom: 24px;
  
  &:hover {
    transform: translateY(-1px);
  }
}

.register-link {
  text-align: center;
  font-size: 14px;
  
  span {
    color: #666;
  }
  
  a {
    color: var(--primary-color);
    text-decoration: none;
    margin-left: 4px;
    
    &:hover {
      color: var(--primary-color-hover);
    }
  }
}
</style> 