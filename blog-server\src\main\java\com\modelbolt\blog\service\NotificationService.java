package com.modelbolt.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.modelbolt.blog.common.PageResult;
import com.modelbolt.blog.model.entity.Notification;
import com.modelbolt.blog.model.entity.NotificationType;
import com.modelbolt.blog.model.entity.NotificationTargetType;
import com.modelbolt.blog.model.vo.NotificationVO;

public interface NotificationService extends IService<Notification> {
    
    /**
     * 创建通知
     *
     * @param userId 接收通知的用户ID
     * @param senderId 发送通知的用户ID
     * @param type 通知类型
     * @param targetId 目标ID
     * @param targetType 目标类型
     * @param content 通知内容
     */
    void createNotification(Long userId, Long senderId, NotificationType type, 
                          Long targetId, NotificationTargetType targetType, String content);

    /**
     * 获取用户通知列表
     *
     * @param userId 用户ID
     * @param type 通知类型
     * @param page 页码
     * @param size 每页大小
     * @return 通知列表
     */
    PageResult<NotificationVO> getUserNotifications(Long userId, String type, Integer page, Integer size);
    
    /**
     * 标记通知为已读
     *
     * @param userId 用户ID
     * @param notificationId 通知ID
     * @return 是否成功
     */
    boolean markAsRead(Long userId, Long notificationId);
    
    /**
     * 标记所有通知为已读
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean markAllAsRead(Long userId);
    
    /**
     * 删除通知
     *
     * @param userId 用户ID
     * @param notificationId 通知ID
     * @return 是否成功
     */
    boolean deleteNotification(Long userId, Long notificationId);
    
    /**
     * 获取未读通知数量
     *
     * @param userId 用户ID
     * @return 未读数量
     */
    int getUnreadCount(Long userId);
} 