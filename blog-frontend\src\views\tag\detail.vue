<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 标签信息 -->
    <div v-loading="loading" class="bg-white rounded-lg shadow-sm p-8 mb-8">
      <template v-if="tag">
        <div class="flex items-center justify-between mb-6">
          <h1 class="text-2xl font-bold">
            <el-tag :type="getRandomType()" size="large" class="mr-3">
              {{ tag.name }}
            </el-tag>
            相关文章
          </h1>
          <el-tag type="primary" size="large">
            {{ tag.articleCount }} 篇文章
          </el-tag>
        </div>
      </template>
    </div>

    <!-- 文章列表 -->
    <div v-loading="articlesLoading" class="space-y-6">
      <router-link
        v-for="article in articles"
        :key="article.id"
        :to="'/articles/' + article.id"
        class="block group"
      >
        <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow duration-300">
          <div class="flex gap-6">
            <div class="w-48 h-32 rounded-lg overflow-hidden flex-shrink-0">
              <img
                :src="getCoverUrl(article.cover)"
                :alt="article.title"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              >
            </div>
            <div class="flex-1">
              <h3 class="text-lg font-medium group-hover:text-blue-600 mb-2">
                {{ article.title }}
              </h3>
              <p class="text-gray-600 text-sm line-clamp-2 mb-4">
                {{ article.summary }}
              </p>
              <div class="flex items-center text-sm text-gray-500 space-x-4">
                <span class="flex items-center">
                  <el-icon class="mr-1"><Timer /></el-icon>
                  {{ formatDate(article.createdAt) }}
                </span>
                <span class="flex items-center">
                  <el-icon class="mr-1"><View /></el-icon>
                  {{ article.viewCount }}
                </span>
                <span class="flex items-center">
                  <el-icon class="mr-1"><ChatDotRound /></el-icon>
                  {{ article.commentCount }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </router-link>

      <!-- 无数据提示 -->
      <el-empty v-if="!articlesLoading && articles.length === 0" description="暂无文章数据" />

      <!-- 分页 -->
      <div v-if="total > 0" class="flex justify-center mt-8">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { Timer, View, ChatDotRound } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/date'
import type { TagVO, ArticleVO } from '@/types/article'
import { getTagById, getArticlesByTagId } from '@/api/tag'
import { ElMessage } from 'element-plus'
import { getCoverUrl } from '@/utils/image'

const route = useRoute()
const tagId = Number(route.params.id)

// 加载状态
const loading = ref(false)
const articlesLoading = ref(false)

// 标签数据
const tag = ref<TagVO | null>(null)

// 文章列表数据
const articles = ref<ArticleVO[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 获取标签详情
const fetchTag = async () => {
  loading.value = true
  try {
    const { data } = await getTagById(tagId)
    tag.value = data
  } catch (error) {
    ElMessage.error('获取标签详情失败')
    console.error('获取标签详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取标签文章列表
const fetchArticles = async () => {
  articlesLoading.value = true
  try {
    const { data } = await getArticlesByTagId(tagId, currentPage.value, pageSize.value)
    articles.value = data.records
    total.value = data.total
  } catch (error) {
    ElMessage.error('获取文章列表失败')
    console.error('获取文章列表失败:', error)
  } finally {
    articlesLoading.value = false
  }
}

// 处理分页大小变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  fetchArticles()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchArticles()
}

// 随机标签类型
const tagTypes = ['primary', 'success', 'warning', 'danger', 'info']
const getRandomType = () => {
  return tagTypes[Math.floor(Math.random() * tagTypes.length)]
}

// 页面加载时获取数据
onMounted(() => {
  fetchTag()
  fetchArticles()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>