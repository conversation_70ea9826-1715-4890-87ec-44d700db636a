package com.modelbolt.blog.model.dto.auth;

import com.modelbolt.blog.model.entity.User;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Schema(description = "注册响应")
public class RegisterResponse {
    
    @Schema(description = "响应消息")
    private String message;
    
    @Schema(description = "用户信息")
    private User user;
    
    @Schema(description = "访问令牌")
    private String token;
} 