package com.modelbolt.blog.model.dto.ai;

import lombok.Data;

@Data
public class ChatResponse {
    /**
     * 响应内容
     */
    private String content;

    /**
     * 内容更新
     */
    private Updates updates;

    private boolean hasChanges;

    @Data
    public static class Updates {
        /**
         * 更新后的标题
         */
        private String title;

        /**
         * 更新后的正文
         */
        private String content;

        /**
         * 更新后的摘要
         */
        private String summary;

        private String diff;
    }
} 