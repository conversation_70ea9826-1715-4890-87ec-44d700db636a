package com.modelbolt.blog.constant;

public class RedisKeyConstant {
    /**
     * 邮箱验证码前缀
     */
    public static final String EMAIL_CODE_PREFIX = "email:code:";

    /**
     * 邮箱验证码发送频率限制前缀
     */
    public static final String EMAIL_RATE_LIMIT_PREFIX = "email:rate:limit:";

    /**
     * 密码重置token前缀
     */
    public static final String PASSWORD_RESET_PREFIX = "password:reset:";

    /**
     * 管理员邮箱验证码前缀
     */
    public static final String ADMIN_EMAIL_CODE_PREFIX = "admin:email:code:";

    /**
     * 管理员邮箱验证码发送频率限制前缀
     */
    public static final String ADMIN_EMAIL_RATE_LIMIT_PREFIX = "admin:email:rate:limit:";

} 