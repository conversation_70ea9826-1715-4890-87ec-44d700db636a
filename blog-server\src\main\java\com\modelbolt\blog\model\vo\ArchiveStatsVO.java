package com.modelbolt.blog.model.vo;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ArchiveStatsVO {
    private Integer total;
    private List<YearStatVO> yearStats;
    private TimeRangeVO timeRange;

    @Data
    public static class YearStatVO {
        private String year;
        private Integer count;
    }

    @Data
    public static class TimeRangeVO {
        private String start;
        private String end;
    }
}